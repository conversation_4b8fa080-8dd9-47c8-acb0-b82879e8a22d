app.controller('ClaimCtrl', ClaimCtrl);
ClaimCtrl.$inject = ['$rootScope', '$uibModal', '$scope', '$filter', '$stateParams', '$moment', '$timeout', '$sce', '$http',
	'CacheFactory', 'PrescriptionFactory', 'BranchService', 'UtilService', 'NoteService', 'UserService',
	'InsuranceService', 'PrescriptionService', 'ClaimService', 'ClaimAdjustmentService', 'NotificationService',
	'FileService', 'PatientService', 'DateService', 'ClaimStatusJsonFactory', 'ClaimStatusFactory',
	'ClaimResubmissionCodeFactory', 'MaritalStatusFactory', 'RelationToSubscriberFactory', 'PrescriptionLCodeFactory',
	'PlacesOfServiceFactory', 'UserFactory', 'FeeScheduleFactory', 'InsuranceVerificationFactory', 'SystemSettingFactory',
	'PaymentFactory', 'ClaimFactory', 'ClaimSubmissionFactory', 'PrescriptionDiagnosisCodeFactory',
	'InsuranceVerificationLCodeFactory', 'TaskFactory', 'PaymentTypesFactory', 'AppointmentService', 'PaymentService',
	'ClaimFileFactory', 'PhoneNumberFactory', 'DownloadFactory', 'NymblStatusFactory', 'PatientFactory', 'FormTypesFactory',
	'NotificationFactory', 'UserNotificationService', 'GL_PeriodFactory', 'EvaluationFormService', 'DiffModalService',
	'FileFactory', 'toastr', 'PdfService', 'Form1500TemplateFactory', 'NymblStatusHistoryFactory'];

function ClaimCtrl($rootScope, $uibModal, $scope, $filter, $stateParams, $moment, $timeout, $sce, $http, CacheFactory,
                   PrescriptionFactory, BranchService, UtilService, NoteService, UserService, InsuranceService,
                   PrescriptionService, ClaimService, ClaimAdjustmentService, NotificationService, FileService,
                   PatientService, DateService, ClaimStatusJsonFactory, _ClaimStatusFactory, ClaimResubmissionCodeFactory,
                   MaritalStatusFactory, RelationToSubscriberFactory, PrescriptionLCodeFactory, PlacesOfServiceFactory,
                   UserFactory, FeeScheduleFactory, InsuranceVerificationFactory, SystemSettingFactory, PaymentFactory,
                   ClaimFactory, ClaimSubmissionFactory, PrescriptionDiagnosisCodeFactory,
                   InsuranceVerificationLCodeFactory, TaskFactory, PaymentTypesFactory, AppointmentService,
                   PaymentService, ClaimFileFactory, PhoneNumberFactory, DownloadFactory, NymblStatusFactory,
                   _PatientFactory, FormTypesFactory, NotificationFactory, UserNotificationService, GL_PeriodFactory,
                   EvaluationFormService, DiffModalService, FileFactory, toastr, PdfService, Form1500TemplateFactory, _NymblStatusHistoryFactory) {

	$rootScope.page = {
		title: 'Claim',
		subtitle: '#' + $stateParams.claimId.toString()
	};

	$http.get('api/hcpcs-modifier').then(function (response) {
		$scope.modifiersList = response.data;
	}, function (response) {
		console.log(response);
		$scope.modifiersList = [];
	});

	$scope.moment = $moment;
	$scope.pageCount = undefined;
	var claimId = $stateParams.claimId;
	$scope.claimId = claimId;
	ClaimFactory.get({id: claimId}).$promise.then(function (claim) {
		$scope.patientId = claim.prescription.patientId;
	});
	var timedSave = {};
	NoteService.initializeNoteCache();

	$scope.splitChecked = {};
	$scope.summernoteOptions = {
		focus: false,
		airMode: false,
		tabDisable: true,
		toolbar: [
			['edit', ['undo', 'redo']],
			['headline', ['style']],
			['style', ['bold', 'italic', 'underline', 'superscript', 'subscript', 'strikethrough', 'clear']],
			['fontface', ['fontname']],
			['textsize', ['fontsize']],
			['fontclr', ['color']],
			['alignment', ['ul', 'ol', 'paragraph', 'lineheight']],
			['height', ['height']],
			['table', ['table']],
			['insert', ['link', 'picture', 'video', 'hr']],
			['insert', ['link', 'hr']]
			// ['view', ['fullscreen', 'codeview']],
			// ['help', ['help']]
		],
		insertTableMaxSize: {
			col: 20,
			row: 20
		},
		dialogsInBody: true,
		callbacks: {
			onPaste: function (e) {
				var updatePastedText = function () {
					var replacedText = $scope.utilService.stripUnsupportedHTMLTagsSummerNote(e.currentTarget.innerHTML);
					if (replacedText) {
						e.currentTarget.innerHTML = replacedText;
					}
				};
				setTimeout(function () {
					updatePastedText();
				}, 100);
			},
			onChange: function (contents, event) {
				function clearTimedSave(rxId) {
					if (rxId in timedSave) {
						//alert("clearing for "+ rxId);
						clearTimeout(timedSave[rxId]);
					}
				}

				var id = event[0].parentElement.parentElement.parentElement.children[0].id;
				var rxId = 0;
				if (id) {
					rxId = id.split('_')[1];
				}
				if (contents !== '<p><br></p>') {
					CacheFactory.get('noteCache').put('/patient/' + $scope.patientId + '/prescription/note/' + rxId, {
						content: contents
					});
					var saveDraft = function (rxId) {
						PrescriptionFactory.get({id: rxId}).$promise.then(function (prescription) {
							NoteService.syncSaveNote(prescription, 'draft').then(function (note) {
								clearTimedSave(rxId);
								NoteService.viewNote(note);
							}).catch(function (_error) {
								//console.error(error);
							});
						});
					};

					clearTimedSave(rxId);
					timedSave[rxId] = setTimeout(saveDraft, 60000, rxId);
				}
			}
		}
	};

	/**
	 * Uploads image to files and docs.
	 * Returns image node for summernote to insert.
	 * @param files
	 * @returns void
	 */
	$scope.upload = function (files) {
		const file = files[0];
		toastr.info('Uploading image...');
		const fileReader = new FileReader();
		fileReader.readAsArrayBuffer(file);
		return (prescriptionId) => {
			fileReader.onload = function (fileIO) {
				const fileFormData = new FormData();
				fileFormData.append('file', new Blob([fileIO.target.result]), file.name);
				FileFactory.uploadImage({
					prescriptionId: prescriptionId | 0,
					patientId: $scope.patientId
				}, fileFormData).$promise.then((uniqueFileName) => {
					let stringFileName = '';
					for (const charIndex in uniqueFileName) {
						// if charindex is a number then add it to the string
						if (!isNaN(parseInt(charIndex))) {
							stringFileName += uniqueFileName[charIndex];
						}
					}
					FileFactory.save({
							type: prescriptionId ? 'prescription' : 'general'
						},
						{
							patientId: $scope.patientId,
							prescriptionId: prescriptionId | 0,
							fileName: stringFileName,
							description: 'Note Image Upload: ' + file.name,
						}).$promise.then(function (response) {
						toastr.success('Successfully uploaded ' + file.name);
						// insert image into summernote via image link insert
						const baseUrl = $('#baseUrl').attr('value');
						const imageLink = `${baseUrl}/api/file/contents/${response.id}`;
						const editorScope = angular.element(`#note_${prescriptionId ? prescriptionId : 0}`);
						editorScope.summernote('insertImage', imageLink);
					}).catch(function (error) {
						toastr.error('Error uploading ' + file.name);
						console.error(error);
					});
				});
			};
			return fileReader;
		};
	};

	$scope.form1500Templates = Form1500TemplateFactory.query();

	$scope.$on("localStorageObjectLoadComplete", function () {
		if (!PrescriptionService.loading && !PatientService.loading) {
			$scope.loadClaim(claimId);
		}
	});

	$scope.loadClaim = function (claimId) {
		$scope.currentUser = UserService.getCurrentUser();
		$scope.hasSubmittedClaimFile = false;
		$scope.branchService = BranchService;
		$scope.fileService = FileService;
		$scope.noteService = NoteService;
		$scope.claimService = ClaimService;
		$scope.userService = UserService;
		$scope.prescriptionService = PrescriptionService;
		$scope.appointmentService = AppointmentService;
		$scope.claimAdjustmentService = ClaimAdjustmentService;
		$scope.insuranceService = InsuranceService;
		$scope.utilService = UtilService;
		$scope.dateService = DateService;
		$scope.evaluationFormService = EvaluationFormService;
		$scope.paymentService = PaymentService;
		$scope.formTypes = FormTypesFactory.get();

		$scope.yesNo = [
			{value: true, name: 'Yes'},
			{value: false, name: 'No'}
		];

		$scope.claim = undefined;
		$scope.claimTotals = undefined;
		$scope.files = undefined;
		$scope.blankFiles = undefined;
		$scope.validationErrors = undefined;
		$scope.insurances = {};
		$scope.users = [];
		$scope.userReassigned = false;
		$scope.submitted = false;
		$scope.manualSubmission = false;
		$scope.autoPostClaimResponses = [];
		// $scope.patientPayments = [];
		$scope.reloadingClaimFull = false;
		$scope.reloadingClaimBlank = false;

		$scope.totalCharges = 0;
		$scope.totalAllowable = 0;
		$scope.totalSalesTax = 0;
		$scope.totalUnresolved = 0;
		$scope.coveredCount = 0;
		$scope.placeOfService = [];
		$scope.minDate = '2000-01-01';
		$scope.maxDate = $moment().format('YYYY-MM-DD');
		$scope.hasClaimFile = false;

		$scope.paymentTypes = PaymentTypesFactory.get();
		$scope.claimStatus = ClaimStatusJsonFactory.get();
		$scope.claimStatuses = NymblStatusFactory.search({type: 'claim', active: true});
		$scope.claimResubmissionCode = ClaimResubmissionCodeFactory.get();
		$scope.maritalStatus = MaritalStatusFactory.get();
		$scope.relationToSubscriber = RelationToSubscriberFactory.get();
		$scope.placesOfService = PlacesOfServiceFactory.get();
		$scope.feeSchedules = FeeScheduleFactory.query();

		$scope.billingCycleEndDate = "";
		$scope.firstSubmissionDate = undefined;
		$scope.printSalesTax = false;
		$scope.accountingLocked = true;
		$scope.DXCodeMissing = false;
		$scope.DXpointerMissing = false;
		$scope.dosPODWarning = false;
		$scope.billerCodeEnabled = false;
		$scope.insuranceBalance = undefined;
		$scope.patientBalance = undefined;
		$scope.realBalance = undefined;
		$scope.box27AcceptsAssignmentDisabled = false;

		ClaimFileFactory.findByClaimId({claimId: claimId}).$promise.then(function (response) {
			$scope.hasSubmittedClaimFile = false;
			$scope.claimFiles = response;
			angular.forEach($scope.claimFiles, function (claimFile) {
				if (claimFile.submitted) {
					$scope.hasSubmittedClaimFile = true;
				}
			});
		});

		GL_PeriodFactory.getLastClosedPeriod().$promise.then(function (response) {
			if (response.endDate) {
				$scope.billingCycleEndDate = response.endDate;
				$scope.minDate = $moment(response.endDate).add(1, "days").format("YYYY-MM-DD");
				$scope.maxDate = $moment(response.endDate).add(1, "days").format("YYYY-MM-DD");
			}
			initCalendar();
			initDateResolvedCalendar();
			initDosCalendarCalendar();
		});

		SystemSettingFactory.findBySection({section: 'claim'}).$promise.then(function (response) {
			$scope.claimDefaults = response;
		});

		$scope.users = UserFactory.getBillingUsers({companyId: UserService.getCompanyId()});

		ClaimFactory.findByIdWithForeignKeys({id: claimId}).$promise.then(function (claim) {
			if (!claim.nymblRcm) {
				claim.nymblRcm = false; // if null or undefined, default it to false
			}
			$scope.claim = claim;
			loadVerifications();
			// True Up the Real Numbers that are actually in the DB.
			ClaimFactory.getClaimTotals({
				rxId: $scope.claim.prescriptionId,
				claimId: $scope.claim.id
			}).$promise.then(function (response) {
				$scope.claimTotals = response;
				$scope.realBalance = $scope.claimTotals.totalCharge + $scope.claimTotals.insurancePaid + $scope.claimTotals.patientPaid + $scope.claimTotals.insuranceAdjustments + $scope.claimTotals.patientAdjustments;
				if ($scope.claim.totalPtResponsibilityBalance !== 0) {
					$scope.claim.totalClaimBalance = $scope.realBalance - $scope.claim.totalPtResponsibilityBalance;
				} else {
					$scope.claim.totalClaimBalance = $scope.realBalance;
					$scope.claim.totalPtResponsibilityBalance = 0;
				}

				// Set Base Claim Numbers trued up. This will not change anything unless they update claim.
				// This is always the true total charge
				$scope.claim.totalClaimAmount = $scope.claimTotals.totalCharge;
				// These are basically actually what has been paid.
				$scope.claim.totalClaimPaid = $scope.claimTotals.insurancePaid * -1;
				$scope.claim.totalPtResponsibilityPaid = $scope.claimTotals.patientPaid * -1;

				$scope.claim.totalClaimBalance = Math.round($scope.claim.totalClaimBalance * 100) / 100;
				$scope.claim.totalPtResponsibilityBalance = Math.round($scope.claim.totalPtResponsibilityBalance * 100) / 100;
				$scope.claim_level_1500_overrides = UserService.features.claim_level_1500_overrides;
			});
			//End of true up from above.
			$scope.patientId = $scope.claim.prescription.patientId;
			if ($scope.claim.prescription.patient.primaryBranchId === undefined) {
				PatientService.openPickPatientPrimaryBranch($scope.patientId);
			}

			if ($scope.claim.prescription.manualPodSignedDate && $scope.claim.prescription.manualPodSignedDate !== $scope.claim.dateOfService) {
				$scope.dosPODWarning = true;
			} else if ($scope.claim.prescription.signedDate && $scope.claim.prescription.signedDate !== $scope.claim.dateOfService) {
				$scope.dosPODWarning = true;
			}

			var payerId;
			if (!!$scope.claim.responsiblePatientInsurance.insuranceCompany.clearingHousePayer) {
				payerId = $scope.claim.responsiblePatientInsurance.insuranceCompany.clearingHousePayer.payerId;
			}
			$scope.responsiblePatientInsuranceCompanyName = $scope.claim.responsiblePatientInsurance.insuranceCompany.name + (!!payerId ? ' - ' + payerId : '');
			SystemSettingFactory.findBySection({section: 'billing'}).$promise.then(function (response) {
				if (response.display_biller_code_field === "Y") {
					$scope.billerCodeEnabled = true;
					$scope.responsiblePayerWithBillerCode = $scope.responsiblePatientInsuranceCompanyName + ' (' + claim.responsiblePatientInsurance.insuranceCompany.billerCode + ')';
				}
			});

			$scope.printView = {printViewPatientInsuranceId: null};
			$scope.printView.printViewPatientInsuranceId = $scope.claim.responsiblePatientInsuranceId;
			if ($scope.claim.responsiblePatientInsuranceId === $scope.claim.otherPatientInsuranceId) {
				$scope.printViewOtherPatientInsuranceId = $scope.claim.patientInsuranceId;
			} else {
				$scope.printViewOtherPatientInsuranceId = $scope.claim.otherPatientInsuranceId;
			}

			if ($scope.claim.responsiblePatientInsurance &&
				$scope.claim.responsiblePatientInsurance.insuranceCompany &&
				$scope.claim.responsiblePatientInsurance.insuranceCompany.form1500TemplateId) {
				$scope.form1500TemplateId = $scope.claim.responsiblePatientInsurance.insuranceCompany.form1500TemplateId;
			} else {
				$scope.form1500TemplateId = null;
			}
			$scope.updateBox27AcceptsAssignment();
			PatientService.init();
			PrescriptionService.init();
			NoteService.init();
			AppointmentService.init();
			FileService.init();
			PrescriptionService.profileLoad($scope.patientId);
			PatientService.profileLoad($scope.patientId);
			AppointmentService.profileLoad($scope.patientId);
			// $scope.reloadPDFs(true, $scope.printViewPatientInsuranceId, $scope.printViewOtherPatientInsuranceId);
			$scope.manualClaimSubmission = {
				claimId: $scope.claim.id,
				patientInsuranceId: $scope.claim.responsiblePatientInsuranceId,
				submittedById: $scope.currentUser.id
			};
			var claimId = $scope.claim.id;
			initFollowUpTaskEntry();
			loadNextPayer();
			UserFactory.get({id: $scope.claim.prescription.treatingPractitionerId}).$promise.then(function (response) {
				$scope.claim.prescription.treatingPractitioner = response;
			});
			TaskFactory.findByClaimIdAndType({claimId: claim.id, type: 'follow_up'}).$promise.then(function (response) {
				$scope.followUpTasks = response;
				// $('#taskUserId').prop('required', true).trigger("chosen:updated");
			});
			$scope.prescriptionDiagnosisCodes = {};
			PrescriptionDiagnosisCodeFactory.findByPrescriptionId({prescriptionId: $scope.claim.prescriptionId}).$promise.then(function (response) {
				$scope.prescriptionDiagnosisCodes = response;
			});
			if ($scope.claim.prescription.referringPhysicianId) {
				PhoneNumberFactory.getPhysicianPhoneNumber({personId: $scope.claim.prescription.referringPhysicianId}).$promise.then(function (phoneNumbers) {
					angular.forEach(phoneNumbers, function (phoneNumber, _index) {
						if ('Work Phone' === phoneNumber.type) {
							$scope.claim.prescription.referringPhysician.phoneNumber = phoneNumber.phoneNumber;
						}
					});
				});
			}
			if ($scope.claim.prescription.primaryCarePhysicianId) {
				PhoneNumberFactory.getPhysicianPhoneNumber({personId: $scope.claim.prescription.primaryCarePhysicianId}).$promise.then(function (phoneNumbers) {
					angular.forEach(phoneNumbers, function (phoneNumber, _index) {
						if ('Work Phone' === phoneNumber.type) {
							$scope.claim.prescription.primaryCarePhysician.phoneNumber = phoneNumber.phoneNumber;
						}
					});
				});
			}
			if ($scope.claim.billingBranchId) {
				angular.forEach(BranchService.userBranches, function (branch) {
					if (branch.id === $scope.claim.billingBranchId) {
						$scope.claim.billingBranch = branch;
					}
				});
			}
			$scope.submissions = {};
			ClaimSubmissionFactory.findByClaimIdForDTO({claimId: $scope.claim.id}).$promise.then(function (response) {
				$scope.submissions = response;
				if ($scope.submissions.length > 0) {
					$scope.submitted = true;
				}
			});
			$rootScope.$broadcast('loadPatientPayments', {patientId: $scope.patientId});
			$rootScope.$broadcast('loadTransactionHistory', {claimId: claimId});

			ClaimService.profileLoad(claim.prescription.patientId);
			ClaimFactory.findAutoPostClaimResponsesById({id: claimId}).$promise.then(function (autoPostClaimResponses) {
				$scope.autoPostClaimResponses = autoPostClaimResponses;
			});

		});
		$scope.canOverrideClaimValues = UserService.hasPermission("claim_values_override");
		$scope.dateOfServiceEdit = UserService.hasPermission("date_of_service_edit");
	};

	$scope.updateBox27AcceptsAssignment = function () {
		if ($scope.form1500TemplateId !== null) {
			angular.forEach($scope.form1500Templates, function (currentForm1500Template) {
				if (currentForm1500Template.id === $scope.form1500TemplateId) {
					if (currentForm1500Template.box27AcceptAssignment && currentForm1500Template.box27AcceptAssignment === "No") {
						$scope.box27AcceptsAssignmentDisabled = true;
					}
				}
			});
		} else {
			$scope.box27AcceptsAssignmentDisabled = false;
		}
	};

	$scope.updateFormTemplateId = function () {
		if ($scope.claim_level_1500_overrides) {
			if ($scope.claim.form1500TemplateId > 0) {
				$scope.form1500TemplateId = $scope.claim.form1500TemplateId;
			} else if ($scope.claim.responsiblePatientInsurance &&
				$scope.claim.responsiblePatientInsurance.insuranceCompany &&
				$scope.claim.responsiblePatientInsurance.insuranceCompany.form1500TemplateId) {
				$scope.form1500TemplateId = $scope.claim.responsiblePatientInsurance.insuranceCompany.form1500TemplateId;
			} else {
				$scope.form1500TemplateId = null;
			}
			$scope.updateBox27AcceptsAssignment();
		}
	};

	var loadNextPayer = function () {
		$scope.nextPayer = {
			disabled: true,
			selected: null,
			list: []
		};
		InsuranceVerificationFactory.findByPrescriptionIdAndNotInsuranceCompany({
			prescriptionId: $scope.claim.prescriptionId,
			insuranceCompanyId: $scope.claim.patientInsurance.insuranceCompanyId
		}).$promise.then(function (response) {
			$scope.nextPayer.disabled = false;
			angular.forEach(response, function (entry, index) {
				if (!$scope.claimDefaults.use_single_claim && entry.patientInsuranceId === $scope.claim.patientInsuranceId)
					response.splice(index, 1);
				if ($scope.claimDefaults.use_single_claim && entry.patientInsuranceId === $scope.claim.responsiblePatientInsuranceId)
					response.splice(index, 1);
			});
			$scope.nextPayer.list = response;
			var empty = {
				patientInsuranceId: null,
				patientInsurance: {
					insuranceCompany: {
						name: 'None'
					}
				}
			};
			$scope.nextPayer.list.unshift(empty);
		});
	};

	$scope.checkForMissingDXPointersOrCodes = function () {
		if ($scope.insurances[0].ivlcs) {
			angular.forEach($scope.insurances[0].ivlcs, function (ivlc) {
				if (!ivlc.prescriptionLCode.diagnosisCode1id && !ivlc.prescriptionLCode.diagnosisCode2id && !ivlc.prescriptionLCode.diagnosisCode3id && !ivlc.prescriptionLCode.diagnosisCode4id) {
					$scope.DXpointerMissing = true;
				}
			});
		}
		if (!$scope.prescriptionDiagnosisCodes || $scope.prescriptionDiagnosisCodes.length === 0) {
			$scope.DXCodeMissing = true;
		}
		return $scope.DXpointerMissing || $scope.DXCodeMissing;
	};

	$scope.followUpTaskCalendar = {
		opened: false,
		dateOptions: {
			formatYear: "yy",
			startingDay: 1,
		},
		open: function ($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.followUpTaskCalendar.opened = true;
		}
	};

	var initCalendar = function () {
		$scope.calendar = {
			opened: {
				manualSubmission: false,
				dos: false,
				manualLineSubmission: {}
			},
			dateOptions: {
				formatYear: 'yy',
				maxDate: $moment().format('YYYY-MM-DD'),
				minDate: $scope.minDate,
				startingDay: 1,
				placement: 'bottom-right'
			},
			open: function ($event, type, index) {
				$event.preventDefault();
				$event.stopPropagation();
				if (type === 'manualLineSubmission') {
					$scope.calendar.opened.manualLineSubmission[index] = true;
				} else if (type === "manualSubmission") {
					$scope.calendar.opened.manualSubmission = true;
				} else {
					$scope.calendar.opened.dos = true;
				}
			}
		};
	};
	initCalendar();

	var initDateResolvedCalendar = function () {
		$scope.dateResolvedCalendar = {
			opened: {
				'dateResolved': false
			},
			dateOptions: {
				formatYear: 'yy',
				minDate: $scope.minDate,
				startingDay: 1
			},
			open: function ($event, which) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.dateResolvedCalendar.opened[which] = true;
			}
		};
	};
	initDateResolvedCalendar();

	var initDosCalendarCalendar = function () {
		$scope.dosCalendar = {
			opened: {
				'dos': false
			},
			dateOptions: {
				formatYear: 'yy',
				minDate: $scope.minDate,
				// maxDate: $scope.maxDate,
				startingDay: 1
			},
			open: function ($event, which) {
				$event.preventDefault();
				$event.stopPropagation();
				$scope.dosCalendar.opened[which] = true;
			}
		};
	};
	initDosCalendarCalendar();

	$scope.reloadPDFs = function (first, patientInsuranceId, otherPatientInsuranceId) {
		$scope.reloadingClaimBlank = true;
		$scope.reloadingClaimFull = true;
		ClaimFactory.viewClaimAll({
			claimId: claimId,
			billingBranchId: $scope.claim.billingBranchId,
			patientInsuranceId: patientInsuranceId,
			otherPatientInsuranceId: otherPatientInsuranceId,
			form1500TemplateId: $scope.form1500TemplateId
		}).$promise.then(function (response) {
			$scope.blankFiles = response.result_blank;
			$scope.files = response.result_full;
			$scope.validationErrors = response.validation_errors;
			$scope.pageCount = response.pageCount;
			$scope.reloadingClaimBlank = false;
			$scope.reloadingClaimFull = false;
			if (first) {
				$scope.display1500pdf("#file_1500_pdf");
			}
		});
	};

	$scope.updatePDFs = function (_first, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId) {
		$scope.reloadingClaimBlank = true;
		$scope.reloadingClaimFull = true;
		ClaimFactory.viewClaimAll({
			claimId: claimId,
			billingBranchId: $scope.claim.billingBranchId,
			patientInsuranceId: patientInsuranceId,
			otherPatientInsuranceId: otherPatientInsuranceId,
			form1500TemplateId: form1500TemplateId
		}).$promise.then(function (response) {
			$scope.blankFiles = response.result_blank;
			$scope.files = response.result_full;
			$scope.validationErrors = response.validation_errors;
			$scope.reloadingClaimBlank = false;
			$scope.reloadingClaimFull = false;
			$scope.pageCount = response.pageCount;
			$scope.display1500pdf("#blank_1500_pdf");
			$scope.display1500pdf("#file_1500_pdf");
		});
	};


	$scope.display1500pdf = function (containerId) {

		if (containerId === "#file_1500_pdf")
			$scope.loadPdfContainer($scope.files, containerId);
		else
			$scope.loadPdfContainer($scope.blankFiles, containerId);
	};

	$scope.showPrintView = function () {
		var result = $scope.files && $scope.blankFiles;
		return result;
	};

	$scope.$watch('claim.billingBranchId', function (newValue, _oldValue) {
		if (newValue) {
			delete $scope.files;
			delete $scope.blankFiles;

			$scope.claim_level_1500_overrides = UserService.features.claim_level_1500_overrides;
			if($scope.claim_level_1500_overrides) {
				if ($scope.claim.form1500TemplateId > 0) {
					$scope.form1500TemplateId = $scope.claim.form1500TemplateId;
				}
			}
			// Don't automatically load PDFs - they will be loaded when the user clicks the Print View tab
			// let patientInsuranceId = !!$scope.printView.printViewPatientInsuranceId ? $scope.printView.printViewPatientInsuranceId : $scope.claim.responsiblePatientInsuranceId;
			// let otherPatientInsuranceId = !!$scope.printViewOtherPatientInsuranceId ? $scope.printViewOtherPatientInsuranceId : $scope.claim.otherPatientInsuranceId;
			// $scope.reloadPDFs(false, patientInsuranceId, otherPatientInsuranceId);
			if ($scope.claim.billingBranchId) {
				angular.forEach(BranchService.userBranches, function (branch) {
					if (branch.id === $scope.claim.billingBranchId) {
						$scope.claim.billingBranch = branch;
					}
				});
			}
		}
	});

	$scope.$watch('printView.printViewPatientInsuranceId', function(printViewPatientInsuranceId) {
		if (printViewPatientInsuranceId > 0 && !!$scope.insurances) {
			angular.forEach($scope.insurances, function(insurance, index) {
				if(insurance.patientInsurance.id === printViewPatientInsuranceId) {
					$scope.form1500TemplateId = insurance.patientInsurance.insuranceCompany.form1500TemplateId;
				}
			});
		}
	});

	$scope.$watchGroup(['submissions', 'minDate'], function () {
		$scope.accountingLocked = true;
		if ($scope.submissions && $scope.submissions.length) {
			$scope.submissions.sort(compare);
			$scope.firstSubmissionDate = $scope.submissions[0].submissionDate;
			$scope.maxDate = $scope.firstSubmissionDate;
			initDosCalendarCalendar();
			/* removed for SCRUM-2146 !$scope.billingCycleLockdown() && */
			// if (!$scope.billingCycleLockdown() && $scope.canOverrideClaimValues)
			// 	$scope.accountingLocked = false;
		}

		if ($scope.canOverrideClaimValues) {
			$scope.accountingLocked = false;
		}
	});

	function compare(a, b) {
		return a.submissionDate > b.submissionDate ? 1 : -1;
	}

	$scope.updatePatientBalance = function () {
		$scope.claim.totalPtResponsibilityBalance = $scope.realBalance - $scope.claim.totalClaimBalance;
		$scope.claim.totalPtResponsibilityBalance = Math.round($scope.claim.totalPtResponsibilityBalance * 100) / 100;
	};

	$scope.updateInsuranceBalance = function () {
		$scope.claim.totalClaimBalance = $scope.realBalance - $scope.claim.totalPtResponsibilityBalance;
		$scope.claim.totalClaimBalance = Math.round($scope.claim.totalClaimBalance * 100) / 100;
	};

	$scope.formatAddress = function (o) {
		if (o === undefined) return "";
		var result = o.streetAddress + ", " + o.city + ", " + o.state;
		result = o.zipcode ? result + " " + o.zipcode : result;
		return result;
	};

	$scope.getLetter = function (index) {
		return String.fromCharCode(index + 65);
	};

	$scope.updateClaim = function (willSend) {
		$('#save-claim').button('loading');
		if ($scope.manualSubmission && !$scope.manualClaimSubmission.submissionDate) {
			UtilService.displayAlert("danger", "Claim Submission Date is required.", "#header-alert-container");
			$('#save-claim').button('reset');
			return;
		}
		if ($scope.manualSubmission && moment($scope.manualClaimSubmission.submissionDate).isBefore($scope.minDate)) {
			UtilService.displayAlert("danger", "Claim Submission Date must be after Billing Cycle Lock Down Date.", "#header-alert-container");
			$('#save-claim').button('reset');
			return;
		}
		//Added here due to need to save default Paper per tixbug-1372
		if ($scope.manualSubmission && $scope.claim.nymblStatusId === undefined) {
			$scope.claim.nymblStatusId = 14;
		}

		ClaimFactory.save($scope.claim).$promise.then(function (response) {
			$scope.claim = response;

			if($scope.claim.prescription.manualPodSignedDate){
				$scope.dosPODWarning = ($scope.claim.prescription.manualPodSignedDate !== $scope.claim.dateOfService);
			} else {
				$scope.dosPODWarning = !!($scope.claim.prescription.signedDate && $scope.claim.prescription.signedDate !== $scope.claim.dateOfService);
			}

			UserFactory.get({id: $scope.claim.prescription.treatingPractitionerId}).$promise.then(function (response) {
				$scope.claim.prescription.treatingPractitioner = response;
			});

			angular.forEach($scope.insurances[0].ivlcs, function (ivlc, index) {
				InsuranceVerificationLCodeFactory.save(ivlc).$promise.then(function (_ivlc1) {
					PrescriptionLCodeFactory.save(ivlc.prescriptionLCode).$promise.then(function (_response) {
						if (index === $scope.insurances[0].ivlcs.length - 1) {
							$scope.updateClaimComplete(willSend);
							$scope.reloadPDFs(false, $scope.printView.printViewPatientInsuranceId, $scope.printViewOtherPatientInsuranceId);
						}
					}, function (error) {
						console.log(error);
					});
				});
			});
			if (!$scope.$$phase) {
				$scope.$apply();
			}
			if ($scope.userReassigned) {
				NotificationService.buildClaimNotification($scope.claim, "A claim has been reassigned to you");
			}
			if ($scope.nextPayer.selected) {
				var params = {
					claimId: $scope.claim.id,
					patientInsuranceId: $scope.nextPayer.selected
				};
				PaymentFactory.sendToNextPayer(params).$promise.then(function (response) {
					$scope.claim = response;
					if (!$scope.$$phase) {
						$scope.$apply();
					}
				}, function (error) {
					$scope.nextPayer.selected = null;
					UtilService.displayAlert("warning", error.data.message, "#header-alert-container");
				});
			} else if ($scope.manualSubmission) {
				$scope.manualClaimSubmission.patientInsuranceId = $scope.claim.responsiblePatientInsuranceId;
				ClaimSubmissionFactory.manualSubmission($scope.manualClaimSubmission).$promise.then(function (_response) {
					$scope.manualSubmission = false;
					ClaimSubmissionFactory.findByClaimIdForDTO({claimId: $scope.claim.id}).$promise.then(function (response) {
						$scope.submissions = response;
						$rootScope.$broadcast('loadPatientPayments', {patientId: $scope.patientId});
						$rootScope.$broadcast('loadTransactionHistory', {claimId: claimId});
						ClaimService.profileLoad($scope.claim.prescription.patientId);
					});
				});
			}
			angular.forEach($scope.insurances, function (iv, $index) {
				if ($scope.claim.prescriptionId === iv.prescriptionId && $scope.claim.patientInsuranceId === iv.patientInsuranceId) {
					$scope.claim.insuranceVerificationIndex = $index;
				}
			});
		}, function (error) {
			console.log(error.data.message);
			var errorMessage = "Changes were not saved!";
			if (error && error.data && error.data.message) {
				errorMessage += error.data.message;
				DiffModalService.popModal(error.data.diffs, error.data.message, $scope.claim.id, "Claim");
			}
			UtilService.displayAlert("danger", errorMessage, "#header-alert-container");
		});
	};

	$scope.updateClaimComplete = function (willSend) {
		$timeout(function () {
			$('#save-claim').button('reset');
			UtilService.displayAlert("success", "Claim Updated Successfully.", "#header-alert-container");
			loadNextPayer();
			if (!$scope.$$phase) {
				$scope.$apply();
			}
			if (willSend) {
				ClaimFactory.sendClaimFiles({
					claimIds: [$scope.claim.id],
					billingBranchId: $scope.claim.billingBranchId
				}).$promise.then(function (response) {
					console.log("Sent claim");
					$('#send-files').button('reset');
					UtilService.displayAlert("success", "<p>" + response.data + "</p>", "#header-alert-container");
					reloadClaimSubmissions();
					ClaimFileFactory.findByClaimId({claimId: $scope.claim.id}).$promise.then(function (response) {
						$scope.hasSubmittedClaimFile = false;
						$scope.claimFiles = response;
						angular.forEach($scope.claimFiles, function (claimFile) {
							if (claimFile.submitted) {
								$scope.hasSubmittedClaimFile = true;
							}
						});
					});
				}, function (error) {
					$('#send-files').button('reset');
					UtilService.displayAlert("danger", "<p>Sending claim failed.</p>" + $sce.trustAsHtml(error.data.data), "#x12-alert-container");
				});
			}
		}, 2000);
	};

	$scope.addFollowUpTask = function (isValid) {
		$scope.submitted = true;
		if (isValid) {
			TaskFactory.saveTask($scope.followUpTaskEntry).$promise.then(function (response) {
				//	if ($scope.claim.userId !== response.userId) {
				// save the claim "irregardless" of the userId so that the claim registers activity via the updated at
				$scope.claim.userId = response.userId;
				$scope.claim.updatedAt = $moment().format('YYYY-MM-DD');

				ClaimFactory.save($scope.claim).$promise.then(function (response) {
					$scope.claim = response;
				}, function (error) {
					console.log(error.data.message);
					var errorMessage = "Changes were not saved!";
					if (error && error.data && error.data.message) {
						errorMessage += error.data.message;
					}
					UtilService.displayAlert("danger", errorMessage, "#header-alert-container");
				});
				//	}
				$scope.followUpTasks = TaskFactory.findByClaimIdAndType({claimId: $scope.claim.id, type: 'follow_up'});
				initFollowUpTaskEntry();
			});
			$scope.submitted = false;
		}
	};

	$scope.editFollowUpTask = function (index, action) {
		if (action === 'complete') {
			if ($scope.followUpTasks[index].completed) {
				$scope.followUpTasks[index].dateCompleted = $moment().utc().format('YYYY-MM-DD HH:mm:ss');
				$scope.followUpTasks[index].completedById = $scope.currentUser.id;
			} else {
				$scope.followUpTasks[index].dateCompleted = null;
				$scope.followUpTasks[index].completedById = null;
			}
			TaskFactory.save($scope.followUpTasks[index]).$promise.then(function (savedTask) {
				if (savedTask.completed) {
					NotificationFactory.markFollowUpNotificationRead(savedTask).$promise.then(function (_response) {
						UserNotificationService.loadNotifications();
					});
				}
			});
		} else if (action === 'edit') {
			$scope.openTaskModal($scope.followUpTasks[index], index);
		} else if (action === 'delete') {
			TaskFactory.deleteTaskAndNotification($scope.followUpTasks[index]).$promise.then(function () {
				$scope.followUpTasks = TaskFactory.findByClaimIdAndType({claimId: $scope.claim.id, type: 'follow_up'});
			});
		}
	};

	$scope.applyAllPOS = function () {
		var indexOfFirstCode = $scope.insurances[0].ivlcs.findIndex(function (ivlc) {
			return ivlc.prescriptionLCode.orderNum === 1;
		});
		angular.forEach($scope.insurances[0].ivlcs, function (_entry, index) {
			$scope.insurances[0].ivlcs[index].prescriptionLCode.pos = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.pos;
		});
	};

	$scope.applyAll = function () {
		var indexOfFirstCode = $scope.insurances[0].ivlcs.findIndex(function (ivlc) {
			return ivlc.prescriptionLCode.orderNum === 1;
		});
		angular.forEach($scope.insurances[0].ivlcs, function (_entry, index) {
			$scope.insurances[0].ivlcs[index].prescriptionLCode.dateOfService = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.dateOfService;
			// $scope.insurances[0].ivlcs[index].prescriptionLCode.diagnosisPointer = $scope.insurances[0].ivlcs[0].prescriptionLCode.diagnosisPointer;
			$scope.insurances[0].ivlcs[index].prescriptionLCode.diagnosisCode1id = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.diagnosisCode1id;
			$scope.insurances[0].ivlcs[index].prescriptionLCode.diagnosisCode2id = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.diagnosisCode2id;
			$scope.insurances[0].ivlcs[index].prescriptionLCode.diagnosisCode3id = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.diagnosisCode3id;
			$scope.insurances[0].ivlcs[index].prescriptionLCode.diagnosisCode4id = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.diagnosisCode4id;
		});
	};

	$scope.applyAllMods = function () {
		var indexOfFirstCode = $scope.insurances[0].ivlcs.findIndex(function (ivlc) {
			return ivlc.prescriptionLCode.orderNum === 1;
		});
		angular.forEach($scope.insurances[0].ivlcs, function (_entry, index) {
			$scope.insurances[0].ivlcs[index].prescriptionLCode.modifier1 = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.modifier1;
			$scope.insurances[0].ivlcs[index].prescriptionLCode.modifier2 = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.modifier2;
			$scope.insurances[0].ivlcs[index].prescriptionLCode.modifier3 = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.modifier3;
			$scope.insurances[0].ivlcs[index].prescriptionLCode.modifier4 = $scope.insurances[0].ivlcs[indexOfFirstCode].prescriptionLCode.modifier4;
		});
	};

	$scope.printPaymentInvoice = function () {
		ClaimFactory.get({id: $scope.claim.id}).$promise.then(function (claim) {
			UtilService.openPrintScreen('hospital_invoice?prescriptionId=' + claim.prescription.id + '?claimId=' + claim.id + '?printSalesTax=' + $scope.printSalesTax);
		});
	};

	$scope.sendSingleClaimFile = function () {
		if (!$rootScope.branchId) {
			UtilService.displayAlert("danger", "<p>A branch must be selected.</p>", "#header-alert-container");
			return;
		} else if ($moment($scope.claim.dateOfService).isAfter($moment().format('YYYY-MM-DD'))) {
			alert('Sending claim with a future Date of Service is not allowed.');
			return;
		} else if ($scope.claim.prescription && $scope.claim.prescription.patient &&
			(!$scope.claim.prescription.patient.streetAddress || !$scope.claim.prescription.patient.city ||
				!$scope.claim.prescription.patient.state || !$scope.claim.prescription.patient.zipcode)) {
			alert('The patient address is incomplete. The claim cannot be sent.');
			return;
		} else if ($scope.checkForMissingDXPointersOrCodes()) {
			if (!confirm("Are you sure you want to send this claim? There are DX codes or DX pointers missing that may result in your claim being rejected.")) {
				return;
			}
		} else if ($rootScope.branch && (!$rootScope.branch.outClearingHouse || !$rootScope.branch.outClearingHouse.active)) {
			UtilService.displayAlert("danger", "<p>The current branch selected is not active for Waystar processing.</p>", "#header-alert-container");
			return;
		}

		var claimIds = [];
		claimIds.push($scope.claim.id);
		$scope.openSendClaimsToBillingModal(claimIds);

	};


	$scope.openSendClaimsToBillingModal = function (claimIds) {
		var modalInstance = $uibModal.open({
			animation: false,
			templateUrl: '_send_claims_to_billing.html',
			controller: 'SendClaimsToBillingModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				claimIds: function () {
					return claimIds;
				}
			}
		});
		modalInstance.result.then(function (response) {
			if (response === undefined) {
				$('#send-files').button('reset');
				UtilService.displayAlert("danger", "<p>Sending claim failed.</p>", "#x12-alert-container");
			} else {
				console.log("Sent claim");
				$('#send-files').button('reset');
				UtilService.displayAlert("success", "<p>" + response.data + "</p>", "#header-alert-container");
			}
			reloadClaimSubmissions(true);
		});
	};

	$scope.splitCodes = function () {
		var plcIds = [];
		var plcNames = [];
		for (var [key, value] of Object.entries($scope.splitChecked)) {
			if (value) {
				plcIds.push(key);
				var name = $filter('filter')($scope.insurances[0].ivlcs, {prescriptionLCodeId: key})[0].prescriptionLCode.lCode.name;
				plcNames.push(name);
			}
		}
		if (confirm("Are you sure you want to split the following codes?\n" + plcNames)) {
			PrescriptionLCodeFactory.splitPrescriptionLCode(plcIds).$promise.then(function (_response) {
				// location.reload();
				loadVerifications();
				setTimeout(function () {
					$scope.checkSplit();
				}, 1000);
			});
		}
	};

	$scope.splitDisabled = true;
	$scope.splitCheckedDisabled = true;
	$scope.checkSplit = function () {
		$scope.splitDisabled = true;
		$scope.splitCheckedDisabled = true;
		var checkBoxes = angular.element('.check-split');
		var tmp = [];
		$scope.splitCheckedDisabled = !checkBoxes.length;
		for (var i = 0; i < checkBoxes.length; i++) {
			tmp.push(checkBoxes[i].checked);
			if (checkBoxes[i].checked) {
				$scope.splitDisabled = false;
			}
		}
		var unique = [...new Set(tmp)];
		if (unique.length === 1) {
			$scope.allChecked.value = unique[0];
		} else {
			$scope.allChecked.value = false;
		}
	};

	$scope.allChecked = {
		value: false
	};
	$scope.toggleAll = function () {
		$scope.splitDisabled = !$scope.allChecked.value;
		angular.forEach($scope.insurances[0].ivlcs, function (ivlc) {
			if (ivlc.prescriptionLCode.quantity % 2 === 0 && ivlc.prescriptionLCode.modifier1 && ivlc.prescriptionLCode.modifier2)
				$scope.splitChecked[ivlc.prescriptionLCode.id] = $scope.allChecked.value;
		});
	};

	$scope.swapBalance = function () {
		var tempNewInsurance = angular.copy($scope.claim.totalPtResponsibilityBalance);
		var tempNewPatient = angular.copy($scope.claim.totalClaimBalance);
		$scope.claim.totalClaimBalance = tempNewInsurance;
		$scope.claim.totalPtResponsibilityBalance = tempNewPatient;
		$scope.claim.totalClaimBalance = Math.round($scope.claim.totalClaimBalance * 100) / 100;
		$scope.claim.totalPtResponsibilityBalance = Math.round($scope.claim.totalPtResponsibilityBalance * 100) / 100;
	};

	$scope.saveIVLC = function (plc) {
		PrescriptionLCodeFactory.save(plc).$promise.then(function (_response) {
			$scope.reloadPDFs(false, $scope.printView.printViewPatientInsuranceId, $scope.printViewOtherPatientInsuranceId);
		});
	};

	$scope.download = function () {
		// Check if the claim is part of a bulk submission
		if ($scope.isBulkClaim()) {
			// Download the bulk X12 file
			DownloadFactory.downloadBulkX12({jobId: $scope.claim.bulkClaimJobId}).$promise.then(function (response) {
				var xfilename = response.header['x-filename'];
				if (!xfilename) {
					xfilename = "bulk_" + $scope.claim.bulkClaimJobId + ".txt";
				}

				var element = document.createElement('a');
				element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(response.data));
				element.setAttribute('download', xfilename);

				element.style.display = 'none';
				document.body.appendChild(element);
				element.click();
				document.body.removeChild(element);
			}, function (_error) {
				UtilService.displayAlert("danger", "<p>Building bulk claim file failed.</p>", "#x12-alert-container");
			});
		} else {
			// Download the single claim X12 file
			DownloadFactory.download({claimId: $scope.claim.id}).$promise.then(function (response) {
				var xclaim = response.header['x-claim'];
				var xstatus = response.header['x-status'];
				var xmessages = response.header['x-messages'] || "";
				var xfilename = response.header['x-filename'];

				if (xstatus && xclaim) {
					var type = '';
					if (xstatus === 'Reject') {
						type = 'danger';
						xstatus = 'Rejected';
					}
					if (xstatus === 'Accept') {
						type = 'success';
						xstatus = 'Accepted';
					}
					var message = 'Claim #' + xclaim + ' was ' + xstatus + ' by Waystar\'s Real Time Claim Edit Rules Engine';

					UtilService.displayAlert(type, '<p>' + message + '</p><p>' + xmessages + '</p>', "#x12-alert-container");
				}
				var element = document.createElement('a');
				element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(response.data));
				element.setAttribute('download', xfilename + '.txt');

				element.style.display = 'none';
				document.body.appendChild(element);
				element.click();
				document.body.removeChild(element);
			}, function (error) {
				UtilService.displayAlert("danger", "<p>Building claim failed.</p>" + $sce.trustAsHtml(error.data.data), "#x12-alert-container");
			});
		}
	};

	$scope.openTaskModal = function (task, index) {
		task.editing = true;
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/tasks/_task_modal.html',
			controller: 'TaskCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				task: function () {
					return task;
				},
				fromAlert: function () {
					return true;
				},
				patientFromProfile: function () {
					return "";
				}
			}
		});
		modalInstance.result.then(function () {
			TaskFactory.findByClaimIdAndType({
				claimId: $scope.claim.id,
				type: 'follow_up'
			}).$promise.then(function (response) {
				$scope.followUpTasks = response;
				if ($scope.followUpTasks[index].completed) {
					NotificationFactory.markFollowUpNotificationRead(task).$promise.then(function (_response) {
						UserNotificationService.loadNotifications();
					});
				}
			});
		});
	};

	$scope.zeroOutInsuranceAndPatientBalance = function () {
		$scope.claim.totalPtResponsibilityBalance = 0;
		$scope.claim.totalClaimBalance = 0;
	};

	$scope.edit = function (patientId, prescriptionId) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/prescription/_update_diagnosis_code_modal.html',
			controller: 'PrescriptionDiagnosisCodeCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'lg',
			resolve: {
				patientId: function () {
					return patientId;
				},
				prescriptionId: function () {
					return prescriptionId;
				}
			}
		});
		modalInstance.result.then(function () {
		});
	};

	$scope.editAuthorizationNumber = function (patientId, prescriptionId, claimId) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/prescription/_update_insurance_verification_auth_number_modal.html',
			controller: 'PrescriptionEditBillingCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				patientId: function () {
					return patientId;
				},
				prescriptionId: function () {
					return prescriptionId;
				},
				claimId: function () {
					return claimId;
				},
				section: function () {
					return undefined;
				}
			}
		});
		modalInstance.result.then(function () {
		});
	};

	$scope.editClaimPhysicians = function (patientId, prescriptionId, claimId, section) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/prescription/_update_claim_physicians_modal.html',
			controller: 'PrescriptionEditBillingCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'xlg',
			resolve: {
				patientId: function () {
					return patientId;
				},
				prescriptionId: function () {
					return prescriptionId;
				},
				claimId: function () {
					return claimId;
				},
				section: function () {
					return section;
				}
			}
		});
		modalInstance.result.then(function () {
		});
	};

	$scope.printTooltip = "Changes to Patient Insurance or Other Insurance from this screen will only be temporarily reflected on the " +
		"HCFA for the purpose of printing. Reloading the page or changing other data on the HCFA from this screen will reset " +
		"the insurance fields to match what they are currently saved as on the claim.";

	//  COMMENTED OUT FOR SCRUM--3425;
	//  $scope.claimHasPrimaryPayment = function () {
	//   var result = false;
	//   angular.forEach(ClaimService.claimPayments, function (cp) {
	//     if (ClaimService.belongsToPrescription(cp.appliedPayment, $scope.claim.prescription.id) &&
	//       cp.appliedPayment.payment.insuranceCompany &&
	//       cp.appliedPayment.payment.insuranceCompany.id === $scope.claim.patientInsurance.insuranceCompany.id) {
	// 			result = true;
	// 			return true;
	// 		}
	// 	});
	// 	return result;
	// };

	function initFollowUpTaskEntry() {
		$scope.followUpTaskEntry = {
			name: 'Follow Up for Claim #' + $scope.claim.id,
			completed: false,
			claimId: claimId,
			prescriptionId: $scope.claim.prescription.id,
			patientId: $scope.claim.prescription.patient.id,
			patientInsuranceId: undefined,
			dueDate: undefined,
			description: '',
			userId: $scope.currentUser.id,
			type: 'follow_up',
			createdById: $scope.currentUser.id
		};
	}

	$scope.billingCycleLockdown = function () {
		if ($scope.claim) {
			var lockdown = $scope.billingCycleEndDate !== '' && $scope.minDate !== '2000-01-01' && $scope.firstSubmissionDate &&
				$moment($scope.minDate).isAfter($moment($scope.firstSubmissionDate).format('YYYY-MM-DD'), 'day');
			return lockdown;
		}
	};

	$scope.loadClaimFiles = function (patientInsurance) {
		var results = [];
		if (patientInsurance) {
			angular.forEach($scope.claimFiles, function (cf, _index) {
				var insuranceId = cf.filename.split('_')[2].split('.')[0];
				if (patientInsurance.insuranceCompanyId === parseInt(insuranceId)) {
					results.push(cf);
				}
			});
		}
		return results;
	};

	$scope.deleteManualSubmission = function (id) {
		if (confirm("Are you sure to wish to delete manual claim submission " + id + "?")) {
			ClaimSubmissionFactory.delete({id: id}).$promise.then(function () {
				reloadClaimSubmissions(true);
			});
		}
	};

	$scope.createManualSubmission = function (iv) {
		var submission = {
			claimId: $scope.claim.id,
			patientInsuranceId: iv.patientInsuranceId,
			submissionDate: iv.$submissionDate,
			submittedById: $scope.currentUser.id
		};
		ClaimSubmissionFactory.save(submission).$promise.then(function () {
			ClaimSubmissionFactory.findByClaimIdForDTO({claimId: $scope.claim.id}).$promise.then(function (response) {
				iv.$submissionDate = '';
				iv.$manualSubmission = false;
				$scope.submissions = response;
			});
		});
	};

	var submissionNotes = {};
	$scope.modifySubmissionNote = function (action, submission) {
		if (action === 'edit') {
			submissionNotes[submission.id] = submission.notes;
		} else if (action === 'cancel') {
			submission.notes = submissionNotes[submission.id];
		} else if (action === 'save') {
			delete submission.submittedBy;
			ClaimSubmissionFactory.save(submission).$promise.then(function () {
				ClaimSubmissionFactory.findByClaimIdForDTO({claimId: $scope.claim.id}).$promise.then(function (response) {
					$scope.submissions = response;
				});
			});
		}
		submission.$readonly = !submission.$readonly;
	};

	$scope.viewClaimPDF = function (iv, submission) {

		var patientInsuranceId = submission.patientInsuranceId;
		var otherPatientInsuranceId = null;
		if (iv.carrierType === 'primary') {
			var sec = $filter('filter')($scope.insurances, {carrierType: 'secondary'});
			otherPatientInsuranceId = sec.length ? sec[0].patientInsuranceId : '';
		} else if (iv.carrierType === 'secondary') {
			var pri = $filter('filter')($scope.insurances, {carrierType: 'primary'});
			otherPatientInsuranceId = pri.length ? pri[0].patientInsuranceId : '';
		}

		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/_hicf_1500_modal.html',
			controller: 'HICF1500ModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'full',
			resolve: {
				claimFileId: function () {
					return submission.claimFileId;
				},
				patientInsuranceId: function () {
					return patientInsuranceId;
				},
				otherPatientInsuranceId: function () {
					return otherPatientInsuranceId;
				}
			}
		});

		modalInstance.result.then(function (_value) {
		});
	};

	// $scope.$watch('followUpTaskEntry.userId', function(newValue, oldValue) {
	// 	if (newValue !== undefined && newValue !== null && newValue !== '')
	// 		$('#taskUserId').prop('disabled', true).trigger("chosen:updated");
	// 	else
	// 		$('#taskUserId').prop('disabled', false).trigger("chosen:updated");
	// });

	function loadVerifications() {
		$scope.splitDisabled = true;
		$scope.splitCheckedDisabled = true;
		if (!$scope.claim) return;
		//SCRUM-1862: check for bad data
		//original call: InsuranceVerificationFactory.findByPrescriptionId({prescriptionId: $scope.claim.prescriptionId}).$promise.then(function (insurances) {
		$scope.insurances = {};
		InsuranceVerificationFactory.findByPrescriptionIdAndCheckPatient({
			prescriptionId: $scope.claim.prescriptionId,
			patientId: $scope.patientId
		}).$promise.then(function (insurances) {
			$scope.insurances = insurances;
			angular.forEach($scope.insurances, function (iv, $index) {
				if ($scope.claim.prescriptionId === iv.prescriptionId && $scope.claim.patientInsuranceId === iv.patientInsuranceId) {
					$scope.claim.insuranceVerificationIndex = $index;
				}
			});
			$scope.totalCharges = 0;
			$scope.totalUnresolved = 0;
			$scope.totalSalesTax = 0;
			$scope.totalAllowable = 0;
			InsuranceVerificationLCodeFactory.findByInsuranceVerificationId({insuranceVerificationId: $scope.insurances[0].id}).$promise.then(function (ivlcs) {
				$scope.insurances[0].ivlcs = [];
				$scope.insurances[0].ivlcs = ivlcs;
				angular.forEach($scope.insurances[0].ivlcs, function (ivlc, _index2) {
					$scope.splitChecked[ivlc.prescriptionLCode.id] = false;
					if (ivlc.prescriptionLCode.quantity % 2 === 0 && ivlc.prescriptionLCode.modifier1 && ivlc.prescriptionLCode.modifier2) {
						$scope.splitCheckedDisabled = false;
					}
					if (ivlc.totalAllowable === undefined) ivlc.totalAllowable = 0;
					$scope.totalCharges += ivlc.totalCharge;
					$scope.totalUnresolved += ivlc.prescriptionLCode.balance;
					$scope.totalSalesTax += ivlc.salesTax;
					if (ivlc.covered) {
						$scope.totalAllowable += ivlc.totalAllowable;
						$scope.coveredCount++;
					}
				});
			});
		});

	}

	$scope.loadPdfContainer = function (contents, containerId) {
		PdfService.unloadPdfContainer(containerId);
		var configs = {
			'fileContents': contents, 'containerId': containerId
		};
		PdfService.load1500Pdf(configs, $scope.pageCount);
	};

	$scope.manualCreateButtonDisabled = function (iv) {
		return !(iv.$manualSubmission && iv.$submissionDate);
	};

	// Determine if this is a bulk claim for UI display purposes
	$scope.isBulkClaim = function() {
		return $scope.claim && ($scope.claim.bulkSubmission === true || $scope.claim.bulkClaimJobId != null);
	};

	const sendButton = $('#send-files');
	const inactiveStatuses = ['inactive_primary_payer_corrected_claim', 'inactive_refund_due_primary_payer_corrected'];
	$scope.$watch('claim.nymblStatusId', function (newValue, _oldValue) {
		if (newValue) {
			$scope.newStatusId = newValue;
			let disabled = inactiveStatuses.includes($scope.claim.nymblStatus.key) ||
				$scope.claim.nymblStatusId === 12 || $scope.claim.nymblStatusId === 13 || $scope.claim.nymblStatusId === 14 || $scope.claim.nymblStatusId === 33;
			sendButton.prop('disabled', disabled);
		}
	});

	$rootScope.$on('reloadClaimSubmissions', function (_event, args) {
		if (args && args.claimId && args.claimId === $scope.claim.id) {
			reloadClaimSubmissions(true);
		}
	});


	function reloadClaimSubmissions(reloadProfile) {
		ClaimSubmissionFactory.findByClaimIdForDTO({claimId: $scope.claim.id}).$promise.then(function (response) {
			$scope.submissions = response;
			$rootScope.$broadcast('loadPatientPayments', {patientId: $scope.patientId});
			$rootScope.$broadcast('loadTransactionHistory', {claimId: claimId});
			if (reloadProfile) {
				ClaimService.profileLoad($scope.claim.prescription.patientId);
			}
		});
	}
}
