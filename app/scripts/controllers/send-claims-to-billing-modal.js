app.controller('SendClaimsToBillingModalCtrl', SendClaimsToBillingModalCtrl);
SendClaimsToBillingModalCtrl.$inject = ['$scope', '$moment', '$uibModalInstance', 'claimIds', 'ClaimFactory', '$sce', 'UtilService', '$interval', 'SystemSettingFactory', 'UserService'];

function SendClaimsToBillingModalCtrl($scope, $moment, $uibModalInstance, claimIds, ClaimFactory, $sce, UtilService, $interval, SystemSettingFactory, UserService) {

	$scope.moment = $moment;
	$scope.userService = UserService; // Add UserService to the scope

	$scope.disableButton = claimIds.length > 0;
	$scope.claims = [];
	$scope.submissionType = claimIds.length > 1 ? 'bulk' : 'single'; // Default based on claim count
	$scope.bulkValidationErrors = [];
	$scope.bulkValidationPassed = false;
	$scope.jobStatus = null;
	$scope.jobStatusInterval = null;
	$scope.responseMessage = null;
	$scope.enableBulkClaims = false; // Default to false until we load the system setting

	// Shared function to check if bulk claims are enabled (system setting + user privilege)
	$scope.isBulkClaimEnabled = function() {
		return $scope.enableBulkClaims && $scope.userService.hasPermission('bulk_claim_submit');
	};

	// Load claims
	angular.forEach(claimIds, function (claimId) {
		ClaimFactory.get({id: claimId}).$promise.then(function (claim) {
			$scope.claims.push(claim);
		});
	});

	// Check if bulk submission is valid when multiple claims are selected and bulk claims are enabled
	// We'll do this after loading the system setting
	SystemSettingFactory.findBySectionAndField({section: 'billing', field: 'enable_bulk_claims'}).$promise.then(function (response) {
		$scope.enableBulkClaims = response && response.value === 'Y';
		console.log("Bulk claims enabled:", $scope.enableBulkClaims);

		// Only validate bulk submission if bulk claims are enabled AND user has the privilege
		if (claimIds.length > 1 && $scope.isBulkClaimEnabled()) {
			ClaimFactory.validateBulkSubmission({claimIds: claimIds}).$promise.then(function (response) {
				console.log("Bulk validation response:", response);
				$scope.bulkValidationPassed = response.valid;
				$scope.bulkValidationErrors = response.errors || [];
			}, function (error) {
				console.error("Error validating bulk submission:", error);
				$scope.bulkValidationErrors = ['Error validating bulk submission'];
			});
		}
	}, function (error) {
		console.error("Error loading bulk claims setting:", error);
		$scope.enableBulkClaims = false; // Default to false if there's an error
	});

	// Set submission type and send claims
	$scope.setSubmissionTypeAndSend = function(type) {
		$scope.submissionType = type;
		$scope.sendClaimsToBilling();
	};

	// Check if bulk submission is available
	$scope.canSubmitBulk = function() {
		return claimIds.length > 1 &&
		       $scope.bulkValidationPassed &&
		       $scope.isBulkClaimEnabled();
	};

	// Send claims to billing
	$scope.sendClaimsToBilling = function () {
		// If we're already showing job status, just refresh it
		if ($scope.jobStatus && $scope.jobStatus.jobId) {
			$scope.startJobStatusPolling($scope.jobStatus.jobId);
			return;
		}

		$scope.disableButton = true;
		// Set the appropriate button to loading state based on submission type
		if ($scope.submissionType === 'bulk') {
			$('#send-bulk-claims-button').button('loading');
		} else {
			$('#send-single-claims-button').button('loading');
		}
		$scope.responseMessage = null;

		// Prepare request
		var request = {
			claimIds: claimIds,
			billingBranchId: null,
			submissionType: $scope.submissionType
		};

		// Send request
		ClaimFactory.sendClaimFilesPost(request).$promise.then(function (response) {
			console.log("Send claims response:", response);

			// Handle bulk submission
			if ($scope.submissionType === 'bulk' && response.jobId) {
				// For bulk submissions, start polling for job status
				$scope.jobStatus = response;
				$scope.startJobStatusPolling(response.jobId);
				$scope.responseMessage = "Bulk submission started. Processing claims...";
			}
			// Handle single submission with data property (string response)
			else if (response.data) {
				// For single submissions, show success message
				$scope.responseMessage = "Claims sent successfully.";
				// Wait a moment to show the success message before closing
				setTimeout(function() {
					$uibModalInstance.close(response);
				}, 1500);
			}
			// Handle single submission with success property (JSON response)
			else if (response.success) {
				$scope.responseMessage = response.data || "Claims sent successfully.";
				// Wait a moment to show the success message before closing
				setTimeout(function() {
					$uibModalInstance.close(response);
				}, 1500);
			}
			// Handle unexpected response format
			else {
				$scope.responseMessage = "Claims processed. Please check status in the claims list.";
				// Wait a moment to show the message before closing
				setTimeout(function() {
					$uibModalInstance.close(response);
				}, 1500);
			}
		}, function (error) {
			console.error("Error sending claims:", error);

			// Try to extract a meaningful error message
			var errorMessage = "";
			if (error.data) {
				if (typeof error.data === 'object' && error.data.error) {
					errorMessage = error.data.error;
				} else if (typeof error.data === 'object' && error.data.data) {
					errorMessage = error.data.data;
				} else if (typeof error.data === 'string') {
					errorMessage = error.data;
				}
			} else if (error.statusText) {
				errorMessage = error.statusText;
			}

			// Check for database schema errors
			if (errorMessage.includes("Unknown column") ||
				(errorMessage.includes("Table") && errorMessage.includes("doesn't exist"))) {
				errorMessage = "Database schema error. Please contact your system administrator. Error: " + errorMessage;
			}

			// Check for tenant context errors
			if (errorMessage.includes("Datasource is not found") ||
				errorMessage.includes("tenant is probably not set to active")) {
				errorMessage = "System error: Tenant context issue. Please contact support.";
			}

			$scope.responseMessage = "Sending claims failed. " + errorMessage;
			UtilService.displayAlert("danger", "<p>Sending claims failed.</p>" + $sce.trustAsHtml(errorMessage), "header-alert-container");
			// Reset the appropriate button based on submission type
			if ($scope.submissionType === 'bulk') {
				$('#send-bulk-claims-button').button('reset');
			} else {
				$('#send-single-claims-button').button('reset');
			}
			$scope.disableButton = false;
		});
	};

	// Start polling for job status
	$scope.startJobStatusPolling = function(jobId) {
		// Cancel any existing interval
		if ($scope.jobStatusInterval) {
			$interval.cancel($scope.jobStatusInterval);
		}

		// Start a new interval
		$scope.jobStatusInterval = $interval(function() {
			ClaimFactory.getBulkJobStatus({jobId: jobId}).$promise.then(function(status) {
				console.log("Job status response:", status);

				// Ensure we have a valid status object
				if (!status) {
					status = {
						jobId: jobId,
						status: "UNKNOWN",
						totalClaims: 0,
						processedClaims: 0,
						successfulClaims: 0,
						failedClaims: 0,
						completed: false
					};
				}

				// Store the status in the scope
				$scope.jobStatus = status;

				// Update response message based on status
				if (status.status === 'COMPLETED') {
					$scope.responseMessage = "Bulk submission completed successfully. " +
						(status.successfulClaims || 0) + " claims processed.";

					// Mark as completed if not already
					if (!status.completed) {
						status.completed = true;
					}
				} else if (status.status === 'FAILED') {
					$scope.responseMessage = "Bulk submission failed: " + (status.errorMessage || "Unknown error");

					// Mark as completed if not already
					if (!status.completed) {
						status.completed = true;
					}
				} else {
					$scope.responseMessage = "Processing claims... " +
						(status.processedClaims || 0) + " of " + (status.totalClaims || 0) + " processed.";
				}

				// If job is completed or failed, stop polling
				if (status.completed || status.status === 'COMPLETED' || status.status === 'FAILED') {
					$interval.cancel($scope.jobStatusInterval);
					// Reset the refresh status button
					$('#refresh-status-button').button('reset');
					$scope.disableButton = false;

					// Refresh the claims list after completion
					if ($scope.$parent && $scope.$parent.refreshClaimsList) {
						$scope.$parent.refreshClaimsList();
					}
				}
			}, function(error) {
				console.error("Error polling job status:", error);

				// Try to extract a meaningful error message
				var errorMessage = "";
				if (error.data) {
					if (typeof error.data === 'object' && error.data.error) {
						errorMessage = error.data.error;
					} else if (typeof error.data === 'object' && error.data.data) {
						errorMessage = error.data.data;
					} else if (typeof error.data === 'string') {
						errorMessage = error.data;
					}
				} else if (error.statusText) {
					errorMessage = error.statusText;
				}

				// Check for database schema errors
				if (errorMessage.includes("Unknown column") ||
					(errorMessage.includes("Table") && errorMessage.includes("doesn't exist"))) {
					errorMessage = "Database schema error. Please contact your system administrator.";
				}

				// Check for tenant context errors
				if (errorMessage.includes("Datasource is not found") ||
					errorMessage.includes("tenant is probably not set to active")) {
					errorMessage = "System error: Tenant context issue. Please contact support.";
				}

				// Create a default status object for error case
				$scope.jobStatus = $scope.jobStatus || {
					jobId: jobId,
					status: "FAILED",
					errorMessage: errorMessage || "Unknown error",
					completed: true
				};

				$scope.responseMessage = "Error checking job status: " + errorMessage;
				$interval.cancel($scope.jobStatusInterval);
				// Reset the refresh status button
				$('#refresh-status-button').button('reset');
				$scope.disableButton = false;
			});
		}, 2000); // Poll every 2 seconds
	};

	// Calculate progress percentage
	$scope.getProgressPercentage = function() {
		if (!$scope.jobStatus || !$scope.jobStatus.totalClaims || $scope.jobStatus.totalClaims <= 0) {
			return 0;
		}

		// Ensure we have valid numbers
		var processed = parseInt($scope.jobStatus.processedClaims) || 0;
		var total = parseInt($scope.jobStatus.totalClaims) || 1; // Avoid division by zero

		// Calculate percentage and ensure it's between 0 and 100
		var percentage = Math.round((processed / total) * 100);
		return Math.min(Math.max(percentage, 0), 100);
	};

	// Close the modal
	$scope.close = function () {
		// Cancel any polling interval
		if ($scope.jobStatusInterval) {
			$interval.cancel($scope.jobStatusInterval);
		}
		$uibModalInstance.close($scope.jobStatus);
	};

	// Clean up when the scope is destroyed
	$scope.$on('$destroy', function() {
		if ($scope.jobStatusInterval) {
			$interval.cancel($scope.jobStatusInterval);
		}
	});
}
