app.factory('DownloadFactory', DownloadFactory);
DownloadFactory.$inject = ['$resource'];

function DownloadFactory($resource) {
	return $resource('api/download', {}, {
		download: {
			url: 'api/download/x12/837/claim/:claimId',
			method: 'GET',
			params: {
				claimId: '@_claimId',
				download: 'true'
			},
			transformResponse: function (data, header, status, config, statusText) {
				return {
					data: data.toString(),
					header: header(),
					status: status,
					config: config,
					statusText: statusText
				}
			}
		},
		downloadBulkX12: {
			url: 'api/download/x12/837/bulk/:jobId',
			method: 'GET',
			params: {
				jobId: '@_jobId',
				download: 'true'
			},
			transformResponse: function (data, header, status, config, statusText) {
				return {
					data: data.toString(),
					header: header(),
					status: status,
					config: config,
					statusText: statusText
				}
			}
		}
	});
}
