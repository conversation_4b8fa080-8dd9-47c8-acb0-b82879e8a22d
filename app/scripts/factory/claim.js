app.factory('ClaimFactory', ClaimFactory);
ClaimFactory.$inject = ['$resource', '$window'];

function ClaimFactory($resource, $window) {
	// Helper function to log JSON payload for debugging
	function logJsonPayload(url, data, error) {
		if (typeof data !== 'string') {
			console.log('Data is not a string, type:', typeof data);
			return;
		}

		if (data.trim() === '') {
			console.log('Empty data received from:', url);
			return;
		}

		console.log('JSON Payload URL:', url);
		console.log('JSON Payload Length:', data.length);

		// Log the first 100 characters to help with debugging
		console.log('JSON Payload Start:', data.substring(0, 100) + '...');

		if (error) {
			console.error('JSON Parse Error:', error.message);

			// Extract error position if available
			var position = error.message.match(/position (\d+)/);
			if (position && position[1]) {
				var pos = parseInt(position[1], 10);
				console.error('Error Position:', pos);

				// Extract a segment around the error position
				var start = Math.max(0, pos - 100);
				var end = Math.min(data.length, pos + 100);
				console.error('JSON around error:', data.substring(start, end));

				// Try to identify the problematic field
				var fieldMatch = data.substring(Math.max(0, pos - 50), pos).match(/"([^"]+)"\s*:/);
				if (fieldMatch && fieldMatch[1]) {
					console.error('Problematic field might be:', fieldMatch[1]);
				}
			}

			// Save to localStorage for later analysis
			try {
				var key = 'jsonPayload_' + new Date().getTime();
				$window.localStorage.setItem(key, data);
				console.log('Saved JSON payload to localStorage with key:', key);
			} catch (e) {
				console.error('Failed to save to localStorage:', e);
			}
		}
	}
	return $resource('api/claim/:id', {id: '@_id'}, {
    findByPatientId: {
      url: 'api/claim/patient/:patientId',
      method: 'GET',
      isArray: true,
      params: {
        patientId: '@_patientId'
      }
    },
		findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId: {
      url: 'api/claim/updateAt/totalClaimBalance/totalPtResponsibilityBalance',
      method: 'GET',
      isArray: true
    },
		findClaimsWithNoActivityDashboard: {
			url: 'api/claim/updateAt/totalClaimBalance/totalPtResponsibilityBalanceDto',
			method: 'GET',
			isArray: true
	},
    findByPrescriptionId: {
      url: 'api/claim/prescription/:prescriptionId',
      method: 'GET',
      isArray: true,
      params: {
		  patientId: '@_prescriptionId'
	  }
	},
		findByIdWithForeignKeys: {
			url: 'api/claim/view/:id',
			method: 'GET',
			params: {
				id: '@_id'
			}
		},
		getClaimTotals: {
			url: 'api/claim/totals/:rxId/:claimId',
			method: 'GET',
			params: {
				claimId: '@_claimId',
				rxId: '@_rxId'
			}
		},
		findAutoPostClaimResponsesById: {
			url: 'api/claim/autopostclaimresponses/:id',
			method: 'GET',
			isArray: true,
			params: {
				id: '@_id'
			}
		},
		addClaim: {
			url: 'api/claim/add/:prescriptionId',
			method: 'GET',
			params: {
				prescriptionId: '@_prescriptionId',
				dateOfService: '#',
				billingBranchId: '@_billingBranchId',
				resend: '@_resend'
			}
		},
		addClaimPost: {
			url: 'api/claim/add',
			method: 'POST'
		},
		search: {
			url: 'api/claim/search?&page=0&size=1000',
			method: 'GET',
			isArray: true
		},

		sendClaimFilesPost: {
			url: 'api/claim/send-files',
			method: 'POST',
			transformResponse: function (data) {
				// Get the URL from the config if available
				var url = this && this.url ? this.url : 'api/claim/send-files';

				// If data is empty, return a default response
				if (!data) {
					return {
						success: false,
						error: "No response received"
					};
				}

				// Log the response for debugging
				logJsonPayload(url, data);

				// If data is already a JSON object, return it
				if (typeof data === 'object') {
					return data;
				}

				// If data is a string but doesn't look like JSON, return it as a success message
				if (typeof data === 'string' && !data.includes('{') && !data.includes('[')) {
					return {
						success: true,
						data: data.toString()
					};
				}

				// Extract data directly using regex for the most common fields
				if (typeof data === 'string' && (data.includes('{') || data.includes('['))) {
					console.log("Extracting data directly from response string");

					var extractedData = {
						success: true,
						jobId: null,
						status: null,
						error: null,
						data: null
					};

					// Extract success field
					var successMatch = data.match(/"success"\s*:\s*(true|false)/);
					if (successMatch && successMatch[1]) {
						extractedData.success = successMatch[1] === 'true';
					}

					// Extract jobId field
					var jobIdMatch = data.match(/"jobId"\s*:\s*"([^"]+)"/);
					if (jobIdMatch && jobIdMatch[1]) {
						extractedData.jobId = jobIdMatch[1];
					}

					// Extract status field
					var statusMatch = data.match(/"status"\s*:\s*"([^"]+)"/);
					if (statusMatch && statusMatch[1]) {
						extractedData.status = statusMatch[1];
					}

					// Extract error field
					var errorMatch = data.match(/"error"\s*:\s*"([^"]+)"/);
					if (errorMatch && errorMatch[1]) {
						extractedData.error = errorMatch[1];
					} else {
						// Try without quotes
						errorMatch = data.match(/"error"\s*:\s*([^,}]+)/);
						if (errorMatch && errorMatch[1]) {
							extractedData.error = errorMatch[1].trim();
						}
					}

					// Extract data field
					var dataMatch = data.match(/"data"\s*:\s*"([^"]+)"/);
					if (dataMatch && dataMatch[1]) {
						extractedData.data = dataMatch[1];
					} else {
						// Try without quotes
						dataMatch = data.match(/"data"\s*:\s*([^,}]+)/);
						if (dataMatch && dataMatch[1]) {
							extractedData.data = dataMatch[1].trim();
						}
					}

					console.log("Extracted data:", extractedData);
					return extractedData;
				}

				// If all else fails, return a generic success response with the data as a string
				return {
					success: true,
					data: data ? data.toString() : null
				};
			}
		},
		validateBulkSubmission: {
			url: 'api/claim/validate-bulk-submission',
			method: 'POST'
		},
		getBulkJobStatus: {
			url: 'api/claim/bulk-job-status/:jobId',
			method: 'GET',
			params: {
				jobId: '@_jobId'
			},
			transformResponse: function (data) {
				// Get the URL from the config if available
				var url = this && this.url ? this.url : 'api/claim/bulk-job-status';

				// If data is empty, return a default response
				if (!data) {
					return {
						jobId: null,
						status: "UNKNOWN",
						totalClaims: 0,
						processedClaims: 0,
						successfulClaims: 0,
						failedClaims: 0,
						completed: false,
						errorMessage: "No response received"
					};
				}

				// Log the response for debugging
				logJsonPayload(url, data);

				// Skip JSON parsing entirely and extract the fields directly
				// This is the most reliable approach for malformed JSON
				var extractedData = {
					jobId: null,
					status: "UNKNOWN",
					totalClaims: 0,
					processedClaims: 0,
					successfulClaims: 0,
					failedClaims: 0,
					currentPayer: null,
					errorMessage: null,
					completed: false
				};

				// Use regex to extract each field individually
				if (typeof data === 'string') {
					console.log("Extracting data directly from response string");

					var jobIdMatch = data.match(/"jobId"\s*:\s*"([^"]+)"/);
					if (jobIdMatch && jobIdMatch[1]) extractedData.jobId = jobIdMatch[1];

					var statusMatch = data.match(/"status"\s*:\s*"([^"]+)"/);
					if (statusMatch && statusMatch[1]) extractedData.status = statusMatch[1];

					var totalClaimsMatch = data.match(/"totalClaims"\s*:\s*(\d+)/);
					if (totalClaimsMatch && totalClaimsMatch[1]) extractedData.totalClaims = parseInt(totalClaimsMatch[1], 10);

					var processedClaimsMatch = data.match(/"processedClaims"\s*:\s*(\d+)/);
					if (processedClaimsMatch && processedClaimsMatch[1]) extractedData.processedClaims = parseInt(processedClaimsMatch[1], 10);

					var successfulClaimsMatch = data.match(/"successfulClaims"\s*:\s*(\d+)/);
					if (successfulClaimsMatch && successfulClaimsMatch[1]) extractedData.successfulClaims = parseInt(successfulClaimsMatch[1], 10);

					var failedClaimsMatch = data.match(/"failedClaims"\s*:\s*(\d+)/);
					if (failedClaimsMatch && failedClaimsMatch[1]) extractedData.failedClaims = parseInt(failedClaimsMatch[1], 10);

					var currentPayerMatch = data.match(/"currentPayer"\s*:\s*"([^"]+)"/);
					if (currentPayerMatch && currentPayerMatch[1]) extractedData.currentPayer = currentPayerMatch[1];

					var errorMessageMatch = data.match(/"errorMessage"\s*:\s*"([^"]+)"/);
					if (errorMessageMatch && errorMessageMatch[1]) extractedData.errorMessage = errorMessageMatch[1];

					var completedMatch = data.match(/"completed"\s*:\s*(true|false)/);
					if (completedMatch && completedMatch[1]) extractedData.completed = completedMatch[1] === 'true';

					console.log("Extracted data:", extractedData);
					return extractedData;
				} else if (typeof data === 'object') {
					// If data is already an object, return it
					return data;
				}

				// If all else fails, return the default object
				return extractedData;
			}
		},
		sort: {
			url: '/api/claim/search?&page=:pageNumber&size=30&sort=:columnName,:sortDirection',
			method: 'GET',
			params: {
				pageNumber: '@_pageNumber',
				columnName: '@_columnName',
				sortDirection: '@_sortDirection'
			}
		},
		exportClaims: {
			url: '/api/claim/export-claims?&page=:pageNumber&size=30&sort=:columnName,:sortDirection',
			method: 'GET',
			params: {
				pageNumber: '@_pageNumber',
				columnName: '@_columnName',
				sortDirection: '@_sortDirection'
			},
			transformResponse: function (data) {
				return {data: data.toString()}
			}
		},
		viewClaimAll: {
			url: 'api/claim/view-files/all',
			method: 'GET',
			isArray: false,
			params: {
				claimId: '@_claimId',
				billingBranchId: '@_billingBranchId',
				patientInsuranceId: '@_patientInsuranceId',
				otherPatientInsuranceId: '@_otherPatientInsuranceId',
				download: 'true'
			}
		},
		viewClaimFull: {
			url: 'api/claim/view-files/full',
			method: 'GET',
			isArray: false,
			params: {
				claimId: '@_claimId',
				billingBranchId: '@_billingBranchId',
				patientInsuranceId: '@_patientInsuranceId',
				otherPatientInsuranceId: '@_otherPatientInsuranceId',
				download: 'true'
			}
		},
		viewClaimBlank: {
			url: 'api/claim/view-files/blank',
			method: 'GET',
			isArray: false,
			params: {
				claimId: '@_claimId',
				billingBranchId: '@_billingBranchId',
				patientInsuranceId: '@_patientInsuranceId',
				otherPatientInsuranceId: '@_otherPatientInsuranceId',
				download: 'true'
			}
		},
		bulkUpdateStatus: {
			url: 'api/claim/bulk-update-status',
			method: 'GET'
		},
		reassignClaims: {
			url: 'api/claim/reassign-claim',
			method: 'GET'
		},
		patientStatements: {
			url: 'api/claim/statements/patients?&page=:pageNumber&size=:pageSize&sort=:columnName,:sortDirection',
			method: 'GET',
			params: {
				pageNumber: '@_page',
				pageSize: '@_pageSize',
				columnName: '@_columnName',
				sortDirection: '@_sortDirection'
			}
		},
		updatePatientExportDates: {
			url: 'api/claim/export-dates',
			method: 'POST',
			isArray: true
		},
		convertStatementToXML: {
			url: 'api/claim/statements/patients/export',
			method: 'POST',
			transformResponse: function (data) {
				return {data: data.toString()}
			}
		},
		exportStatementsSentToCollections: {
			url: 'api/claim/statements/collections/export',
			method: 'POST',
			transformResponse: function (data) {
				return {data: data.toString()}
			}
		},
		profileLoad: {
			url: 'api/claim/profile/:patientId',
			method: 'GET',
			isArray: true,
			params: {
				patientId: '@_patientId'
			}
		},
		getProjectedVsBilled: {
			url: 'api/claim/projected-vs-billed',
			method: 'GET',
			isArray: true
		},
		deleteClaim: {
			url: 'api/claim/delete',
			method: 'POST'
		},
		updatePhysicians: {
			url: 'api/claim/update/physicians',
			method: 'POST'
		},
		loadTransactionHistoryByClaimId: {
			url: 'api/claim/transaction-history/claim/:claimId',
			method: 'GET',
			isArray: true,
			params: {
				claimId: '@_claimId'
			}
		},
		loadTransactionHistoryByPrescriptionId: {
			url: 'api/claim/transaction-history/prescription/:prescriptionId',
			method: 'GET',
			isArray: true,
			params: {
				prescriptionId: '@_prescriptionId'
			}
		}
	});
}
