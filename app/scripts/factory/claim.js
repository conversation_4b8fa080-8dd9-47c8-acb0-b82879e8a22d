app.factory('ClaimFactory', ClaimFactory);
ClaimFactory.$inject = ['$resource'];

function ClaimFactory($resource) {
	return $resource('api/claim/:id', {id: '@_id'}, {
    findByPatientId: {
      url: 'api/claim/patient/:patientId',
      method: 'GET',
      isArray: true,
      params: {
        patientId: '@_patientId'
      }
    },
		findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId: {
      url: 'api/claim/updateAt/totalClaimBalance/totalPtResponsibilityBalance',
      method: 'GET',
      isArray: true
    },
		findClaimsWithNoActivityDashboard: {
			url: 'api/claim/updateAt/totalClaimBalance/totalPtResponsibilityBalanceDto',
			method: 'GET',
			isArray: true
	},
    findByPrescriptionId: {
      url: 'api/claim/prescription/:prescriptionId',
      method: 'GET',
      isArray: true,
      params: {
		  patientId: '@_prescriptionId'
	  }
	},
		findByIdWithForeignKeys: {
			url: 'api/claim/view/:id',
			method: 'GET',
			params: {
				id: '@_id'
			}
		},
		getClaimTotals: {
			url: 'api/claim/totals/:rxId/:claimId',
			method: 'GET',
			params: {
				claimId: '@_claimId',
				rxId: '@_rxId'
			}
		},
		findAutoPostClaimResponsesById: {
			url: 'api/claim/autopostclaimresponses/:id',
			method: 'GET',
			isArray: true,
			params: {
				id: '@_id'
			}
		},
		addClaim: {
			url: 'api/claim/add/:prescriptionId',
			method: 'GET',
			params: {
				prescriptionId: '@_prescriptionId',
				dateOfService: '#',
				billingBranchId: '@_billingBranchId',
				resend: '@_resend'
			}
		},
		addClaimPost: {
			url: 'api/claim/add',
			method: 'POST'
		},
		search: {
			url: 'api/claim/search?&page=0&size=1000',
			method: 'GET',
			isArray: true
		},

		sendClaimFilesPost: {
			url: 'api/claim/send-files',
			method: 'POST',
			transformResponse: function (data) {
				// If data is empty, return a default response
				if (!data) {
					return {
						success: false,
						error: "No response received"
					};
				}

				// If data is already a JSON object, return it (Angular already parsed it)
				if (typeof data === 'object') {
					return data;
				}

				// If data is a string but doesn't look like JSON, return it as a success message
				if (typeof data === 'string' && !data.includes('{') && !data.includes('[')) {
					return {
						success: true,
						data: data.toString()
					};
				}

				// Try to parse JSON using native JSON.parse
				if (typeof data === 'string') {
					try {
						return JSON.parse(data);
					} catch (e) {
						// If JSON parsing fails, return the data as a string response
						return {
							success: true,
							data: data.toString()
						};
					}
				}

				// Fallback: return the data as is
				return data;
			}
		},
		validateBulkSubmission: {
			url: 'api/claim/validate-bulk-submission',
			method: 'POST'
		},
		getBulkJobStatus: {
			url: 'api/claim/bulk-job-status/:jobId',
			method: 'GET',
			params: {
				jobId: '@_jobId'
			},
			transformResponse: function (data) {
				// If data is empty, return a default response
				if (!data) {
					return {
						jobId: null,
						status: "UNKNOWN",
						totalClaims: 0,
						processedClaims: 0,
						successfulClaims: 0,
						failedClaims: 0,
						completed: false,
						errorMessage: "No response received"
					};
				}

				// If data is already an object, return it (Angular already parsed it)
				if (typeof data === 'object') {
					return data;
				}

				// Try to parse JSON using native JSON.parse
				if (typeof data === 'string') {
					try {
						return JSON.parse(data);
					} catch (e) {
						// If JSON parsing fails, return a default response
						return {
							jobId: null,
							status: "UNKNOWN",
							totalClaims: 0,
							processedClaims: 0,
							successfulClaims: 0,
							failedClaims: 0,
							currentPayer: null,
							errorMessage: "JSON parsing failed",
							completed: false
						};
					}
				}

				// Fallback: return the data as is
				return data;
			}
		},
		sort: {
			url: '/api/claim/search?&page=:pageNumber&size=30&sort=:columnName,:sortDirection',
			method: 'GET',
			params: {
				pageNumber: '@_pageNumber',
				columnName: '@_columnName',
				sortDirection: '@_sortDirection'
			}
		},
		exportClaims: {
			url: '/api/claim/export-claims?&page=:pageNumber&size=30&sort=:columnName,:sortDirection',
			method: 'GET',
			params: {
				pageNumber: '@_pageNumber',
				columnName: '@_columnName',
				sortDirection: '@_sortDirection'
			},
			transformResponse: function (data) {
				return {data: data.toString()}
			}
		},
		viewClaimAll: {
			url: 'api/claim/view-files/all',
			method: 'GET',
			isArray: false,
			params: {
				claimId: '@_claimId',
				billingBranchId: '@_billingBranchId',
				patientInsuranceId: '@_patientInsuranceId',
				otherPatientInsuranceId: '@_otherPatientInsuranceId',
				download: 'true'
			}
		},
		viewClaimFull: {
			url: 'api/claim/view-files/full',
			method: 'GET',
			isArray: false,
			params: {
				claimId: '@_claimId',
				billingBranchId: '@_billingBranchId',
				patientInsuranceId: '@_patientInsuranceId',
				otherPatientInsuranceId: '@_otherPatientInsuranceId',
				download: 'true'
			}
		},
		viewClaimBlank: {
			url: 'api/claim/view-files/blank',
			method: 'GET',
			isArray: false,
			params: {
				claimId: '@_claimId',
				billingBranchId: '@_billingBranchId',
				patientInsuranceId: '@_patientInsuranceId',
				otherPatientInsuranceId: '@_otherPatientInsuranceId',
				download: 'true'
			}
		},
		bulkUpdateStatus: {
			url: 'api/claim/bulk-update-status',
			method: 'GET'
		},
		reassignClaims: {
			url: 'api/claim/reassign-claim',
			method: 'GET'
		},
		patientStatements: {
			url: 'api/claim/statements/patients?&page=:pageNumber&size=:pageSize&sort=:columnName,:sortDirection',
			method: 'GET',
			params: {
				pageNumber: '@_page',
				pageSize: '@_pageSize',
				columnName: '@_columnName',
				sortDirection: '@_sortDirection'
			}
		},
		updatePatientExportDates: {
			url: 'api/claim/export-dates',
			method: 'POST',
			isArray: true
		},
		convertStatementToXML: {
			url: 'api/claim/statements/patients/export',
			method: 'POST',
			transformResponse: function (data) {
				return {data: data.toString()}
			}
		},
		exportStatementsSentToCollections: {
			url: 'api/claim/statements/collections/export',
			method: 'POST',
			transformResponse: function (data) {
				return {data: data.toString()}
			}
		},
		profileLoad: {
			url: 'api/claim/profile/:patientId',
			method: 'GET',
			isArray: true,
			params: {
				patientId: '@_patientId'
			}
		},
		getProjectedVsBilled: {
			url: 'api/claim/projected-vs-billed',
			method: 'GET',
			isArray: true
		},
		deleteClaim: {
			url: 'api/claim/delete',
			method: 'POST'
		},
		updatePhysicians: {
			url: 'api/claim/update/physicians',
			method: 'POST'
		},
		loadTransactionHistoryByClaimId: {
			url: 'api/claim/transaction-history/claim/:claimId',
			method: 'GET',
			isArray: true,
			params: {
				claimId: '@_claimId'
			}
		},
		loadTransactionHistoryByPrescriptionId: {
			url: 'api/claim/transaction-history/prescription/:prescriptionId',
			method: 'GET',
			isArray: true,
			params: {
				prescriptionId: '@_prescriptionId'
			}
		}
	});
}
