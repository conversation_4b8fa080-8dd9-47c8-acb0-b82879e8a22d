<script type="text/ng-template" id="_send_claims_to_billing.html" xmlns="http://www.w3.org/1999/html">
    <form name="sendClaimsToBillingForm" id="sendClaimsToBillingForm" class="ajax-crud-form" novalidate modal-movable>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="close()">
                    <i class="fa fa-times"></i>
                </button>
                <h4 class="modal-title">Submit Claims</h4>
            </div>
            <div class="modal-body bg-body">
                <!-- Initial submission view -->
                <div class="row mt-20 pl-15" ng-if="!jobStatus">
                    <h5 ng-if="claims.length === 1">"Are you sure this Claim is ready to be submitted?" Please click "Submit" to submit Claim File.
                        Click "Cancel" to go back.</h5>
                    <h5 ng-if="claims.length > 1 && enableBulkClaims && userService.hasPermission('bulk_claim_submit')">Are you sure you'd like to submit these claims? Click "Send Bulk Claims" to send a
                        single claim file to each payer. Click "Send Single Claims" to send multiple claim files
                        to each payer. Click "Cancel" if you do not wish to continue.</h5>
                    <h5 ng-if="claims.length > 1 && (!enableBulkClaims || !userService.hasPermission('bulk_claim_submit'))">Are you sure you'd like to submit these claims? Click "Send Single Claims" to send multiple claim files
                        to each payer. Click "Cancel" if you do not wish to continue.</h5>

                    <!-- No hidden field needed - submission type is set in the controller and when buttons are clicked -->

                    <!-- Bulk validation errors - only show when bulk claims are enabled and user has permission -->
                    <div class="row mb-10" ng-if="bulkValidationErrors.length > 0 && enableBulkClaims && userService.hasPermission('bulk_claim_submit')">
                        <div class="col-sm-12">
                            <div class="alert alert-danger">
                                <strong>Cannot submit claims in bulk:</strong>
                                <ul>
                                    <li ng-repeat="error in bulkValidationErrors">{{error}}</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk submission info - only show when bulk claims are enabled -->
                    <div class="row mb-10" ng-if="canSubmitBulk()">
                        <div class="col-sm-12">
                            <div class="alert alert-info">
                                <strong>Bulk Submission:</strong> All selected claims will be submitted together in a single file.
                                This can improve processing efficiency for claims going to the same payer.
                            </div>
                        </div>
                    </div>

                    <!-- Claims table -->
                    <div class="alt-table-responsive">
                        <table class="table table-condensed panel fs-12 b-solid b-primary b-2x">
                            <thead class="bg-primary">
                            <tr>
                                <th class="w-90">Claim ID</th>
                                <th class="w-90">Prescription ID</th>
                                <th class="w-100">Date of Service</th>
                            </tr>
                            </thead>
                            <tbody id="send-claims-to-billing-table-body">
                            <tr ng-show="claims.length" ng-repeat="claim in claims">
                                <td>{{claim.id}}</td>
                                <td>{{claim.prescriptionId}}</td>
                                <td>{{moment(claim.dateOfService).format('MM/DD/YYYY') }}</td>
                            </tr>
                            <tr ng-show="!claims.length">
                                <td colspan="3">No Claims Selected to Send.</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Job status view for bulk submissions -->
                <div class="row mt-20 pl-15" ng-if="jobStatus">
                    <div class="col-sm-12">
                        <h4>Bulk Claim Submission Status</h4>

                        <!-- Progress bar -->
                        <div class="progress">
                            <div class="progress-bar" role="progressbar"
                                 ng-class="{'progress-bar-success': jobStatus.status === 'COMPLETED', 'progress-bar-danger': jobStatus.status === 'FAILED'}"
                                 aria-valuenow="{{getProgressPercentage()}}" aria-valuemin="0" aria-valuemax="100"
                                 ng-style="{'width': getProgressPercentage() + '%'}">
                                {{getProgressPercentage()}}%
                            </div>
                        </div>

                        <!-- Status details -->
                        <div class="row mt-10">
                            <div class="col-sm-6">
                                <p><strong>Status:</strong> {{jobStatus.status}}</p>
                                <p><strong>Total Claims:</strong> {{jobStatus.totalClaims}}</p>
                                <p><strong>Processed Claims:</strong> {{jobStatus.processedClaims}}</p>
                            </div>
                            <div class="col-sm-6">
                                <p><strong>Successful Claims:</strong> {{jobStatus.successfulClaims}}</p>
                                <p><strong>Failed Claims:</strong> {{jobStatus.failedClaims}}</p>
                                <p ng-if="jobStatus.currentPayer"><strong>Current Payer:</strong> {{jobStatus.currentPayer}}</p>
                            </div>
                        </div>

                        <!-- Error message -->
                        <div class="row mt-10" ng-if="jobStatus.errorMessage">
                            <div class="col-sm-12">
                                <div class="alert alert-danger">
                                    <strong>Error:</strong> {{jobStatus.errorMessage}}
                                </div>
                            </div>
                        </div>

                        <!-- Completion message removed to avoid duplication with footer message -->
                    </div>
                </div>
            </div>
            <footer ng-if="responseMessage" class="alert"
                ng-class="{
                    'alert-success': jobStatus && jobStatus.status === 'COMPLETED',
                    'alert-danger': jobStatus && jobStatus.status === 'FAILED',
                    'alert-info': !jobStatus || (jobStatus && jobStatus.status !== 'COMPLETED' && jobStatus.status !== 'FAILED')
                }">
                {{responseMessage}}
            </footer>
            <div class="modal-footer">
                <!-- Job status view buttons -->
                <div ng-if="jobStatus" class="row">
                    <div class="col-sm-12 text-right">
                        <button class="btn btn-rounded btn-sm"
                                ng-class="{'btn-default': jobStatus.completed || jobStatus.status === 'COMPLETED' || jobStatus.status === 'FAILED', 'btn-danger': !jobStatus.completed && jobStatus.status !== 'COMPLETED' && jobStatus.status !== 'FAILED'}"
                                ng-click="close()">
                            {{(jobStatus.completed || jobStatus.status === 'COMPLETED' || jobStatus.status === 'FAILED') ? 'Close' : 'Cancel'}}
                        </button>
                        <button type="button"
                                class="btn btn-sm btn-rounded"
                                ng-class="{'btn-info': !jobStatus.completed && jobStatus.status !== 'COMPLETED' && jobStatus.status !== 'FAILED', 'btn-primary': jobStatus.completed || jobStatus.status === 'COMPLETED' || jobStatus.status === 'FAILED'}"
                                id="refresh-status-button"
                                name="refresh-status-button"
                                ng-disabled="jobStatus.completed || jobStatus.status === 'COMPLETED' || jobStatus.status === 'FAILED'"
                                ng-click="sendClaimsToBilling()"
                                data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
                            <span ng-if="!jobStatus.completed && jobStatus.status !== 'COMPLETED' && jobStatus.status !== 'FAILED'">Refresh Status</span>
                            <span ng-if="jobStatus.completed || jobStatus.status === 'COMPLETED' || jobStatus.status === 'FAILED'">Done</span>
                        </button>
                    </div>
                </div>

                <!-- Initial view buttons -->
                <div ng-if="!jobStatus" class="row">
                    <div class="col-sm-12 text-right">
                        <!-- Send Bulk Claims button - only visible when bulk claims are enabled and user has permission -->
                        <button type="button"
                                class="btn btn-sm btn-rounded btn-warning"
                                id="send-bulk-claims-button"
                                name="send-bulk-claims-button"
                                ng-if="enableBulkClaims && userService.hasPermission('bulk_claim_submit')"
                                ng-disabled="!disableButton || claims.length < 2 || !canSubmitBulk()"
                                ng-click="setSubmissionTypeAndSend('bulk')"
                                data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
                            Send Bulk Claims
                        </button>

                        <!-- Send Single Claims button - always enabled -->
                        <button type="button"
                                class="btn btn-sm btn-rounded btn-success"
                                id="send-single-claims-button"
                                name="send-single-claims-button"
                                ng-click="setSubmissionTypeAndSend('single')"
                                ng-disabled="!disableButton"
                                data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
                            Send Single Claims
                        </button>

                        <!-- Cancel button -->
                        <button type="button" class="btn btn-rounded btn-sm btn-danger" ng-click="close()">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</script>
