<legend class="form-section-header">Billing Setup</legend>
<div class="row">
    <div class="col-sm-2">
        <div class="col-sm-12 form-group"
             ng-class="{ 'has-error' : submitted && systemSettingsForm.default_billing_fee_schedule.$invalid, 'has-success' : submitted && systemSettingsForm.default_billing_fee_schedule.$valid}">
            <label for="default_billing_fee_schedule">Default Billing Fee Schedule</label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.default_billing_fee_schedule.id"
                          showlabel="{{false}}"></audit-button>
            <div id="default_billing_fee_schedule-class-handler" class="select">
                <select name="default_billing_fee_schedule" id="default_billing_fee_schedule"
                        class="form-control input-sm chosen-select"
                        required ng-disabled="!editing" ng-model="billing.default_billing_fee_schedule.value"
                        ng-options="feeSchedule.id as (feeSchedule.isMedicare ? feeSchedule.name.toUpperCase()+'-'+feeSchedule.state.toUpperCase() : feeSchedule.name) for feeSchedule in feeSchedules | orderBy:['-isMedicare','name']">
                    <option value=""></option>
                </select>
            </div>
            <div ng-messages="systemSettingsForm.default_billing_fee_schedule.$error" role="alert" ng-show="submitted">
                <div class="help-block" ng-message="required">Default Billing Fee Schedule is required.</div>
            </div>
        </div>
        <div class="col-sm-12 form-group"
             ng-class="{ 'has-error' : submitted && systemSettingsForm.downpayment_percentage.$invalid, 'has-success' : submitted && systemSettingsForm.downpayment_percentage.$valid}">
            <label for="downpayment_percentage">Downpayment Percentage</label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.downpayment_percentage.id"
                          showlabel="{{false}}"></audit-button>
            <div id="downpayment-percentage-class-handler" class="input-group input-group-sm input-sm">
                <input id="downpayment_percentage" ng-model="billing.downpayment_percentage.value" type="number"
                       step="1"
                       name="downpayment_percentage" class="form-control" min="0" max="100"
                       placeholder="Downpayment Percentage" ng-disabled="!editing" required string-to-number>
                <span class="input-group-addon" style="border-radius: 0px 4px 4px 0px;">%</span>
            </div>
            <div ng-messages="systemSettingsForm.downpayment_percentage.$error" role="alert" ng-show="submitted">
                <div class="help-block" ng-message="required">Downpayment Percentage is required.</div>
            </div>
        </div>
        <div class="col-sm-12 form-group"
             ng-class="{ 'has-error' : submitted && systemSettingsForm.delivery_percentage.$invalid, 'has-success' : submitted && systemSettingsForm.delivery_percentage.$valid}">
            <label for="delivery_percentage">Delivery Percentage</label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.delivery_percentage.id"
                          showlabel="{{false}}"></audit-button>
            <div id="delivery-percentage-class-handler" class="input-group input-group-sm input-sm">
                <input id="delivery_percentage" ng-model="billing.delivery_percentage.value" type="number" step="1"
                       name="delivery_percentage" class="form-control" min="0" max="100"
                       placeholder="Delivery Percentage" ng-disabled="!editing" required string-to-number>
                <span class="input-group-addon" style="border-radius: 0px 4px 4px 0px;">%</span>
            </div>
            <div ng-messages="systemSettingsForm.delivery_percentage.$error" role="alert" ng-show="submitted">
                <div class="help-block" ng-message="required">Delivery Percentage is required.</div>
            </div>
        </div>
        <div class="col-sm-12 form-group"
             ng-class="{ 'has-error' : submitted && systemSettingsForm.delivery_percentage.$invalid, 'has-success' : submitted && systemSettingsForm.delivery_percentage.$valid}">
            <label for="autopost_percentage">Primary % Allowed >=</label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.autopost_percentage.id"
                          showlabel="{{false}}"></audit-button>
            <div id="auto-post-percentage-class-handler" class="input-group input-group-sm input-sm">
                <input id="autopost_percentage" ng-model="billing.autopost_percentage.value" type="number" step="1"
                       name="autopost_percentage" class="form-control" min="0" max="100"
                       placeholder="Auto-Post Percentage" ng-disabled="true" required string-to-number>
                <span class="input-group-addon" style="border-radius: 0px 4px 4px 0px;">%</span>
            </div>
            <div ng-messages="systemSettingsForm.autopost_percentage.$error" role="alert" ng-show="submitted">
                <div class="help-block" ng-message="required">Auto-Post Percentage is required.</div>
            </div>
        </div>
        <div class="col-sm-12 form-group"
             ng-class="{ 'has-error' : submitted && systemSettingsForm.default_clearing_house.$invalid, 'has-success' : submitted && systemSettingsForm.default_clearing_house.$valid}">
            <label for="autopost_percentage">Default Clearing House</label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.default_clearing_house.id"
                          showlabel="{{false}}"></audit-button>
            <div id="default_clearing_house-class-handler" class="select">
                <select name="default_clearing_house" id="default_clearing_house"
                        class="form-control input-sm chosen-select"
                        required ng-disabled="!editing || (editing && !authService.isSuperAdmin())" ng-model="billing.default_clearing_house.value"
                        ng-options="clearinghouse.id as clearinghouse.name for clearinghouse in clearinghouses">
                </select>
            </div>
            <div ng-messages="systemSettingsForm.default_clearing_house.$error" role="alert" ng-show="submitted">
                <div class="help-block" ng-message="required">Default Clearing House is required.</div>
            </div>
        </div>
    </div>
    <div class="col-sm-5">
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="auto_claim_follow_up" id="auto_claim_follow_up"
                       ng-disabled="!editing" ng-model="billing.auto_claim_follow_up.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Automatically generate follow up after sending claim to Waystar.
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.auto_claim_follow_up.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="print_patient_statement_on_payment_apply"
                       id="print_patient_statement_on_payment_apply"
                       ng-disabled="!editing" ng-model="billing.print_patient_statement_on_payment_apply.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                "Print Patient Statement" checked by default when applying payment.
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting"
                          ng-model="billing.print_patient_statement_on_payment_apply.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="sn_on_pod" id="sn_on_pod"
                       ng-disabled="!editing" ng-model="billing.sn_on_pod.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Include serial numbers of purchase order items included on Proof of Delivery receipt.
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.sn_on_pod.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="enable_autopost" id="enable_autopost"
                       ng-disabled="!editing" ng-model="billing.enable_autopost.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Enable Auto-Posting
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.enable_autopost.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="rural_non_rural_on" id="rural_non_rural_on"
                       ng-disabled="!editing" ng-model="billing.rural_non_rural_on.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Enable Rural and Non-Rural Fees
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.stripe_pay_on.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="enable_bulk_era" id="enable_bulk_era"
                       ng-disabled="!editing" ng-model="billing.enable_bulk_era.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Enable Bulk ERA processing
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.enable_bulk_era.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10"
             ng-class="{ 'has-error' : submitted && systemSettingsForm.useSingleClaim.$invalid, 'has-success' : submitted && systemSettingsForm.useSingleClaim.$valid}">
            <label class="checkbox-inline checkbox-custom pt-15">
                <input type="checkbox" name="useSingleClaim" id="useSingleClaim" ng-true-value="'Y'"
                       ng-false-value="'N'"
                       ng-disabled="!editing || (editing && !authService.isSuperAdmin())"
                       ng-model="claim.use_single_claim.value"><i></i>
                Use Single Claim
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="claim.use_single_claim.id"
                          showlabel="{{false}}"></audit-button>
        </div>
    </div>
    <div class="col-sm-5">
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="exclude_comment_export" id="patient_statement_needs_review"
                       ng-disabled="!editing" ng-model="billing.patient_statement_needs_review.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Additional review step before sending patient statement to collections.
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.patient_statement_needs_review.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="exclude_comment_export" id="auto_lock_wip"
                       ng-disabled="!editing" ng-model="billing.auto_lock_wip.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Auto Lock WIP Sections Upon Send to Billing.
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.auto_lock_wip.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="stripe_pay_on" id="stripe_pay_on"
                       ng-disabled="!editing" ng-model="billing.stripe_pay_on.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Stripe Pay Active
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.stripe_pay_on.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom">
                <input type="checkbox" name="display_biller_code_field" id="display_biller_code_field"
                       ng-disabled="!editing" ng-model="billing.display_biller_code_field.value"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"><i></i>
                Display Biller Code Field on Insurance Companies
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.display_biller_code_field.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10"
             ng-class="{ 'has-error' : submitted && systemSettingsForm.useRentalAutoBill.$invalid, 'has-success' : submitted && systemSettingsForm.useRentalAutoBill.$valid}">
            <label class="checkbox-inline checkbox-custom pt-15">
                <input type="checkbox" name="useRentalAutoBill" id="useRentalAutoBill"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"
                       ng-disabled="!editing || (editing && !authService.isSuperAdmin())"
                       ng-model="claim.use_rental_auto_bill.value"><i></i>
                Enable Auto-Bill For Rentals
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="claim.use_rental_auto_bill.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom pt-15">
                <input type="checkbox" name="enable_back_of_1500" id="enableBackof1500"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"
                       ng-disabled="!editing || (editing && !authService.isAdmin())"
                       ng-model="claim.enable_back_of_1500.value"><i></i>
                Enable Back of 1500 Form in Print View
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="claim.enable_back_of_1500.id"
                          showlabel="{{false}}"></audit-button>
        </div>
        <div class="col-sm-12 form-group pt-25 pb-10">
            <label class="checkbox-inline checkbox-custom pt-15">
                <input type="checkbox" name="enable_bulk_claims" id="enable_bulk_claims"
                       ng-true-value="'Y'"
                       ng-false-value="'N'"
                       ng-disabled="!editing || (editing && !authService.isAdmin())"
                       ng-model="billing.enable_bulk_claims.value"><i></i>
                Enable Bulk Claims
            </label>
            <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.enable_bulk_claims.id"
                          showlabel="{{false}}"></audit-button>
        </div>
    </div>
</div>
