#context
server.servlet.contextPath=/
server.port=8080
server.servlet.application-display-name=451Tech Nymbl
server.forward-headers-strategy=framework
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css,image/jpeg,image/png,image/gif,audio/mpeg,audio/mp4,audio/wav,audio/webm

# JPA
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
#solid gold: spring.jpa.show-sql=true
# spring.jpa.properties.hibernate.format_sql=true

# DATA REST
spring.data.rest.base-path=/api
spring.data.rest.default-page-size=100
spring.data.rest.return-body-on-create=true
spring.data.rest.return-body-on-update=true
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
spring.main.allow-bean-definition-overriding=true

# email
spring.mail.host=smtp.office365.com
spring.mail.port=587
spring.mail.username=<EMAIL>
#spring.mail.password=451Tech##
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# logfile location
# LEAVE THIS MICHAEL
logfile.location=/opt/nymbl/logs
spring.thymeleaf.mode=LEGACYHTML5
spring.thymeleaf.cache=false
#This value is in seconds
nymbl.token.expiration=7200
punchout.token.expiration=84600
patientIntake.token.expiration=120
patientIntake.user.name=patientIntakeupload
patientIntake.user.id=7937
############################################################################
nymbl.era.download=0 30/59 * * * ?
#EVERY 60 MINUTES, 30 MINUTES PAST THE HOUR
# ###########################################################################
nymbl.send.claims=0 15/59 * * * ?
#EVERY 60 MINUTES, 15 MINUTES PAST THE HOUR
############################################################################
nymbl.appointment.reminder=0 45 15-23 * * ?
#UTC         PDT / PST                       EDT/EST
#15:00:00    8:00:00 AM / 7:00:00 AM         11:00:00 AM / 10:00:00 AM
#23:00:00    4:00:00 PM / 3:00:00 PM         7:00:00 PM / 6:00:00 PM
############################################################################

nymbl.daily.timezone.st.map={5:"E",6:"C",7:"M",8:"P"}
nymbl.daily.process.est=0 15 5 1/1 * ?
nymbl.daily.process.cst=0 15 6 1/1 * ?
nymbl.daily.process.mst=0 15 7 1/1 * ?
nymbl.daily.process.pst=0 15 8 1/1 * ?
#UTC         EST        CST         MST         PST
#05:15:00    00:15:00
#06:15:00               00:15:00
#07:15:00                           00:15:00
#08:15:00                                       00:15:00
nymbl.daily.timezone.dt.map={4:"E",5:"C",6:"M",7:"P"}
nymbl.daily.process.edt=0 15 4 1/1 * ?
nymbl.daily.process.cdt=0 15 5 1/1 * ?
nymbl.daily.process.mdt=0 15 6 1/1 * ?
nymbl.daily.process.pdt=0 15 7 1/1 * ?
#UTC         EDT        CDT         MDT         PDT
#04:15:00    00:15:00
#05:15:00               00:15:00
#06:15:00                           00:15:00
#07:15:00                                       00:15:00
############################################################################
nymbl.ledger.timezone.st.map={5:"E",6:"C",7:"M",8:"P"}
nymbl.ledger.process.est=0 0 5 1/1 * ?
nymbl.ledger.process.cst=0 0 6 1/1 * ?
nymbl.ledger.process.mst=0 0 7 1/1 * ?
nymbl.ledger.process.pst=0 0 8 1/1 * ?
#UTC         EST        CST         MST         PST
#05:00:00    00:00:00
#06:00:00               00:00:00
#07:00:00                           00:00:00
#08:00:00                                       00:00:00
nymbl.ledger.timezone.dt.map={4:"E",5:"C",6:"M",7:"P"}
nymbl.ledger.process.edt=0 0 4 1/1 * ?
nymbl.ledger.process.cdt=0 0 5 1/1 * ?
nymbl.ledger.process.mdt=0 0 6 1/1 * ?
nymbl.ledger.process.pdt=0 0 7 1/1 * ?
#UTC         EDT        CDT         MDT         PDT
#04:00:00    00:00:00
#05:00:00               01:00:00
#06:00:00                           01:00:00
#07:00:00                                       01:00:00
############################################################################
nymbl.ledger.migration=0 0 12 1/1 * ?
#UTC        EST         CST         MST         PST
#12:00:00   07:00:00    06:00:00    05:00:00    04:00:00
############################################################################
#nymbl.static.ledger.process=0 0 11 1/1 * ?
#UTC        EST         CST         MST         PST
#11:00:00   06:00:00    05:00:00    04:00:00    03:00:00
############################################################################
nymbl.clinic.timezone.st.map={7:"E",8:"C",9:"M",10:"P"}
nymbl.clinic.process.est=0 0 7 1/1 * ?
nymbl.clinic.process.cst=0 0 8 1/1 * ?
nymbl.clinic.process.mst=0 0 9 1/1 * ?
nymbl.clinic.process.pst=0 0 10 1/1 * ?
#UTC        EST         CST         MST         PST
#07:00:00   02:00:00
#08:00:00               02:00:00
#09:00:00                           02:00:00
#10:00:00                                       02:00:00
nymbl.clinic.timezone.dt.map={6:"E",7:"C",8:"M",9:"P"}
nymbl.clinic.process.edt=0 0 6 1/1 * ?
nymbl.clinic.process.cdt=0 0 7 1/1 * ?
nymbl.clinic.process.mdt=0 0 8 1/1 * ?
nymbl.clinic.process.pdt=0 0 9 1/1 * ?
#UTC        EDT         CDT         MDT         PDT
#06:00:00   02:00:00
#07:00:00               02:00:00
#08:00:00                           02:00:00
#09:00:00                                       02:00:00
############################################################################
nymbl.quicksight.timezone.st.map={8:"E",9:"C",10:"M",11:"P"}
nymbl.quicksight.process.est=0 0 8 1/1 * ?
nymbl.quicksight.process.cst=0 0 9 1/1 * ?
nymbl.quicksight.process.mst=0 0 10 1/1 * ?
nymbl.quicksight.process.pst=0 0 11 1/1 * ?
#UTC        EST         CST         MST         PST
#08:00:00   03:00:00
#09:00:00               03:00:00
#10:00:00                           03:00:00
#11:00:00                                       03:00:00
nymbl.quicksight.timezone.dt.map={7:"E",8:"C",9:"M",10:"P"}
nymbl.quicksight.process.edt=0 0 7 1/1 * ?
nymbl.quicksight.process.cdt=0 0 8 1/1 * ?
nymbl.quicksight.process.mdt=0 0 9 1/1 * ?
nymbl.quicksight.process.pdt=0 0 10 1/1 * ?
#UTC        EDT         CDT         MDT         PDT
#07:00:00   03:00:00
#08:00:00               03:00:00
#09:00:00                           03:00:00
#10:00:00                                       03:00:00
############################################################################
nymbl.pecos.process=0 30 12 1,9,17,25 * ?
#UTC         PDT / PST                       EDT/EST
#12:30:00    5:30:00 AM / 4:30:00 AM         8:30:00 AM / 7:30:00 AM

upload.directory=/opt/nymbl/uploads
pdf.form.custom.directory=/opt/nymbl/uploads/company/{0}/forms/
pdf.form.custom.temp.directory=/opt/nymbl/uploads/company/{0}/forms/temp/
pdf.form.patient.directory=/opt/nymbl/uploads/company/{0}/patient_documents/{1}/{2}/
db.username=dbadmin
#db.password=P49ikJr8rjH4udoK4rhH4jdpBH


#Forbin
forbin.environment=staging

logback.loglevel=ERROR
#AWS
aws.user.access.region.name=us-east-1
aws.quicksight.data.access.region=us-east-1
aws.access.key=
aws.secret.key=
empire.get.token.retries=8
empire.get.token.retry.thread.sleep=1000
empire.minus.seconds.from.access.token.availability.for.is.should.refresh=120
#Zendesk
#zendesk.token.secret=B960A6E9B26B0A55ADE6F3E75B8CB2254BCB46011A958E3EA1CE0BDE6E7991CC
#Redis
redis.ttl=604800
redis.cache.names=templates, companies
#nymbl.twilio.account-sid=**********************************
#nymbl.twilio.auth-token=5d2a5c8039c58989d5c21cc8de326601
#nymbl.twilio.nymbl-sms-number=+***********
#Mitigate logging error
log4j2.formatMsgNoLookups=true
#Nylas
#nylas.client-id=3a0euaclfjil6dss11onir8x9
#nylas.client-secret=a9idz357iw43i9t0wfvh8lpv3
## Outlook
#outlook.client-id=e9e39037-167f-4533-b186-156c13b6fc28
#outlook.client-secret=*************************************
cloud.aws.stack.auto=false
cloud.aws.region.auto=false
aws.sns-stripe-arn=arn:aws:sns:us-east-2:************:stripe_refunds_topic
aws.sns-topic.name=stripe_refunds_topic
stripe.refund.subject=Nymbl Transaction Process Update.
stripe.status.refund=succeeded, processing
stripe.status.cancel=requires_action, requires_confirmation, requires_payment_method, requires_capture
stripe.status.update=processing, requires_action, requires_confirmation, requires_payment_method
stripe.refund.reason=duplicate, fraudulent, requested_by_customer
stripe.account.change.notification=Your account has been updated, if you did not request this change <NAME_EMAIL>
stripe.refund.task.name=Refund for Payment # %s was %s

spring.batch.jdbc.initialize-schema=ALWAYS
spring.batch.job.enabled=false

#qa.migration.schema=automation_test
#qa.migration.code=flyway
#spring.flyway.schemas=qa_test
#spring.flyway.user=dbadmin
#spring.flyway.driver-class-name=software.aws.rds.jdbc.mysql.Driver
#spring.flyway.cleanDisabled=false
#
#spring.flyway.baselineOnMigrate=true
#spring.flyway.placeholderReplacement=true
#spring.flyway.locations=filesystem:${upload.directory}/db/migration

spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

management.endpoints.web.exposure.include=*
management.health.db.ignore-routing-data-sources=true
management.health.rabbit.enabled=false
management.health.redis.enabled=false
management.health.mail.enabled=false
management.endpoint.health.show-components=always
management.endpoint.health.show-details=always

## Nymbl Pay Configs
nymbl.patient.pay.url=/nymbl_patient_pay
nymbl.posted.payments.internal=billing/success/{stripeIdentifier}/browser
nymbl.patient.payments.public=/patient_pay_success
nymbl.posted.payments.terminal=billing/success/{stripeIdentifier}/terminal

google.search.api=www.google.com/maps/search/?api=1&query=

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=100MB

healthscribe.audio.prefix={tenant}/{branch}/practionerId-{practitionerId}/patientId-{patientId}/

#Logging Levels - Coordinate with logback.xml and config beans
logging.level.root=ERROR
logging.level.org.springframework=ERROR
logging.level.com.nymbl=INFO
#nymbl.flywayUser=dbadmin
#nymbl.flywayPort=3306
#nymbl.dbUrlPrefix=jdbc:mysql:aws://

## default 2592000, 60 X 1min; 900 is about 15 minutes, 450 is 7.5min, 330 is 5.5min.
eval.token.expiration=2592000
review.token.expiration=604800

lmn.file.temp.directory=/opt/nymbl/uploads/company/{0}/lmn/{1}/temp/
evaluation.localstack.s3.url=
evaluation.localstack.sqs.queue.url=
guest.common.basepath=custom-form,/api/guest-user/refresh,/api/guest-user/expire,/api/ui/config

# UI Runtime Config Parameter Store path
ui.runtime.parameter.path=/config/nymbl/ui

# CORS
management.endpoints.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
management.endpoints.web.cors.allowed-headers=*
management.endpoints.web.cors.allow-credentials=true
management.endpoints.web.cors.exposed-headers=X-Auth-Token,X-Tenant-Id,Branch-Id

# Swagger UI is disabled by default in all environments
swagger.ui.enabled=false
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.docExpansion=none
springdoc.swagger-ui.displayRequestDuration=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.disable-swagger-default-url=true

