package com.nymbl.tenant.model;

import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for BulkClaimJob entity.
 * This class provides custom configuration for the BulkClaimJob entity
 * to match the database schema.
 */
@Configuration
public class BulkClaimJobConfig {
    // Constants for audit table configuration
    public static final String AUDIT_TABLE_NAME = "bulk_claim_job_audit";
    public static final String REVISION_FIELD_NAME = "revision_id";
    public static final String REVISION_TYPE_FIELD_NAME = "revision_type";
}
