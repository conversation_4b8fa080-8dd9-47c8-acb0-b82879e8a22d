package com.nymbl.tenant.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.ZonedDateTime;

/**
 * Entity for tracking bulk claim submission jobs.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "bulk_claim_job")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id", "jobId", "startTime", "endTime", "status", "totalClaims",
        "processedClaims", "successfulClaims", "failedClaims", "currentPayer", "errorMessage", "completed",
        "x12FilePath", "x12FileContent", "legacy"
})
@JsonIgnoreProperties(ignoreUnknown = true)
@EntityListeners(AuditingEntityListener.class)
@Audited
@org.hibernate.envers.AuditTable(value = "bulk_claim_job_audit")
public class BulkClaimJob extends ModelStub {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_id", nullable = false)
    private String jobId;

    @Column(name = "start_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private ZonedDateTime startTime;

    @Column(name = "end_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private ZonedDateTime endTime;

    @Column(name = "status", nullable = false)
    private String status; // PENDING, PROCESSING, COMPLETED, FAILED

    @Column(name = "total_claims")
    private Integer totalClaims;

    @Column(name = "processed_claims")
    private Integer processedClaims;

    @Column(name = "successful_claims")
    private Integer successfulClaims;

    @Column(name = "failed_claims")
    private Integer failedClaims;

    @Column(name = "current_payer")
    private String currentPayer;

    @Column(name = "error_message", columnDefinition = "text")
    private String errorMessage;

    @Column(name = "completed")
    @Builder.Default
    private Boolean completed = false;

    @Column(name = "x12_file_path")
    private String x12FilePath;

    @Column(name = "x12_file_content", columnDefinition = "LONGTEXT")
    private String x12FileContent;

    /**
     * Get the X12 content for backward compatibility.
     *
     * @return the X12 file content
     */
    public String getX12Content() {
        return x12FileContent;
    }
}
