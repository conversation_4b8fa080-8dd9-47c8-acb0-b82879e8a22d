package com.nymbl.tenant.service;

import com.nymbl.config.dto.reports.*;
import com.nymbl.tenant.model.GL_Period;
import com.nymbl.tenant.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON>
 * 07/06/2021
 * Internal Reports Service
 * All Reports Logic and Execution
 */
@Slf4j
@Service
public class ReportService {

    private final GeneralLedgerRepository generalLedgerRepository;
    private final GL_PeriodRepository gl_periodRepository;
    private final PrescriptionRepository prescriptionRepository;
    private final NymblStatusHistoryRepository nymblStatusHistoryRepository;
    private final AppointmentRepository appointmentRepository;

    @Autowired
    public ReportService(GeneralLedgerRepository generalLedgerRepository, GL_PeriodRepository gl_periodRepository,
                         PrescriptionRepository prescriptionRepository, NymblStatusHistoryRepository nymblStatusHistoryRepository,
                         AppointmentRepository appointmentRepository) {
        super();
        this.generalLedgerRepository = generalLedgerRepository;
        this.gl_periodRepository = gl_periodRepository;
        this.prescriptionRepository = prescriptionRepository;
        this.nymblStatusHistoryRepository = nymblStatusHistoryRepository;
        this.appointmentRepository = appointmentRepository;
    }

    public List<IRxDeliveredNotBilled> getRxDeliveredNotBilled(Long branchId, String startDate, String endDate) {
        List<IRxDeliveredNotBilled> results = prescriptionRepository.getRxDeliveredNotBilled(branchId, startDate, endDate);

        return results;
    }

    public List<IRxTimeline> getRxTimelines(Long branchId, LocalDate startDate, LocalDate endDate, String dateOption) {
        if (StringUtils.isEmpty(dateOption)) {
            dateOption = "prescription";
        }

        return prescriptionRepository.getRxTimelines(branchId, startDate, endDate, dateOption);
    }


    public List<IStatusChangeTracking> getRxStatusChangeTrackingReport(Long branchId, String startDate, String endDate) {
        List<IStatusChangeTracking> results = nymblStatusHistoryRepository.getRxStatusChangeTrackingReport(branchId, startDate, endDate);
        return results;
    }

    public List<IStatusChangeTracking> getClaimStatusChangeTrackingReport(Long branchId, String startDate, String endDate) {
        return nymblStatusHistoryRepository.getClaimStatusChangeTrackingReport(branchId, startDate, endDate);
    }

    public List<IPrescriptionStatusHistoryChangeTracking> getNymblStatusHistoryChangeTracking(Long branchId, String startDate, String endDate) {
        List<IPrescriptionStatusHistoryChangeTracking> results = nymblStatusHistoryRepository.getNymblStatusHistoryChangeTracking(branchId, startDate, endDate);
        return results;
    }

    public List<IClaimStatusHistoryChangeTracking> getClaimStatusHistoryChangeTracking(Long branchId, String startDate, String endDate) {
        return nymblStatusHistoryRepository.getClaimStatusHistoryChangeTracking(branchId, startDate, endDate);
    }

    public List<INoteCompletionTimeline> getNoteCompletionTimelinesReport(Long branchId, OffsetDateTime startDateTime, OffsetDateTime endDateTime) {
        List<INoteCompletionTimeline> results = appointmentRepository.getNoteCompletionTimelinesReport(branchId, startDateTime, endDateTime);
        return results;
    }


    public RxSalesReport getSalesSummaryReport(Long branchId, Long start, Long end) {
        //TODO Need to handle missing ids being passed instead of defaults
        Map<String, Long> periodMap = loadGLPeriods(start, end);
        return getSalesReport(branchId, periodMap.get("start"), periodMap.get("end"));
    }

    public RxSalesReport getSalesSummaryReportForWidget(Long branchId) {

        org.joda.time.LocalDate currentDate = new org.joda.time.LocalDate();
        Optional<GL_Period> glPeriod = gl_periodRepository.findCurrentPeriod(java.sql.Date.valueOf(currentDate.toString()));
        String tempStart = glPeriod.isPresent() ? glPeriod.get().getYear().toString() + String.format("%02d", glPeriod.get().getPeriod()) : "200001";
        String tempEnd = glPeriod.isPresent() ? glPeriod.get().getYear() + String.format("%02d", glPeriod.get().getPeriod()) : "203012";

        return getSalesReport(branchId, Long.valueOf(tempStart), Long.valueOf(tempEnd));
    }

    private RxSalesReport getSalesReport(Long branchId, Long start, Long end) {
        RxSalesReport rxSalesReport = new RxSalesReport();
        ArrayList<IRxSales> data = generalLedgerRepository.getRxSalesSummaryReport(branchId, start, end);
        rxSalesReport.setRxSales(data);
        List<Object[]> totals = generalLedgerRepository.getRxSalesSummaryReportTotals(branchId, start, end);

        rxSalesReport.setTotalBillable(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[0] : BigDecimal.ZERO);
        rxSalesReport.setTotalContractual(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[1] : BigDecimal.ZERO);
        rxSalesReport.setTotalAllowable(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[2] : BigDecimal.ZERO);

        return rxSalesReport;
    }
    public RxLCodeSalesReport getSalesByLCodeSummaryReport(Long branchId, Long start, Long end) {
        //TODO Need to handle missing ids being passed instead of defaults
        Map<String, Long> periodMap = loadGLPeriods(start, end);
        RxLCodeSalesReport result = new RxLCodeSalesReport();
        ArrayList<IRxLCodeSales> data = generalLedgerRepository.getRxSalesByLCodeSummaryReport(branchId, periodMap.get("start"), periodMap.get("end"));
        result.setRxSales(data);
        List<Object[]> totals = generalLedgerRepository.rxSalesByLCodeSummaryTotals(branchId, periodMap.get("start"), periodMap.get("end"));
        result.setTotalBillable(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[0] : BigDecimal.ZERO);
        result.setTotalContractual(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[1] : BigDecimal.ZERO);
        result.setTotalAllowable(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[2] : BigDecimal.ZERO);
        return result;
    }

    public HcpcDetailReport getHCPCDetailReport(Long branchId, Long start, Long end) {
        //TODO Need to handle missing ids being passed instead of defaults
        Map<String, Long> periodMap = loadGLPeriods(start, end);
        HcpcDetailReport result = new HcpcDetailReport();
        ArrayList<IHcpcDetail> data = generalLedgerRepository.getHcpcDetailReport(branchId, periodMap.get("start"), periodMap.get("end"));
        data.removeIf(x -> x.getPaymentFilterId() == null);
        // data.removeIf(x -> x.getCo_45().compareTo(BigDecimal.ZERO) == 0 && x.getPayments().compareTo(BigDecimal.ZERO) == 0 && x.getAdjustments().compareTo(BigDecimal.ZERO) == 0 && x.getLine_adjustments().compareTo(BigDecimal.ZERO) == 0);
        result.setData(data);
//        List<Object[]> totals = generalLedgerRepository.hcpcDetailTotals(branchId, periodMap.get("start"), periodMap.get("end"));
//        result.setTotalBillable(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[0] : BigDecimal.ZERO);
//        result.setTotalContractual(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[1] : BigDecimal.ZERO);
//        result.setTotalAllowable(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[2] : BigDecimal.ZERO);
        return result;
    }

    public DailyCloseReport getDailyCloseReport(Long branchId, Long start, Long end, Boolean isSuperAdmin) {
        DailyCloseReport result = new DailyCloseReport();
        Map<String, Long> periodMap = loadGLPeriods(start, end);
        List<IDailyClose> data = generalLedgerRepository.getDailyCloseReport(branchId, periodMap.get("start"), periodMap.get("end"), isSuperAdmin);
        data.removeIf(x -> x.getAppliedAmount().compareTo(BigDecimal.ZERO) == 0);
        result.setData(data);
        List<Object[]> totals = generalLedgerRepository.getDailyCloseReportTotals(branchId, periodMap.get("start"), periodMap.get("end"), isSuperAdmin);
        result.setTotalAmount(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[0] : BigDecimal.ZERO);
        result.setTotalApplied(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[1] : BigDecimal.ZERO);
        result.setTotalUnapplied(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[2] : BigDecimal.ZERO);
        return result;
    }

    public List<IPayments> getZeroDollarPaymentsReport(Long branchId, java.util.Date startDate, java.util.Date endDate) {

        List<IPayments> result = generalLedgerRepository.getZeroDollarPayments(branchId, startDate, endDate);

        return result;
    }

    public AdjustmentsReport getAdjustmentsReport(Long branchId, Long start, Long end) {
        AdjustmentsReport result = new AdjustmentsReport();
        Map<String, Long> periodMap = loadGLPeriods(start, end);
        List<IAdjustments> data = generalLedgerRepository.getAdjustmentReport(branchId, periodMap.get("start"), periodMap.get("end"));
        data.removeIf(x -> x.getApplied().compareTo(BigDecimal.ZERO) == 0);
        result.setData(data);
        List<Object[]> totals = generalLedgerRepository.getAdjustmentReportTotals(branchId, periodMap.get("start"), periodMap.get("end"));
        result.setTotalAmount(BigDecimal.ZERO);
        result.setTotalApplied(totals != null && !totals.isEmpty() ? (BigDecimal) totals.get(0)[1] : BigDecimal.ZERO);
        result.setTotalUnapplied(BigDecimal.ZERO);
        return result;
    }

    public AccountsReceivableReport getAccountsReceivableReport(Long branchId, Long end) {
        AccountsReceivableReport result = new AccountsReceivableReport();
        Map<String, Long> periodMap = loadGLPeriods(null, end);
        List<IAccountsReceivable> data = new ArrayList<>();
        List<IAccountsReceivable> entries = generalLedgerRepository.getAccountReceivableReport(branchId, periodMap.get("end"));
        for(IAccountsReceivable a : entries){
            if (a.getAr().compareTo(BigDecimal.ZERO) != 0 || a.getActivity().compareTo(BigDecimal.ZERO) != 0 || a.getHasActivity().equals(1L)) {
                data.add(a);
                result.setTotalBillable(result.getTotalBillable().add(a.getBillable() != null ? a.getBillable() : BigDecimal.ZERO));
                result.setTotalAmount(result.getTotalAmount().add(a.getPayments() != null ? a.getPayments() : BigDecimal.ZERO));
                result.setTotalAmount(result.getTotalAmount().add(a.getAdjustments() != null ? a.getAdjustments() : BigDecimal.ZERO));

                result.setTotalActivity(result.getTotalActivity().add(a.getActivity() != null ? a.getActivity() : BigDecimal.ZERO));
                result.setTotalAr(result.getTotalAr().add(a.getAr() != null ? a.getAr() : BigDecimal.ZERO));
            }
        }
        result.setData(data);
        return result;
    }

    public OutstandingBalanceReport getOutstandingBalancesReport(Long branchId, Long end) {

        OutstandingBalanceReport result = new OutstandingBalanceReport();
        Map<String, Long> periodMap = loadGLPeriods(null, end);
        List<IOutstandingBalance> data = new ArrayList<>();
        List<IOutstandingBalance> entries = generalLedgerRepository.getOutstandingBalancesReport(branchId, periodMap.get("end"));
        for(IOutstandingBalance a : entries){
//            if (a.getAr().compareTo(BigDecimal.ZERO) != 0 || a.getActivity().compareTo(BigDecimal.ZERO) != 0 || a.getHasActivity().equals(1L)) {
                data.add(a);
                result.setTotalBillable(result.getTotalBillable().add(a.getBillable() != null ? a.getBillable() : BigDecimal.ZERO));
                result.setTotalAmount(result.getTotalAmount().add(a.getPayments() != null ? a.getPayments() : BigDecimal.ZERO));
            result.setTotalAmount(result.getTotalAmount().add(a.getAdjustments() != null ? a.getAdjustments() : BigDecimal.ZERO));
                result.setTotalActivity(result.getTotalActivity().add(a.getActivity() != null ? a.getActivity() : BigDecimal.ZERO));
                result.setTotalAr(result.getTotalAr().add(a.getAr() != null ? a.getAr() : BigDecimal.ZERO));
//            }
        }
        result.setData(data);
        return result;
    }

    public Map<String, Long> loadGLPeriods(Long start, Long end) {
        Map<String, Long> result = new HashMap<>();
        Optional<GL_Period> startPeriod = Optional.empty();
        if (start != null) {
            startPeriod = gl_periodRepository.findById(start);
        }
        Optional<GL_Period> endPeriod = Optional.empty();
        if (end != null) {
            endPeriod = gl_periodRepository.findById(end);
        }
        String tempStart = startPeriod.isPresent() ? startPeriod.get().getYear().toString() + String.format("%02d", startPeriod.get().getPeriod()) : "200001";
        String tempEnd = endPeriod.isPresent() ? endPeriod.get().getYear().toString() + String.format("%02d", endPeriod.get().getPeriod()) : "203012";
        result.put("start", Long.valueOf(tempStart));
        result.put("end", Long.valueOf(tempEnd));
        return result;
    }

    public List<IPractitionerAppointments> getPractitionerAppointmentReport(Long branchId, Long userId, OffsetDateTime startDateTime, OffsetDateTime endDateTime) {
        if (startDateTime == null || startDateTime.equals("null")) startDateTime = OffsetDateTime.MIN;
        if (endDateTime == null || endDateTime.equals("null")) endDateTime = OffsetDateTime.now();
        List<IPractitionerAppointments> result = generalLedgerRepository.getPractitionerAppointmentReports(branchId, userId, startDateTime, endDateTime);
        return result;
    }

    public List<ClaimsActivity> getClaimsActivityReport(Long branchId, Long startPeriod, Long endPeriod){
        List<ClaimsActivity> result = generalLedgerRepository.getClaimsActivityReport(branchId, startPeriod, endPeriod);
        return result;
    }


    public List<RentalPrescriptionRow> getRentalPrescriptionReport(Long branchId, Long prescriptionStatusId, Boolean isPaused, Boolean isCanceled, Boolean isCompleted){
        List<RentalPrescriptionRow> rows = prescriptionRepository.getRentalPrescriptionReport(branchId, prescriptionStatusId, isPaused, isCanceled, isCompleted);

        Map<Boolean, List<RentalPrescriptionRow>> partitioned = rows.stream()
                .collect(Collectors.partitioningBy(row -> row.getParentId() == null));

        List<RentalPrescriptionRow> parents = partitioned.get(true);
        List<RentalPrescriptionRow> children = partitioned.get(false);

        // Initialize the subPrescriptions for parents if they are null
        parents.forEach(parent -> {
            if (parent.getSubPrescriptions() == null) {
                parent.setSubPrescriptions(new ArrayList<>());
            }
        });

        // Add each child to the appropriate parent's subPrescriptions
        children.forEach(child -> {
            parents.stream()
                    .filter(parent -> Objects.equals(parent.getPrescriptionId(), child.getParentId()))
                    .findFirst()
                    .ifPresent(parent -> {
                        // Add the child to the parent's subPrescriptions list
                        parent.getSubPrescriptions().add(child);
                    });
        });

        for (RentalPrescriptionRow parent : parents) {
            // Get the subPrescriptions (children) of the current parent
            List<RentalPrescriptionRow> childCollection = parent.getSubPrescriptions();

            if (childCollection != null) {
                // Sort the children by prescriptionDate
                childCollection.sort(Comparator.comparing(RentalPrescriptionRow::getPrescriptionDate));

                Long parentClaimNumber = parent.getRentalClaimNumber();

                for (int i = 0; i < childCollection.size(); i++) {
                    RentalPrescriptionRow child = childCollection.get(i);

                    // Copy nextBillDate and numRentalBillingPeriods from the parent to the child
                    child.setNextBillDate(parent.getNextBillDate());
                    child.setNumRentalBillingPeriods(parent.getNumRentalBillingPeriods());

                    // Set child's rentalClaimNumber based on the parent's rentalClaimNumber
                    if (parentClaimNumber != null) {
                        child.setRentalClaimNumber(parentClaimNumber + i + 1);
                    }
                }
            }
        }

        return parents;
    }



}
