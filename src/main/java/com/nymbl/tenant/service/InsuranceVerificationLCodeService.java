package com.nymbl.tenant.service;

import com.nymbl.config.dto.DataModel;
import com.nymbl.config.dto.PatientTotalAllowableDTO;
import com.nymbl.config.dto.arReports.ArIvlcPeriodTotals;
import com.nymbl.config.enums.PriceOption;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.NumberUtil;
import com.nymbl.tenant.interfaces.IInsuranceVerification_L_Code;
import com.nymbl.tenant.interfaces.repository.InsuranceVerification_L_CodeIRepository;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.InsuranceVerificationRepository;
import com.nymbl.tenant.repository.InsuranceVerificationSQL;
import com.nymbl.tenant.repository.InsuranceVerification_L_CodeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.nymbl.config.Constants.DF_YYYY_MM_DD_HH_MM;

/**
 * Created by Bradley Moore on 10/19/2017.
 */
@Service
public class InsuranceVerificationLCodeService extends AbstractTableService<InsuranceVerification_L_Code, Long> {

    private final InsuranceVerification_L_CodeRepository insuranceVerificationLCodeRepository;
    private final InsuranceVerificationRepository insuranceVerificationRepository;
    private final L_CodeFeeService lCodeFeeService;
    private final PatientInsuranceService patientInsuranceService;
    private final InsuranceVerification_L_CodeIRepository insuranceVerification_L_CodeIRepository;
    private final SystemSettingService systemSettingService;

    @Autowired
    public InsuranceVerificationLCodeService(InsuranceVerification_L_CodeRepository insuranceVerificationLCodeRepository,
                                             InsuranceVerificationRepository insuranceVerificationRepository,
                                             L_CodeFeeService lCodeFeeService,
                                             @Lazy PatientInsuranceService patientInsuranceService,
                                             InsuranceVerification_L_CodeIRepository insuranceVerification_l_codeIRepository,
                                             SystemSettingService systemSettingService) {
        super(insuranceVerificationLCodeRepository);
        this.insuranceVerificationLCodeRepository = insuranceVerificationLCodeRepository;
        this.insuranceVerificationRepository = insuranceVerificationRepository;
        this.lCodeFeeService = lCodeFeeService;
        this.patientInsuranceService = patientInsuranceService;
        this.insuranceVerification_L_CodeIRepository = insuranceVerification_l_codeIRepository;
        this.systemSettingService = systemSettingService;
    }

    public List<InsuranceVerification_L_Code> findByInsuranceVerificationId(Long insuranceVerificationId) {
        //System.out.println("insuranceVerificationLCodeRepository.findByPrescriptionIdQuery = \n"+insuranceVerificationLCodeRepository.findByPrescriptionIdQuery.replaceAll(":prescriptionId",prescriptionId+""));
        return insuranceVerificationLCodeRepository.findByInsuranceVerificationId(insuranceVerificationId);
    }

    public List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndCoveredTrue(Long insuranceVerificationId) {
        return insuranceVerificationLCodeRepository.findByInsuranceVerificationIdAndCoveredTrue(insuranceVerificationId);
    }

    public List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndCoveredTrueAndOrderByPlcOrderNum(Long insuranceVerificationId) {
        return insuranceVerificationLCodeRepository.findByInsuranceVerificationIdAndCoveredTrueAndOrderByPlcOrderNum(insuranceVerificationId);
    }

    public List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndBillTrue(Long insuranceVerificationId) {
        return insuranceVerificationLCodeRepository.findByInsuranceVerificationIdAndBillTrueOrderByPrescriptionLCode_OrderNum(insuranceVerificationId);
    }

    public List<InsuranceVerification_L_Code> findByInsuranceVerificationIdAndBillTrueAndOrderByPlcOrderNum(Long insuranceVerificationId) {
        return insuranceVerificationLCodeRepository.findByInsuranceVerificationIdAndBillTrueAndOrderByPlcOrderNum(insuranceVerificationId);
    }

    public InsuranceVerification_L_Code findTopByInsuranceVerificationIdAndPrescriptionLCodeIdOrderByIdDesc(Long insuranceVerificationId, Long prescriptionLCodeId) {
        try {
            return insuranceVerificationLCodeRepository.findTopByInsuranceVerificationIdAndPrescriptionLCodeIdOrderByIdDesc(insuranceVerificationId, prescriptionLCodeId);
        } catch (Exception e) {
            // If there are multiple results, get all results and return the most recent one
            List<InsuranceVerification_L_Code> results = insuranceVerificationLCodeRepository.findAllByInsuranceVerificationIdAndPrescriptionLCodeId(insuranceVerificationId, prescriptionLCodeId);
            if (results != null && !results.isEmpty()) {
                // Sort by ID in descending order and return the first one
                results.sort((a, b) -> b.getId().compareTo(a.getId()));
                return results.get(0);
            }
            return null;
        }
    }

    public List<InsuranceVerification_L_Code> findByPrescriptionLCodeId(Long prescriptionLCodeId) {
        return insuranceVerificationLCodeRepository.findByPrescriptionLCodeId(prescriptionLCodeId);
    }

    public List<InsuranceVerification_L_Code> findByInsuranceVerificationPatientInsuranceIdAndPrescriptionId(Long patientInsuranceId, Long prescriptionId) {
        return insuranceVerificationLCodeRepository.findByInsuranceVerification_PatientInsurance_IdAndInsuranceVerification_Prescription_Id(patientInsuranceId, prescriptionId);
    }

    public InsuranceVerification_L_Code buildNewInsuranceVerificationLCode(Prescription_L_Code pl, Long insuranceVerificationId, Boolean isNew, BigDecimal msrp, BigDecimal cost) {
        boolean crtEnabled = "Y".equals(systemSettingService.findBySectionAndField("general", "hide_device_type").getValue());
        boolean useRural = "Y".equals(systemSettingService.findBySectionAndField("billing", "rural_non_rural_on").getValue());
        boolean isRural = Boolean.TRUE.equals(pl.getPrescription().getIsRural()) && useRural;
        InsuranceVerification iv = insuranceVerificationRepository.findById(insuranceVerificationId).get();
        InsuranceVerification_L_Code result;
        Long billingFeeId = iv.getPatientInsurance().getInsuranceCompany().getDefaultBillingAmountId();
        Long allowableId = iv.getPatientInsurance().getInsuranceCompany().getDefaultAllowableId();
        L_CodeFee allowable = lCodeFeeService.getByLCodeIdAndFeeScheduleIdOrderByLCodeIdAsc(pl.getLCodeId(), allowableId, false, crtEnabled, pl.getModifier1(), pl.getModifier2(), pl.getModifier3(), pl.getModifier4());
        L_CodeFee billable = lCodeFeeService.getByLCodeIdAndFeeScheduleIdOrderByLCodeIdAsc(pl.getLCodeId(), billingFeeId, false, crtEnabled, pl.getModifier1(), pl.getModifier2(), pl.getModifier3(), pl.getModifier4());

        PriceOption allowablePriceOption = allowable.getPriceOption();
        PriceOption billablePriceOption = billable.getPriceOption();

        allowable = lCodeFeeService.applyPriceOption(allowable, pl);
        billable = lCodeFeeService.applyPriceOption(billable, pl);

        BigDecimal currentAllowableFee = isRural ? allowable.getRuralFee() : allowable.getFee();
        BigDecimal currentBillableFee = isRural ? billable.getRuralFee() : billable.getFee();
        if (isNew) {
            result = new InsuranceVerification_L_Code();
            result.setUseSalesTax(false);
            result.setSalesTax(BigDecimal.ZERO);
            result.setCovered(true);
            result.setBill(true);
            result.setInsuranceVerificationId(insuranceVerificationId);
            result.setPrescriptionLCodeId(pl.getId());
            result.setPrescriptionLCode(pl);
            result.setTotalAllowable(currentAllowableFee != null ? currentAllowableFee.multiply(new BigDecimal(pl.getQuantity())) : BigDecimal.ZERO);
            result.setTotalCharge(currentBillableFee != null ? currentBillableFee.multiply(new BigDecimal(pl.getQuantity())) : BigDecimal.ZERO);
            result.setAllowableFee(currentAllowableFee != null ? currentAllowableFee : BigDecimal.ZERO);
            result.setBillingFee(currentBillableFee != null ? currentBillableFee : BigDecimal.ZERO);
            result.setMsrp(msrp);
            result.setCost(cost);
        } else {
            result = findTopByInsuranceVerificationIdAndPrescriptionLCodeIdOrderByIdDesc(insuranceVerificationId, pl.getId());
            if (crtEnabled && !PriceOption.CUSTOM.equals(allowablePriceOption)) {
                result.setAllowableFee(currentAllowableFee);
                result.setTotalAllowable(currentAllowableFee != null ? currentAllowableFee.multiply(new BigDecimal(pl.getQuantity())) : BigDecimal.ZERO);
            } else {
                result.setTotalAllowable(result.getAllowableFee().multiply(new BigDecimal(pl.getQuantity())));
            }
            if (crtEnabled && !PriceOption.CUSTOM.equals(billablePriceOption)) {
                result.setBillingFee(currentBillableFee);
                result.setTotalCharge(currentBillableFee != null ? currentBillableFee.multiply(new BigDecimal(pl.getQuantity())) : BigDecimal.ZERO);
            } else {
                result.setTotalCharge(result.getBillingFee().multiply(new BigDecimal(pl.getQuantity())));
            }
        }
        return result;
    }

    public List<InsuranceVerification_L_Code> findByPrescriptionIdWithCalculatedFirstCarrier(Long prescriptionId) {
        return insuranceVerificationLCodeRepository.findByPrescriptionIdWithCalculatedFirstCarrier(prescriptionId);
    }

    public List<InsuranceVerification_L_Code> findByPrescriptionIdAndCarrierType(Long prescriptionId, String carrierType) {
        return insuranceVerificationLCodeRepository.findByPrescriptionLCode_PrescriptionIdAndInsuranceVerificationCarrierType(prescriptionId, carrierType);
    }

    public List<InsuranceVerification_L_Code> findPrimaryIvlcForPrescriptionByPrescriptionId(Long prescriptionId) {
        return insuranceVerificationLCodeRepository.findPrimaryIvlcForPrescriptionByPrescriptionId(prescriptionId);
    }

    public PatientTotalAllowableDTO getTotalAllowableByPatientId(Long patientId) {
        PatientTotalAllowableDTO totalAllowableDto = new PatientTotalAllowableDTO();
        BigDecimal totalAllowable = insuranceVerificationLCodeRepository.getTotalAllowableByPatientId(patientId);
        totalAllowableDto.setTotalAllowable(totalAllowable);
        return totalAllowableDto;
    }

    public List<InsuranceVerification_L_Code> getInsuranceVerificationLCodes(Claim claim, InsuranceVerification insuranceVerification) {
        List<InsuranceVerification_L_Code> temp = new ArrayList<>();
        if (insuranceVerification != null) {
            temp = patientInsuranceService.getInsuranceVerificationLCodes(claim.getPrescriptionId(), claim.getPatientInsurance(), insuranceVerification);
        }
        return temp;
    }

    public void splitAndCreateIvlc(Prescription_L_Code plc1, Prescription_L_Code plc2) {
        List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeRepository.findByPrescriptionLCodeId(plc1.getId());
        for (InsuranceVerification_L_Code ivlc : ivlcs) {
            ivlc.setTotalAllowable(ivlc.getTotalAllowable().divide(new BigDecimal("2")));
            ivlc.setTotalCharge(ivlc.getTotalCharge().divide(new BigDecimal("2")));

            if (ivlc.getSalesTax() == null) {
                ivlc.setSalesTax(BigDecimal.ZERO);
            } else if (!ivlc.getSalesTax().equals(BigDecimal.ZERO)) {
                ivlc.setSalesTax(ivlc.getSalesTax().divide(new BigDecimal(2)));
            }

            save(ivlc);

            InsuranceVerification_L_Code newIvlc = new InsuranceVerification_L_Code();
            newIvlc.setAllowableFee(ivlc.getAllowableFee());
            newIvlc.setBillingFee(ivlc.getBillingFee());
            newIvlc.setCovered(ivlc.getCovered());
            newIvlc.setBill(ivlc.getBill());
            newIvlc.setInsuranceVerificationId(ivlc.getInsuranceVerificationId());
            newIvlc.setPrescriptionLCodeId(plc2.getId());
            newIvlc.setTotalAllowable(ivlc.getTotalAllowable());
            newIvlc.setTotalCharge(ivlc.getTotalCharge());
            newIvlc.setAuthNumber(ivlc.getAuthNumber());
            newIvlc.setStartDate(ivlc.getStartDate());
            newIvlc.setExpirationDate(ivlc.getExpirationDate());
            newIvlc.setUseSalesTax(ivlc.getUseSalesTax());
            newIvlc.setSalesTax(ivlc.getSalesTax() != null && !ivlc.getSalesTax().equals(BigDecimal.ZERO) ? ivlc.getSalesTax() : BigDecimal.ZERO);
            save(newIvlc);
        }
    }

    /**
     * @param startDate
     * @param endDate
     * @param branchId
     * @return AgingClaimDTO
     * <p>
     * NOTE: WriteOff value is negated inside the SQL query.
     * WriteOff = (Billable - Allowable) * -1
     */
    public ArIvlcPeriodTotals getReportBillablesAndAllowablesUsingIvlcs(Date startDate, Date endDate, Long branchId, String deviceType) {
        ArIvlcPeriodTotals result = new ArIvlcPeriodTotals();
        List<Object[]> data = insuranceVerificationLCodeRepository.getReportsBillablesAndAllowables(startDate, endDate, branchId, deviceType);
        result.setBillable(NumberUtil.getBigDecimalValue(data.get(0)[0]));
        result.setAllowable(NumberUtil.getBigDecimalValue(data.get(0)[1]));
        result.setWriteOff(NumberUtil.getBigDecimalValue(data.get(0)[2]));
        return result;
    }

//    public List<ArIvlcPeriodTotals> getIvlcsBillableAllowableForClaimsNotSubmittedByInsuranceCompany(Long branchId){
//        List<ArIvlcPeriodTotals> result = new ArrayList<>();
//        List<Object[]> data = insuranceVerificationLCodeRepository.getIvlcsBillableAllowableForClaimsNotSubmittedByInsuranceCompany(branchId);
//        for(Object[] x : data){
//            ArIvlcPeriodTotals temp = new ArIvlcPeriodTotals();
//            temp.setId(Long.valueOf(x[0].toString()));
//            temp.setBillable(NumberUtil.getBigDecimalValue(x[1]));
//            temp.setAllowable(NumberUtil.getBigDecimalValue(x[2]));
//            temp.setWriteOff(NumberUtil.getBigDecimalValue(x[3]));
//            temp.setDescription(x[4].toString());
//            result.add(temp);
//        }
//        return result;
//    }

    /**
     * Shared
     *
     * @param claimId
     * @return
     */
    public ArIvlcPeriodTotals getIvlcTotalsByClaimId(Long claimId) {
        ArIvlcPeriodTotals result = new ArIvlcPeriodTotals();
        List<Object[]> data = insuranceVerificationLCodeRepository.getIvlcTotalsByClaimId(claimId);
        for (Object[] x : data) {
            result.setId(Long.valueOf(x[0].toString()));
            result.setBillable(NumberUtil.getBigDecimalValue(x[1]));
            result.setAllowable(NumberUtil.getBigDecimalValue(x[2]));
            result.setWriteOff(NumberUtil.getBigDecimalValue(x[3]));
            result.setSalesTax(NumberUtil.getBigDecimalValue(x[4]));
        }
        return result;
    }

    public List<InsuranceVerification_L_Code> getIvlcsByClaimId(Long claimId) {
        return insuranceVerificationLCodeRepository.getIvlcsByClaimId(claimId);
    }

    public List<Object[]> getSalesTaxTotals(Date startDate, Date endDate, Long branchId, String deviceType) {
        List<Object[]> result = insuranceVerificationLCodeRepository.getSalesTaxTotals(startDate, endDate, branchId, deviceType);
        if (result.get(0)[0] == null) result.get(0)[0] = BigDecimal.ZERO;
        if (result.get(0)[1] == null) result.get(0)[1] = BigDecimal.ZERO;
        if (result.get(0)[2] == null) result.get(0)[2] = BigDecimal.ZERO;
        if (result.get(0)[3] == null) result.get(0)[3] = BigDecimal.ZERO;
        return result;
    }

    public List<Object[]> getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchId(Date startDate, Date endDate, Long branchId, Boolean usePatientBranch) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
//        System.out.println("InsuranceVerification_L_CodeRepository.getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchIdQuery = \n" + InsuranceVerification_L_CodeRepository.getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchIdQuery
//            .replaceAll(":startDate", "'" + dateStart + "'")
//            .replaceAll(":endDate", "'" + dateEnd + "'")
//            .replaceAll(":branchId", branchId == null ? "null" : branchId.toString())
//            .replaceAll(":deviceType", "'%'")
//            .replaceAll(":isSubmission", true ? "1" : "0")
//            .replaceAll(":usePatientBranch", usePatientBranch ? "1" : "0"));
        List<Object[]> result = insuranceVerificationLCodeRepository.getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchId(dateStart, dateEnd, branchId, "%", true, usePatientBranch);
        return result;
    }

    public List<Object[]> getViewUserBillingTotalsBySubmissionDateBetweenAndBranchId(Date startDate, Date endDate, Long branchId, Boolean usePatientBranch) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
//        System.out.println("InsuranceVerification_L_CodeRepository.getViewUserBillingTotalsBySubmissionDateBetweenAndBranchIdQuery = \n" + InsuranceVerification_L_CodeRepository.getViewUserBillingTotalsBySubmissionDateBetweenAndBranchIdQuery
//            .replaceAll(":startDate", "'" + dateStart + "'")
//            .replaceAll(":endDate", "'" + dateEnd + "'")
//            .replaceAll(":branchId", branchId == null ? "null" : branchId.toString())
//            .replaceAll(":deviceType", "'%'")
//            .replaceAll(":isSubmission", true ? "1" : "0")
//        );
        List<Object[]> result = insuranceVerificationLCodeRepository.getViewUserBillingTotalsBySubmissionDateBetweenAndBranchId(dateStart, dateEnd, branchId, "%", true, usePatientBranch);
        return result;
    }

    public List<Object[]> getPractitionerBillingTotalsCategoriesBySubmissionDateBetweenAndBranchId(Date startDate, Date endDate, Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<Object[]> result = insuranceVerificationLCodeRepository.getPractitionerBillingTotalsCategoriesBySubmissionDateBetweenAndBranchId(dateStart, dateEnd, branchId, "%");
        return result;
    }

    public List<InsuranceVerification_L_Code> findByExpirationDate(Date expirationDate) {
        List<InsuranceVerification_L_Code> results = insuranceVerificationLCodeRepository.findByExpirationDate(expirationDate);
        return results;
    }

    public List<InsuranceVerification_L_Code> findByExpirationDateWhereInsuranceVerificationIsActiveAndRxHasNoClaimSubmission(String expirationDate) {
        List<InsuranceVerification_L_Code> results = insuranceVerificationLCodeRepository
                .findByExpirationDateWhereInsuranceVerificationIsActiveAndRxHasNoClaimSubmission(expirationDate);
        return results;
    }

    public DataModel getIvlcsByPrescriptionId(Long prescriptionId) {
        DataModel dm = new DataModel();
        dm.setHeader(InsuranceVerificationSQL.ivlcsByPrescriptionIdHeader);
        dm.setData(insuranceVerificationLCodeRepository.getIvlcsByPrescriptionId(prescriptionId));
        return dm;
    }

    public List<Object[]> getProperSalesNumbersByPrescriptionId(Long prescriptionId) {
        List<Object[]> result = insuranceVerificationLCodeRepository.getProperSalesNumbersByPrescriptionId(prescriptionId);
        return result;
    }

    public List<InsuranceVerification_L_Code> getPrimaryIvlcForPrescriptionByPrescriptionId(Long prescriptionId) {
        List<InsuranceVerification_L_Code> result = insuranceVerificationLCodeRepository.getPrimaryIvlcForPrescriptionByPrescriptionId(prescriptionId);
        return result;
    }

    public BigDecimal calculateIVLCSalesTax(InsuranceVerification_L_Code ivlc) {
        BigDecimal salesTax = BigDecimal.ZERO;
        Branch b = ivlc != null && ivlc.getPrescriptionLCode() != null && ivlc.getPrescriptionLCode().getPrescription() != null ?
                ivlc.getPrescriptionLCode().getPrescription().getBranch() : null;
        if (ivlc != null && ivlc.getUseSalesTax() && b != null && b.getUseSalesTax()) {
            if (b.getSalesTaxCalculationValue() == null || b.getSalesTaxCalculationValue().equals("allowable")) {
                salesTax = ivlc.getAllowableFee().multiply(b.getSalesTax().scaleByPowerOfTen(-2)).multiply(BigDecimal.valueOf(ivlc.getPrescriptionLCode().getQuantity()));
            } else {
                salesTax = ivlc.getBillingFee().multiply(b.getSalesTax().scaleByPowerOfTen(-2)).multiply(BigDecimal.valueOf(ivlc.getPrescriptionLCode().getQuantity()));
            }
        }
        return salesTax;
    }

    public List<IInsuranceVerification_L_Code> findInterfaceByInsuranceVerificationIdAndCoveredTrue(Long insuranceVerificationId) {
        return insuranceVerification_L_CodeIRepository.findByInsuranceVerificationIdAndCoveredTrue(insuranceVerificationId);
    }

    @Override
    public void loadForeignKeys(InsuranceVerification_L_Code o) {
    }

}
