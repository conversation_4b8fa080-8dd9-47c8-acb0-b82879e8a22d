package com.nymbl.tenant.service;

import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.repository.SystemSettingRepository;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 10/19/2017.
 */
@Service
public class SystemSettingService extends AbstractTableService<SystemSetting, Long> {

    private final SystemSettingRepository systemSettingRepository;

    @Autowired
    public SystemSettingService(SystemSettingRepository systemSettingRepository) {
        super(systemSettingRepository);
        this.systemSettingRepository = systemSettingRepository;
    }

    public void loadForeignKeys(SystemSetting o) {}

    public List<SystemSetting> findBySection(String section) {
        return systemSettingRepository.findBySection(section);
    }

    public SystemSetting findBySectionAndField(String section, String field) {
        return systemSettingRepository.findBySectionAndField(section, field);
    }

    public Map<String, Object> getStringObjectMap(String section, List<SystemSetting> results) {
        Map<String, Object> map = new HashMap<>();
        map.put("section", section);
        for(SystemSetting entry : results) {
            if(isNumericField(entry.getSection(), entry.getField())) {
                map.put(entry.getField(), StringUtil.isBlank(entry.getValue()) ? BigDecimal.ZERO : new BigDecimal(entry.getValue()));
            } else
                map.put(entry.getField(), entry.getValue());
        }
        return map;
    }

    @Deprecated
    public Map<String, String> getPasswordSettings() {
        checkSetting("password", "require_mixed_case", "0");
        checkSetting("password", "require_special_character", "0");
        checkSetting("password", "minimum_length", "6");
        Map<String, String> map = createMap("password");
        return map;
    }

//    public Map<String, String> getAccountingDefaultSettings() {
//        checkSetting("accounting", "accounting_basis", "cash");
//        checkSetting("accounting", "beginning_receivables", "0");
//        checkSetting("accounting", "contractual_percentage", "0");
//        Map<String, String> map = createMap("accounting");
//        return map;
//    }

//    public Map<String, String> getClaimDefaultSettings() {
//        checkSetting("claim", "insured_id_number", "none");
//        checkSetting("claim", "patient_signature", "print_name");
//        checkSetting("claim", "insured_signature", "signature_on_file");
//        checkSetting("claim", "show_accident_date",  "box_15");
//        checkSetting("claim", "physician_id_to_use", "npi");
//        checkSetting("claim", "physician_id_qualifier", "1c");
//        checkSetting("claim", "place_of_service", "21");
//        checkSetting("claim", "rendering_provider_npi", "practitioner_npi");
//        checkSetting("claim", "rendering_provider_other_id", "leave_blank");
//        checkSetting("claim", "rendering_provider_other_id_qualifier", "0b");
//        checkSetting("claim", "tax_id_to_use", "ssn");
//        checkSetting("claim", "tax_id_number", "");
//        checkSetting("claim", "facility_taxonomy", "");
//        checkSetting("claim", "billing_taxonomy", "");
//        checkSetting("claim", "provider_information", "facility_information");
//        checkSetting("claim", "format_837", "x12");
//        checkSetting("claim", "use_icb_name_for_hcfa", "N");
//        checkSetting("claim", "use_single_claim", "N");
//        checkSetting("claim", "use_rental_auto_bill", "N");
//        Map<String, String> map = createMap("claim");
//        return map;
//    }

    @Deprecated
    public Map<String, String> getBillingDefaultSettings() {
        checkSetting("billing", "default_billing_fee_schedule", "2");
        checkSetting("billing", "downpayment_percentage", "30");
        checkSetting("billing", "delivery_percentage", "70");
        checkSetting("billing", "rural_non_rural_on", "N");
        checkSetting("billing", "enable_bulk_era", "N");
        checkSetting("billing", "enable_bulk_claims", "N");
        checkSetting("billing", "display_biller_code_field", "N");
        Map<String, String> map = createMap("billing");
        return map;
    }

    @Deprecated
    public Map<String, String> getForbinDefaultSettings() {
        checkSetting("forbin", "is_forbin_user", "N");
        checkSetting("forbin", "vgm_number", "V0000");
        checkSetting("forbin", "atp_resna_number", "000");
        Map<String, String> map = createMap("forbin");
        return map;
    }

    @Deprecated
    public Map<String, String> getPurchaseDefaultSettings() {
        checkSetting("purchasing", "enable_po_single_vendor", "0");
        checkSetting("purchasing", "enable_custom_po", "0");
        Map<String, String> map = createMap("purchasing");
        return map;
    }

    private void checkSetting(String section, String field, String value) {
        SystemSetting exists = findBySectionAndField(section, field);
        if(exists == null) {
            SystemSetting result = new SystemSetting();
            result.setSection(section);
            result.setField(field);
            result.setValue(value);
            save(result);
        }
    }

    private boolean isNumericField(String section, String field){
        String[] sections = { "accounting", "billing" };
        String[] fields = { "beginning_receivables", "contractual_percentage","default_billing_fee_schedule","downpayment_percentage", "delivery_percentage", "autopost_percentage"};
        return (ArrayUtils.contains(sections, section) && ArrayUtils.contains(fields, field));
    }

    private Map<String, String> createMap(String section){
        List<SystemSetting> settings = findBySection(section);
        Map<String, String> map = new HashMap<>();
        for(SystemSetting setting : settings)
            map.put(setting.getField(), setting.getValue());
        return map;
    }
}
