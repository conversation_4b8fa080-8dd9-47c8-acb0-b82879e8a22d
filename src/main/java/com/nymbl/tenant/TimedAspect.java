package com.nymbl.tenant;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON> on 11/05/2020.
 */
@Slf4j
@Aspect
@Component
public class TimedAspect {

    @Around(value = "@annotation(Timed)")
    public Object measureExecutionTime(final ProceedingJoinPoint joinPoint) throws Throwable {
        String tenant = TenantContext.getCurrentTenant();
        long start = System.currentTimeMillis();
        Object proceed = joinPoint.proceed();
        long ms = System.currentTimeMillis() - start;
        String message = String.format("%d min, %d sec",
                TimeUnit.MILLISECONDS.toMinutes(ms),
                TimeUnit.MILLISECONDS.toSeconds(ms) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(ms))
        );
        log.info("{} executed in {} on {}", joinPoint.getSignature().toShortString(), message, tenant);
        return proceed;
    }
}
