package com.nymbl.tenant.controller;

import com.nymbl.config.Constants;
import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.dto.ClaimSearchDTO;
import com.nymbl.config.dto.PhysicianBillingEditDTO;
import com.nymbl.config.dto.StatementDTO;
import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.security.PrivilegeChecker;
import com.nymbl.config.service.ClaimProfileService;
import com.nymbl.config.service.Print1500Service;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.tenant.Auditable;
import com.nymbl.tenant.dashboard.dto.ClaimDto;
import com.nymbl.tenant.dashboard.service.ClaimsDtoService;
import com.nymbl.tenant.dto.BulkClaimRequest;
import com.nymbl.tenant.dto.ClaimCreationRequest;
import com.nymbl.tenant.dto.ClaimFilesRequest;
import com.nymbl.tenant.forms.PdfReaderService;
import com.nymbl.tenant.interfaces.IClaimTotals;
import com.nymbl.tenant.model.AutoPostClaimResponse;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.PatientInsurance;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.PaymentService;
import com.nymbl.tenant.service.StatementService;
import com.nymbl.tenant.service.SystemSettingService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

/**
 * Created by Bradley Moore on 05/21/2017.
 */
@Slf4j
@RestController
@RequestMapping("/api/claim")
public class ClaimController extends AbstractController<Claim, Long> {

    private static final Logger log = LoggerFactory.getLogger(ClaimController.class);

    private final ClaimService claimService;
    private final StatementService statementService;
    private final ClaimProfileService claimProfileService;
    private final FileUtil fileUtil;
    private final ClaimsDtoService claimsDtoService;
    private final Print1500Service print1500Service;
    private final PdfReaderService pdfReaderService;
    private final PaymentService paymentService;
    private final BulkClaimJobService bulkClaimJobService;
    private final PrivilegeChecker privilegeChecker;
    private final SystemSettingService systemSettingService;

    @Autowired
    public ClaimController(ClaimService claimService,
                           StatementService statementService,
                           ClaimProfileService claimProfileService,
                           FileUtil fileUtil,
                           ClaimsDtoService claimsDtoService,
                           Print1500Service print1500Service,
                           PdfReaderService pdfReaderService,
                           PaymentService paymentService,
                           BulkClaimJobService bulkClaimJobService,
                           PrivilegeChecker privilegeChecker,
                           SystemSettingService systemSettingService) {
        super(claimService);
        this.claimService = claimService;
        this.statementService = statementService;
        this.claimProfileService = claimProfileService;
        this.fileUtil = fileUtil;
        this.claimsDtoService = claimsDtoService;
        this.print1500Service = print1500Service;
        this.pdfReaderService = pdfReaderService;
        this.paymentService = paymentService;
        this.bulkClaimJobService = bulkClaimJobService;
        this.privilegeChecker = privilegeChecker;
        this.systemSettingService = systemSettingService;
    }

    @GetMapping(value = "/patient/{patientId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPatientId(@PathVariable Long patientId,
                                             @RequestParam(name = "claimFor", required = false) Integer claimFor,
                                             HttpServletRequest request) {
        if (claimFor == null) claimFor = 0;
        List<Claim> results = claimService.findByPrescriptionPatientId(patientId, claimFor);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/updateAt/totalClaimBalance/totalPtResponsibilityBalance", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId(@RequestParam(name = "branchId", required = false) Long branchId, HttpServletRequest request) {
        List<Claim> results = claimService.findAllByUpdatedAtBeforeAndTotalClaimBalanceGreaterThanOrTotalPtResponsibilityBalanceGreaterThanAndBranchId(branchId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/prescription/{prescriptionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPrescriptionId(@PathVariable Long prescriptionId,
                                                  @RequestParam(name = "claimFor", required = false) Integer claimFor,
                                                  HttpServletRequest request) {
        List<Claim> results = claimService.findByPrescriptionId(prescriptionId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/autopostclaimresponses/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findAutoPostClaimResponsesById(@PathVariable Long id, HttpServletRequest request) {
        List<AutoPostClaimResponse> result = claimService.getAutoPostClaimResponseByClaimId(id);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "/view/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByClaimId(@PathVariable Long id, HttpServletRequest request) {
        Claim result = claimService.findOne(id);
        return ResponseEntity.ok(result);
    }

    @Auditable(entry = "Update Claim")
    @Override
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> save(@RequestBody Claim claim, HttpServletRequest request) {
        Map<String, Object> respMap;
        try {
                respMap = claimService.saveForVersion(claim);

                if (null != respMap.get(SAVED)) {
                    return ResponseEntity.ok(respMap.get(SAVED));
                }

            return ResponseEntity.badRequest().body(respMap);
        } catch ( Exception e){
            String exceptionAsString = StringUtil.getExceptionAsString(e);
            log.error(exceptionAsString);
            return ResponseEntity.badRequest().body(StringUtil.getExceptionAsString(e));
            }
        }

    @Auditable(entry = "Add Claim By Prescription Id")
    @GetMapping(value = "/add/{prescriptionId}")
    public ResponseEntity<?> addClaim(@PathVariable Long prescriptionId,
                                      @RequestParam(name = "userId", required = false) Long userId,
                                      @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                      @RequestParam(name = "dateOfService", required = false) java.sql.Date dateOfService,
                                      @RequestParam(name = "resend", required = false) boolean resend,
                                      HttpServletRequest request) throws Exception {
        Claim result = claimService.addClaim(prescriptionId, userId, billingBranchId, dateOfService, resend);
        List<Claim> list = claimService.findByPrescriptionId(prescriptionId);
        claimService.auditNewlyAddedClaim(list);
        return ResponseEntity.ok(result);
    }

    /**
     * Add a claim using POST method.
     * This endpoint is CSRF-protected and should be used instead of the GET endpoint.
     *
     * @param request The request object containing prescription ID and other parameters
     * @param httpRequest The HTTP servlet request
     * @return ResponseEntity with the created claim or error message
     */
    @Auditable(entry = "Add Claim By Prescription Id")
    @PostMapping(value = "/add", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> addClaimPost(@RequestBody ClaimCreationRequest request,
                                      HttpServletRequest httpRequest) throws Exception {
        // Validate request
        if (request.getPrescriptionId() == null) {
            return ResponseEntity.badRequest().body("Prescription ID is required");
        }

        Claim result = claimService.addClaim(
            request.getPrescriptionId(),
            request.getUserId(),
            request.getBillingBranchId(),
            request.getDateOfService(),
            request.isResend()
        );

        List<Claim> list = claimService.findByPrescriptionId(request.getPrescriptionId());
        claimService.auditNewlyAddedClaim(list);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> search(@RequestParam(name = "claimId", required = false) Long claimId,
                                    @RequestParam(name = "patientId", required = false) Long patientId,
                                    @RequestParam(name = "status", required = false) String status,
                                    @RequestParam(name = "nymblStatusId", required = false) Long nymblStatusId,
                                    @RequestParam(name = "branchId", required = false) Long branchId,
                                    @RequestParam(name = "insuranceCompanyId", required = false) Long insuranceCompanyId,
                                    @RequestParam(name = "userId", required = false) Long userId,
                                    @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                    @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                    @RequestParam(name = "showUnresolved", required = false) Boolean showUnresolved,
                                    @RequestParam(name = "dosStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosStartDate,
                                    @RequestParam(name = "dosEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosEndDate,
                                    @RequestParam(name = "prescriptionId", required = false) Long prescriptionId,
                                    Boolean export,
                                    Pageable pageable,
                                    HttpServletRequest request) {
        java.sql.Date sqlStartDate = startDate != null ? new java.sql.Date(startDate.getTime()) : null;
        java.sql.Date sqlEndDate = endDate != null ? new java.sql.Date(endDate.getTime()) : null;
        java.sql.Date sqlDosStartDate = dosStartDate != null ? new java.sql.Date(dosStartDate.getTime()) : null;
        java.sql.Date sqlDosEndDate = dosEndDate != null ? new java.sql.Date(dosEndDate.getTime()) : null;
        Page<ClaimSearchDTO> results = claimService.sortSearch(claimId, patientId, status, nymblStatusId
                , branchId, insuranceCompanyId, userId, sqlStartDate, sqlEndDate, sqlDosStartDate
                , sqlDosEndDate, showUnresolved, pageable, export, prescriptionId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/export-claims", produces = MediaType.APPLICATION_JSON_VALUE)
    public String exportClaims(@RequestParam(name = "claimId", required = false) Long claimId,
                               @RequestParam(name = "patientId", required = false) Long patientId,
                               @RequestParam(name = "status", required = false) String status,
                               @RequestParam(name = "nymblStatusId", required = false) Long nymblStatusId,
                               @RequestParam(name = "branchId", required = false) Long branchId,
                               @RequestParam(name = "insuranceCompanyId", required = false) Long insuranceCompanyId,
                               @RequestParam(name = "userId", required = false) Long userId,
                               @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                               @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                               @RequestParam(name = "showUnresolved", required = false) Boolean showUnresolved,
                               @RequestParam(name = "dosStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosStartDate,
                               @RequestParam(name = "dosEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosEndDate,
                               @RequestParam(name = "prescriptionId", required = false) Long prescriptionId,
                               Boolean export,
                               Pageable pageable,
                               HttpServletRequest request) {
        java.sql.Date sqlStartDate = startDate != null ? new java.sql.Date(startDate.getTime()) : null;
        java.sql.Date sqlEndDate = endDate != null ? new java.sql.Date(endDate.getTime()) : null;
        java.sql.Date sqlDosStartDate = dosStartDate != null ? new java.sql.Date(dosStartDate.getTime()) : null;
        java.sql.Date sqlDosEndDate = dosEndDate != null ? new java.sql.Date(dosEndDate.getTime()) : null;
        String results = claimService.exportClaims(claimId, patientId, status, nymblStatusId, branchId, insuranceCompanyId
                , userId, sqlStartDate, sqlEndDate, sqlDosStartDate, sqlDosEndDate, showUnresolved, pageable, export, prescriptionId);
        return results;
    }

    /**
     * GET /bulk-job-status/:jobId : Get the status of a bulk claim job.
     *
     * @param jobId the job ID
     * @return the ResponseEntity with status 200 (OK) and the job status in the body
     */
    @GetMapping("/bulk-job-status/{jobId}")
    public ResponseEntity<?> getBulkJobStatus(@PathVariable String jobId) {
        log.debug("REST request to get bulk claim job status : {}", jobId);
        BulkClaimJob job = bulkClaimJobService.findByJobId(jobId);
        if (job == null) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Job not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        }

        // Convert the entity to DTO before returning
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(com.nymbl.tenant.dto.BulkClaimJobDTO.fromEntity(job));
    }

    @Auditable(entry = "Send Claim Files")
    @PostMapping(value = "/send-files", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> sendClaimFilesPost(@RequestBody ClaimFilesRequest request,
                                           HttpServletRequest httpRequest) throws X12Exception {
        log.debug("REST request to send claim files");

        try {
            List<Long> claimIds = request.getClaimIds();
            if (claimIds == null || claimIds.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("error", "Claim IDs are required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            Long billingBranchId = request.getBillingBranchId();

            // If submission type is bulk, use bulk submission
            if ("bulk".equals(request.getSubmissionType())) {
                // Check if bulk claims are enabled in system settings
                SystemSetting setting = systemSettingService.findBySectionAndField("billing", "enable_bulk_claims");
                String enableBulkClaims = setting != null ? setting.getValue() : "N";
                if (!"Y".equals(enableBulkClaims)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(Map.of(
                                    "success", false,
                                    "error", "Bulk claims feature is not enabled for this tenant"
                            ));
                }

                // Check if user has the bulk_claim_submit privilege
                if (!privilegeChecker.hasPrivilege("bulk_claim_submit")) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(Map.of(
                                    "success", false,
                                    "error", "You do not have permission to submit bulk claims"
                            ));
                }

                Map<String, Object> result = claimService.sendBulkClaimFiles(claimIds, billingBranchId);
                return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(result);
            } else {
                // Otherwise, use the existing single claim submission
                boolean success = claimService.sendClaimFiles(claimIds, billingBranchId);
                Map<String, Object> result = new HashMap<>();
                result.put("success", success);

                if (success) {
                    result.put("data", "Claims sent successfully.");
                    return ResponseEntity.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(result);
                } else {
                    result.put("error", "Claims did not pass edit rules.");
                    return ResponseEntity.badRequest()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(result);
                }
            }
        } catch (X12Exception e) {
            // Handle X12Exception directly
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (Exception e) {
            log.error("Error sending claims: " + e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Error sending claims: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Validate claims for bulk submission.
     * This endpoint checks if the selected claims can be submitted together in a bulk file.
     *
     * @param request The request object containing claim IDs to validate
     * @return ResponseEntity with validation results
     */
    @PostMapping(value = "/validate-bulk-submission", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateBulkSubmission(@RequestBody BulkClaimRequest request) {
        // Check if bulk claims are enabled in system settings
        try {
            SystemSetting setting = systemSettingService.findBySectionAndField("billing", "enable_bulk_claims");
            String enableBulkClaims = setting != null ? setting.getValue() : "N";
            if (!"Y".equals(enableBulkClaims)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of(
                                "valid", false,
                                "errors", List.of("Bulk claims feature is not enabled for this tenant")
                        ));
            }

            // Check if user has the bulk_claim_submit privilege
            if (!privilegeChecker.hasPrivilege("bulk_claim_submit")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of(
                                "valid", false,
                                "errors", List.of("You do not have permission to submit bulk claims")
                        ));
            }

            Map<String, Object> response = new HashMap<>();
            List<String> errors = claimService.validateBulkSubmission(request.getClaimIds());

            response.put("valid", errors.isEmpty());
            response.put("errors", errors);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error validating bulk submission: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                            "valid", false,
                            "errors", List.of("Error validating bulk submission: " + e.getMessage())
                    ));
        }
    }

    @GetMapping(value = "/view-files/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> viewFilesAll(@RequestParam(name = "claimId") Long claimId,
                                          @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                          @RequestParam(name = "patientInsuranceId", required = false) Long patientInsuranceId,
                                          @RequestParam(name = "otherPatientInsuranceId", required = false) Long otherPatientInsuranceId,
                                          @RequestParam(name = "form1500TemplateId", required = false) Long form1500TemplateId,
                                          HttpServletRequest request) throws IOException {

        // Check if this is an actual download request or just a page load
        String userAgent = request.getHeader("User-Agent");
        String acceptHeader = request.getHeader("Accept");

        // Check for specific download request indicators
        boolean isExplicitDownloadRequest =
            // Check if this is a direct download request (has specific Accept header)
            (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_JSON_VALUE)) ||
            // Check for download attribute in request parameters
            request.getParameter("download") != null ||
            // Check for specific referer patterns that indicate user-initiated downloads
            (request.getHeader("Referer") != null &&
             request.getHeader("Referer").contains("download=true"));

        // Only generate PDF file for actual download requests
        if (!isExplicitDownloadRequest && userAgent != null && userAgent.contains("Mozilla")) {
            log.info("Page load request detected, not generating PDF file");
            Map<String, Object> emptyResponse = new HashMap<>();
            emptyResponse.put("result_full", new byte[0]);
            emptyResponse.put("result_blank", new byte[0]);
            emptyResponse.put("validation_errors", new ArrayList<>());
            return ResponseEntity.ok(emptyResponse);
        }
        // Full
        Map<String, List<String>> result = print1500Service.getAllPrintClaimFiles(claimId, true, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);

        Map<String, Object> response = new HashMap<>();

        // Check if we have any files to process
        if (result.get("files") == null || result.get("files").isEmpty()) {
            log.info("No PDF files to process for claim {}", claimId);
            response.put("result_full", new byte[0]);
        } else {
            String path = result.get("files").size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "filled_");
            try {
                byte[] contents = fileUtil.loadEobContents(path);
                response.put("result_full", contents);
                pdfReaderService.delete1500pdfForms(path);
            } catch (Exception e) {
                log.error("Error loading PDF file: {}", e.getMessage());
                response.put("result_full", new byte[0]);
            }
        }
        // Blank
        result = print1500Service.getAllPrintClaimFiles(claimId, false, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);

        // Check if we have any files to process
        if (result.get("files") == null || result.get("files").isEmpty()) {
            log.info("No blank PDF files to process for claim {}", claimId);
            response.put("result_blank", new byte[0]);
        } else {
            String blankPath = result.get("files").size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "blank_");
            try {
                byte[] blankContents = fileUtil.loadEobContents(blankPath);
                response.put("result_blank", blankContents);
                pdfReaderService.delete1500pdfForms(blankPath);
            } catch (Exception e) {
                log.error("Error loading blank PDF file: {}", e.getMessage());
                response.put("result_blank", new byte[0]);
            }
        }

        if (!result.get("validation_errors").isEmpty()) {
            response.put("validation_errors", result.get("validation_errors"));
        }
        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/view-files/full", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> viewFilesFull(@RequestParam(name = "claimId") Long claimId,
                                           @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                           @RequestParam(name = "patientInsuranceId", required = false) Long patientInsuranceId,
                                           @RequestParam(name = "otherPatientInsuranceId", required = false) Long otherPatientInsuranceId,
                                           @RequestParam(name = "form1500TemplateId", required = true) Long form1500TemplateId,
                                           HttpServletRequest request) throws IOException {

        // Check if this is an actual download request or just a page load
        String userAgent = request.getHeader("User-Agent");
        String acceptHeader = request.getHeader("Accept");

        // Check for specific download request indicators
        boolean isExplicitDownloadRequest =
            // Check if this is a direct download request (has specific Accept header)
            (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_JSON_VALUE)) ||
            // Check for download attribute in request parameters
            request.getParameter("download") != null ||
            // Check for specific referer patterns that indicate user-initiated downloads
            (request.getHeader("Referer") != null &&
             request.getHeader("Referer").contains("download=true"));

        // Only generate PDF file for actual download requests
        if (!isExplicitDownloadRequest && userAgent != null && userAgent.contains("Mozilla")) {
            log.info("Page load request detected, not generating PDF file");
            Map<String, Object> emptyResponse = new HashMap<>();
            emptyResponse.put("result", new byte[0]);
            emptyResponse.put("validation_errors", new ArrayList<>());
            return ResponseEntity.ok(emptyResponse);
        }

        Map<String, List<String>> result = print1500Service.getAllPrintClaimFiles(claimId, true, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);
        Map<String, Object> response = new HashMap<>();

        // Check if we have any files to process
        if (result.get("files") == null || result.get("files").isEmpty()) {
            log.info("No PDF files to process for claim {}", claimId);
            response.put("result", new byte[0]);
        } else {
            String path = result.get("files").size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "filled_");
            try {
                byte[] contents = fileUtil.loadEobContents(path);
                response.put("result", contents);
                pdfReaderService.delete1500pdfForms(path);
            } catch (Exception e) {
                log.error("Error loading PDF file: {}", e.getMessage());
                response.put("result", new byte[0]);
            }
        }

        if (result.get("validation_errors") != null && !result.get("validation_errors").isEmpty()) {
            response.put("validation_errors", result.get("validation_errors"));
        }

        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/view-files/blank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> viewFilesBlank(@RequestParam(name = "claimId") Long claimId,
                                            @RequestParam(name = "billingBranchId", required = false) Long billingBranchId,
                                            @RequestParam(name = "patientInsuranceId", required = false) Long patientInsuranceId,
                                            @RequestParam(name = "otherPatientInsuranceId", required = false) Long otherPatientInsuranceId,
                                            @RequestParam(name = "form1500TemplateId", required = true) Long form1500TemplateId,
                                            HttpServletRequest request) throws IOException {

        // Check if this is an actual download request or just a page load
        String userAgent = request.getHeader("User-Agent");
        String acceptHeader = request.getHeader("Accept");

        // Check for specific download request indicators
        boolean isExplicitDownloadRequest =
            // Check if this is a direct download request (has specific Accept header)
            (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_JSON_VALUE)) ||
            // Check for download attribute in request parameters
            request.getParameter("download") != null ||
            // Check for specific referer patterns that indicate user-initiated downloads
            (request.getHeader("Referer") != null &&
             request.getHeader("Referer").contains("download=true"));

        // Only generate PDF file for actual download requests
        if (!isExplicitDownloadRequest && userAgent != null && userAgent.contains("Mozilla")) {
            log.info("Page load request detected, not generating PDF file");
            Map<String, Object> emptyResponse = new HashMap<>();
            emptyResponse.put("result", new byte[0]);
            emptyResponse.put("validation_errors", new ArrayList<>());
            return ResponseEntity.ok(emptyResponse);
        }

        Map<String, List<String>> result = print1500Service.getAllPrintClaimFiles(claimId, false, billingBranchId, patientInsuranceId, otherPatientInsuranceId, form1500TemplateId);
        Map<String, Object> response = new HashMap<>();

        // Check if we have any files to process
        if (result.get("files") == null || result.get("files").isEmpty()) {
            log.info("No blank PDF files to process for claim {}", claimId);
            response.put("result", new byte[0]);
        } else {
            String path = result.get("files").size() == 1 ? result.get("files").get(0) : pdfReaderService.mergePdfs(result.get("files"), claimId, "blank_");
            try {
                byte[] contents = fileUtil.loadEobContents(path);
                response.put("result", contents);
                pdfReaderService.delete1500pdfForms(path);
            } catch (Exception e) {
                log.error("Error loading blank PDF file: {}", e.getMessage());
                response.put("result", new byte[0]);
            }
        }

        if (result.get("validation_errors") != null && !result.get("validation_errors").isEmpty()) {
            response.put("validation_errors", result.get("validation_errors"));
        }

        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/bulk-update-status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> bulkUpdateStatus(@RequestParam(name = "claimIds") List<Long> claimIds,
    										  @RequestParam(name = "nymblStatusId") Long nymblStatusId,
                                              HttpServletRequest request) {
        claimService.bulkUpdateClaimStatus(claimIds, nymblStatusId);
    	return ResponseEntity.ok(null);
    }

    @GetMapping(value = "/reassign-claim", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> reassignClaims(@RequestParam(name = "claimIds") List<Long> claimIds,
                                            @RequestParam(name = "userId") Long userId,
                                            HttpServletRequest request) {
        claimService.reassignClaims(claimIds, userId);
        return ResponseEntity.ok(null);
    }

    @GetMapping(value = "/statements/patients", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findPatientStatements(@RequestParam(name = "patientId", required = false) Long patientId,
                                                   @RequestParam(name = "branchId", required = false) Long branchId,
                                                   @RequestParam(name = "insuranceCompanyId", required = false) Long insuranceCompanyId,
                                                   @RequestParam(name = "aging", required = false) Integer aging,
                                                   @RequestParam(name = "submittedStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date submittedStartDate,
                                                   @RequestParam(name = "submittedEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date submittedEndDate,
                                                   @RequestParam(name = "lastSentStartDate", required = false) String lastSentStartDate,
                                                   @RequestParam(name = "lastSentEndDate", required = false) String lastSentEndDate,
                                                   @RequestParam(name = "attempt", required = false) String attempt,
                                                   @RequestParam(name = "dosStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosStartDate,
                                                   @RequestParam(name = "dosEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dosEndDate,
                                                   @RequestParam(name = "claimId", required = false) Long claimId,
                                                   @RequestParam(name = "prescriptionId", required = false) Long prescriptionId,
                                                   @RequestParam(name = "unappliedPayments", required = false) Boolean unappliedPayments,
                                                   Pageable pageable,
                                                   HttpServletRequest request) {
        java.sql.Date startDate = lastSentStartDate != null ? DateUtil.getDate(lastSentStartDate, Constants.DF_YYYY_MM_DD) : null;
        java.sql.Date endDate = lastSentEndDate != null ? DateUtil.getDate(lastSentEndDate, Constants.DF_YYYY_MM_DD) : null;
        java.sql.Date sqlDosStartDate = dosStartDate != null ? new java.sql.Date(dosStartDate.getTime()) : null;
        java.sql.Date sqlDosEndDate = dosEndDate != null ? new java.sql.Date(dosEndDate.getTime()) : null;
        Page result = statementService.statements(patientId, branchId, startDate, endDate, sqlDosStartDate, sqlDosEndDate, attempt, claimId, prescriptionId, unappliedPayments, pageable);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "/export-dates", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> updatePatientExportDates(@RequestBody List<StatementDTO> statementDTOs,
                                                      HttpServletRequest request) throws Exception {
        List<StatementDTO> results = statementService.updatePatientExportDates(statementDTOs, false);
        return ResponseEntity.ok(results);

    }

    @PostMapping(value = "statements/patients/export", produces = "text/plain;charset=UTF-8")
    @ResponseBody
    public ResponseEntity<?> createPatientStatementExportFile(@RequestBody List<StatementDTO> statementDTOS,
                                                              HttpServletRequest request) {
        String result = statementService.writeStatementToXMLFile(statementDTOS);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "statements/collections/export", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> exportStatementsSentToCollections(@RequestBody List<StatementDTO> statementDTOS, HttpServletRequest request) {
        String result = statementService.exportStatementsSentToCollections(statementDTOS);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "/update/physicians", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> updatePhysicians(@RequestBody PhysicianBillingEditDTO dto, HttpServletRequest request) {
        Claim result = claimService.updateClaimPhysicianInfo(dto);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "profile/{patientId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> profileLoad(@PathVariable Long patientId,
                                         @RequestParam(name = "getArchived", required = false) boolean getArchived) {
        return ResponseEntity.ok(claimProfileService.load(patientId, getArchived));
    }

    @GetMapping(value = "transaction-history/claim/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> loadTransactionHistoryByClaimId(@PathVariable Long claimId) {
        return ResponseEntity.ok(paymentService.loadTransactionHistoryByClaimId(claimId));
    }

    @GetMapping(value = "transaction-history/prescription/{prescriptionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> loadTransactionHistoryByPrescriptionId(@PathVariable Long prescriptionId) {
        return ResponseEntity.ok(paymentService.loadTransactionHistoryByPrescriptionId(prescriptionId));
    }

    @GetMapping(value = "projected-vs-billed", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getProjectedVsBilled(@RequestParam(name = "branchIds") List<Long> branchIds,
                                                  @RequestParam(name = "isPatient", required = true) boolean isPatient,
                                                  @RequestParam(name = "isPrescription", required = true) boolean isPrescription,
                                                  HttpServletRequest request) {
        List<Map<String, String>> results = claimService.getProjectedVsBilled(branchIds, isPatient, isPrescription);
        return ResponseEntity.ok(results);
    }

    @PostMapping(value = "/delete", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> deleteClaim(@RequestBody Claim claim,
                                         HttpServletRequest request) {
        ResponseEntity<?> response = claimService.deleteClaim(claim.getId());
        if (response.getStatusCode().equals(HttpStatus.OK)) claimService.auditDeleteClaim(claim);
        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/updateAt/totalClaimBalance/totalPtResponsibilityBalanceDto", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findClaimsWithNoActivityDashboard(@RequestParam(name = "branchId", required = false) Long branchId, HttpServletRequest request) {
        List<ClaimDto> results = claimsDtoService.findClaimsWithNoActivityDashboard(branchId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/totals/{rxId}/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getClaimTotals(@PathVariable Long rxId,
                                            @PathVariable Long claimId,
                                            HttpServletRequest request) {
        IClaimTotals result = claimService.getClaimTotals(claimId, rxId);
        return ResponseEntity.ok(result);
    }

    @Data
    static class ClaimDTO {
        private List<Long> claimIds;
        private Long billingBranchId;

        public ClaimDTO() {
        }
    }
}
