package com.nymbl.tenant.batch.config;

import com.nymbl.tenant.batch.dto.QuicksightTenantUserPermission;
import com.nymbl.tenant.batch.listeners.AwsJobListener;
import com.nymbl.tenant.batch.listeners.AwsStepListener;
import com.nymbl.tenant.batch.listeners.ChunkCountListener;
import com.nymbl.tenant.batch.processors.TenantUserPermissionRowMapper;
import com.nymbl.tenant.batch.service.CSVTransferS3Quicksight;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.jdbc.pool.DataSource;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.configuration.support.DefaultBatchConfiguration;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.database.StoredProcedureItemReader;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.builder.FlatFileItemWriterBuilder;
import org.springframework.batch.item.file.transform.BeanWrapperFieldExtractor;
import org.springframework.batch.item.file.transform.DelimitedLineAggregator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.WritableResource;
import org.springframework.transaction.PlatformTransactionManager;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

/**
 * Created by Bradley Moore on 8/19/24.
 */
@Slf4j
@Configuration
public class TenantUserPermissionsBatchConfiguration extends DefaultBatchConfiguration {

    @Qualifier("masterTransactionManager")
    public final PlatformTransactionManager masterTransactionManager;
    private final AwsStepListener awsStepListener;
    private final CSVTransferS3Quicksight csvTransferS3Quicksight;
    private final DataSource dataSource;

    public TenantUserPermissionsBatchConfiguration(PlatformTransactionManager masterTransactionManager,
                                                   AwsStepListener awsStepListener,
                                                   CSVTransferS3Quicksight csvTransferS3Quicksight,
                                                   DataSource dataSource) {
        this.masterTransactionManager = masterTransactionManager;
        this.awsStepListener = awsStepListener;
        this.csvTransferS3Quicksight = csvTransferS3Quicksight;
        this.dataSource = dataSource;
    }

    @NotNull
    @Override
    protected PlatformTransactionManager getTransactionManager() {
        return masterTransactionManager;
    }

    @Bean(name = "tenantUserPermissionsBatchJob")
    public Job tenantUserPermissionsBatchJob(AwsJobListener listener, JobRepository jobRepository) {
        return new JobBuilder("tenantUserPermissionsBatchJob", jobRepository)
                .incrementer(new RunIdIncrementer())
                .listener(listener)
                .flow(toQsTenantUserPermissionsCreateTempFile(jobRepository))
                .next(toQsTenantUserPermissionsAwsStep(jobRepository))
                .end()
                .build();
    }

    @Bean
    public Step toQsTenantUserPermissionsCreateTempFile(JobRepository jobRepository) {
        return new StepBuilder("toQsTenantUserPermissionsCreateTempFile", jobRepository)
                .<QuicksightTenantUserPermission, QuicksightTenantUserPermission>chunk(1000, masterTransactionManager)
                .reader(qsTenantUserPermissionsStoredProcedureItemReader(null))
                .writer(qsTenantUserPermissionsFlatFileItemWriter(null, null))
                .listener(new ChunkCountListener("User Permissions"))
                .listener(awsStepListener)
                .build();
    }

    @Bean
    public Step toQsTenantUserPermissionsAwsStep(JobRepository jobRepository) {
        return new StepBuilder("toQsTenantUserPermissionsStep", jobRepository).tasklet(csvTransferS3Quicksight, masterTransactionManager).build();
    }

    public StoredProcedureItemReader<QuicksightTenantUserPermission> qsTenantUserPermissionsStoredProcedureItemReader(@Value("#{jobParameters['tenant']}") String tenant) {
        StoredProcedureItemReader<QuicksightTenantUserPermission> reader = new StoredProcedureItemReader<>();
        reader.setDataSource(dataSource);
        reader.setProcedureName("userTenantPermissions");
        reader.setRowMapper(new TenantUserPermissionRowMapper());
        try {
            reader.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error while reading user permissions", e);
        }
        return reader;
    }

    @Bean
    @StepScope
    public FlatFileItemWriter<QuicksightTenantUserPermission> qsTenantUserPermissionsFlatFileItemWriter(@Value("#{jobParameters['tenant']}") String tenant, @Value("#{jobParameters['filePath']}") String filePath) {
        Field[] fields = QuicksightTenantUserPermission.class.getDeclaredFields();
        List<String> names = Arrays.stream(fields).map(Field::getName).toList();
        String[] arrayFieldNames = new String[names.size()];
        arrayFieldNames = names.toArray(arrayFieldNames);
        final String header = String.join(",", arrayFieldNames);

        BeanWrapperFieldExtractor<QuicksightTenantUserPermission> fieldExtractor = new BeanWrapperFieldExtractor<>();
        fieldExtractor.setNames(arrayFieldNames);
        fieldExtractor.afterPropertiesSet();
        DelimitedLineAggregator<QuicksightTenantUserPermission> lineAggregator = new DelimitedLineAggregator<QuicksightTenantUserPermission>();
        lineAggregator.setFieldExtractor(fieldExtractor);
        WritableResource resource = new FileSystemResource(filePath);
        return new FlatFileItemWriterBuilder<QuicksightTenantUserPermission>()
                .name("itemWriter")
                .append(true)
                .shouldDeleteIfEmpty(false)
                .shouldDeleteIfExists(true)
                .lineAggregator(lineAggregator)
                .resource(resource)
                .headerCallback(writer -> writer.write(header))
                .build();
    }
}
