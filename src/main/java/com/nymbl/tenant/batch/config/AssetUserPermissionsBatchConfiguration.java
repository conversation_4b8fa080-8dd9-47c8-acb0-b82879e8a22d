package com.nymbl.tenant.batch.config;

import com.nymbl.tenant.batch.dto.QuicksightAssetUserPermissions;
import com.nymbl.tenant.batch.listeners.AwsJobListener;
import com.nymbl.tenant.batch.listeners.AwsStepListener;
import com.nymbl.tenant.batch.listeners.ChunkCountListener;
import com.nymbl.tenant.batch.processors.AssetUserPermissionsRowMapper;
import com.nymbl.tenant.batch.service.CSVTransferS3Quicksight;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.jdbc.pool.DataSource;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.configuration.support.DefaultBatchConfiguration;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.database.StoredProcedureItemReader;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.builder.FlatFileItemWriterBuilder;
import org.springframework.batch.item.file.transform.BeanWrapperFieldExtractor;
import org.springframework.batch.item.file.transform.DelimitedLineAggregator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.WritableResource;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * Created by Bradley Moore on 8/2/24.
 */
@Slf4j
@Configuration
public class AssetUserPermissionsBatchConfiguration extends DefaultBatchConfiguration {

    @Qualifier("masterTransactionManager")
    public final PlatformTransactionManager masterTransactionManager;
    private final AwsStepListener awsStepListener;
    private final CSVTransferS3Quicksight csvTransferS3Quicksight;
    private final DataSource dataSource;

    public AssetUserPermissionsBatchConfiguration(PlatformTransactionManager masterTransactionManager,
                                                  AwsStepListener awsStepListener,
                                                  CSVTransferS3Quicksight csvTransferS3Quicksight,
                                                  DataSource dataSource) {

        this.masterTransactionManager = masterTransactionManager;
        this.awsStepListener = awsStepListener;
        this.csvTransferS3Quicksight = csvTransferS3Quicksight;
        this.dataSource = dataSource;
    }

    @NotNull
    @Override
    protected PlatformTransactionManager getTransactionManager() {
        return masterTransactionManager;
    }

    @Bean(name = "assetUserPermissionsCSVToAWSJob")
    public Job assetUserPermissionsCSVToAWSJob(AwsJobListener listener, JobRepository jobRepository) {
        return new JobBuilder("assetUserPermissionsCSVToAWSJob", jobRepository)
                .incrementer(new RunIdIncrementer())
                .listener(listener)
                .flow(toQsAssetUserPermissionsCreateTempFile(jobRepository))
                .next(toQsAssetUserPermissionsAwsStep(jobRepository))
                .end()
                .build();
    }

    @Bean
    public Step toQsAssetUserPermissionsCreateTempFile(JobRepository jobRepository) {
        return new StepBuilder("toQsAssetUserPermissionsCreateTempFile", jobRepository)
                .<QuicksightAssetUserPermissions, QuicksightAssetUserPermissions>chunk(1000, masterTransactionManager)
                .reader(qsAssetUserPermissionsStoredProcedureItemReader())
                .writer(qsUserPermissionsFlatFileItemWriter(null))
                .listener(new ChunkCountListener("User Asset Permissions"))
                .listener(awsStepListener)
                .build();
    }

    @Bean
    public Step toQsAssetUserPermissionsAwsStep(JobRepository jobRepository) {
        return new StepBuilder("toQsAssetUserPermissionsAwsStep", jobRepository).tasklet(csvTransferS3Quicksight, masterTransactionManager).build();
    }

    @Bean
    @StepScope
    public StoredProcedureItemReader<QuicksightAssetUserPermissions> qsAssetUserPermissionsStoredProcedureItemReader() {
        StoredProcedureItemReader<QuicksightAssetUserPermissions> reader = new StoredProcedureItemReader<>();
        reader.setDataSource(dataSource);
        reader.setProcedureName("userAssetPermissions");
        reader.setRowMapper(new AssetUserPermissionsRowMapper());
        try {
            reader.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error while reading user asset permissions", e);
        }
        return reader;
    }

    @Bean
    @StepScope
    public FlatFileItemWriter<QuicksightAssetUserPermissions> qsUserPermissionsFlatFileItemWriter(@Value("#{jobParameters['filePath']}") String filePath) {

        final String header = "UserName,tenant";

        BeanWrapperFieldExtractor<QuicksightAssetUserPermissions> fieldExtractor = new BeanWrapperFieldExtractor<>();
        fieldExtractor.setNames(header.split(","));
        fieldExtractor.afterPropertiesSet();
        DelimitedLineAggregator<QuicksightAssetUserPermissions> lineAggregator = new DelimitedLineAggregator<>();
        lineAggregator.setFieldExtractor(fieldExtractor);
        WritableResource resource = new FileSystemResource(filePath);
        return new FlatFileItemWriterBuilder<QuicksightAssetUserPermissions>()
                .name("itemWriter")
                .append(true)
                .shouldDeleteIfEmpty(false)
                .shouldDeleteIfExists(true)
                .lineAggregator(lineAggregator)
                .resource(resource)
                .headerCallback(writer -> writer.write(header))
                .build();
    }
}
