package com.nymbl.tenant;

import org.springframework.core.task.TaskDecorator;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Task decorator that copies the tenant context and security context to the new thread.
 * This ensures background tasks maintain the correct tenant context and security context.
 */
public class TenantContextCopyingDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        // Capture current tenant context
        String tenantId = TenantContext.getCurrentTenant();
        String userTimezoneId = TenantContext.getUserTimezoneId();

        // Capture current security context
        SecurityContext securityContext = SecurityContextHolder.getContext();

        return () -> {
            // Save the original security context of the worker thread
            SecurityContext originalSecurityContext = SecurityContextHolder.getContext();

            try {
                // Set tenant context in the new thread
                TenantContext.setCurrentTenant(tenantId);
                TenantContext.setUserTimezoneId(userTimezoneId);

                // Set security context in the new thread
                SecurityContextHolder.setContext(securityContext);

                // Run the actual task
                runnable.run();
            } finally {
                // Restore the original security context of the worker thread
                SecurityContextHolder.setContext(originalSecurityContext);

                // Clear tenant context
                TenantContext.clear();
            }
        };
    }
}
