package com.nymbl.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Data Transfer Object for sending claim files requests.
 * This DTO is used to receive parameters for sending claim files
 * via the POST endpoint.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Schema(description = "Request object for sending claim files")
public class ClaimFilesRequest {

    @NotEmpty(message = "Claim IDs are required")
    @Schema(description = "List of claim IDs to send", required = true)
    private List<Long> claimIds;

    @Schema(description = "ID of the billing branch")
    private Long billingBranchId;

    @Schema(description = "Type of submission (single or bulk)", defaultValue = "single")
    private String submissionType;
}
