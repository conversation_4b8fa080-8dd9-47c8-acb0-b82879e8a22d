package com.nymbl.tenant.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.nymbl.tenant.model.BulkClaimJob;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * Data Transfer Object for bulk claim job status.
 * This DTO is used to transfer bulk claim job status information
 * to the frontend without including unnecessary fields that might
 * cause serialization issues.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Bulk claim job status information")
public class BulkClaimJobDTO {

    @Schema(description = "Job ID")
    private String jobId;

    @Schema(description = "Job start time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private ZonedDateTime startTime;

    @Schema(description = "Job end time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private ZonedDateTime endTime;

    @Schema(description = "Job status (PENDING, PROCESSING, COMPLETED, FAILED)")
    private String status;

    @Schema(description = "Total number of claims to process")
    private Integer totalClaims;

    @Schema(description = "Number of claims processed so far")
    private Integer processedClaims;

    @Schema(description = "Number of claims successfully processed")
    private Integer successfulClaims;

    @Schema(description = "Number of claims that failed processing")
    private Integer failedClaims;

    @Schema(description = "Current payer being processed")
    private String currentPayer;

    @Schema(description = "Error message if job failed")
    private String errorMessage;

    @Schema(description = "Whether the job is completed")
    private Boolean completed;

    @Schema(description = "Path to the X12 file")
    private String x12FilePath;

    @Schema(description = "X12 file content")
    private String x12FileContent;

    /**
     * Convert a BulkClaimJob entity to a BulkClaimJobDTO.
     *
     * @param entity the BulkClaimJob entity
     * @return the BulkClaimJobDTO
     */
    public static BulkClaimJobDTO fromEntity(BulkClaimJob entity) {
        if (entity == null) {
            return null;
        }

        return BulkClaimJobDTO.builder()
            .jobId(entity.getJobId())
            .status(entity.getStatus())
            .totalClaims(entity.getTotalClaims())
            .processedClaims(entity.getProcessedClaims())
            .successfulClaims(entity.getSuccessfulClaims())
            .failedClaims(entity.getFailedClaims())
            .completed(entity.getCompleted())
            .currentPayer(entity.getCurrentPayer())
            .errorMessage(entity.getErrorMessage())
            .startTime(entity.getStartTime())
            .endTime(entity.getEndTime())
            .x12FilePath(entity.getX12FilePath())
            .x12FileContent(entity.getX12FileContent())
            .build();
    }
}
