package com.nymbl.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Data Transfer Object for validating bulk claim submissions.
 * This DTO is used to receive parameters for validating if claims
 * can be submitted together in a bulk file.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Schema(description = "Request object for validating bulk claim submissions")
public class BulkClaimRequest {

    @NotEmpty(message = "Claim IDs are required")
    @Schema(description = "List of claim IDs to validate for bulk submission", required = true)
    private List<Long> claimIds;

    // Constructors, getters, setters, toString, equals, and hashCode methods are generated by Lombok annotations
}
