package com.nymbl.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.sql.Date;

/**
 * Data Transfer Object for claim creation requests.
 * This DTO is used to receive parameters for creating claims
 * via the POST endpoint.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Schema(description = "Request object for creating claims")
public class ClaimCreationRequest {

    @NotNull(message = "Prescription ID is required")
    @Schema(description = "ID of the prescription to create a claim for", required = true)
    private Long prescriptionId;

    @Schema(description = "ID of the user creating the claim")
    private Long userId;

    @Schema(description = "ID of the billing branch")
    private Long billingBranchId;

    @Schema(description = "Date of service")
    private Date dateOfService;

    @Schema(description = "Whether this is a resend of an existing claim", defaultValue = "false")
    private boolean resend;

    // Constructors are generated by Lombok annotations

    // Getters and setters are generated by Lombok annotations

    // toString method is generated by Lombok annotations
}
