package com.nymbl.tenant.repository;

import com.nymbl.tenant.model.BulkClaimJob;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for BulkClaimJob entity.
 */
@Repository
public interface BulkClaimJobRepository extends JpaRepository<BulkClaimJob, Long> {

    /**
     * Find bulk claim jobs by job ID.
     *
     * @param jobId the job ID
     * @return list of bulk claim jobs
     */
    List<BulkClaimJob> findByJobId(String jobId);

    /**
     * Find the most recent bulk claim job by job ID.
     *
     * @param jobId the job ID
     * @return the most recent bulk claim job
     */
    @Query("SELECT b FROM BulkClaimJob b WHERE b.jobId = :jobId ORDER BY b.id DESC")
    List<BulkClaimJob> findByJobIdOrderByIdDesc(@Param("jobId") String jobId);
}
