package com.nymbl;

import com.nymbl.config.MailService;
import com.nymbl.config.security.NoIndexFilter;
import com.nymbl.config.service.TableObjectContainer;
import com.nymbl.properties.CascadeProperties;
import jakarta.servlet.MultipartConfigElement;
import org.apache.catalina.filters.RemoteIpFilter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.util.unit.DataSize;

import java.util.Properties;

/**
 * Created by Bradley Moore on 05/16/2017.
 */
@SpringBootApplication(
        scanBasePackages = {"com.nymbl"},
        exclude = {MultipartAutoConfiguration.class}
)
@EnableScheduling
//SCRUM-2894: @EnableCaching
@EnableConfigurationProperties(value = {CascadeProperties.class})
public class Application extends SpringBootServletInitializer {

    private static final int MAX_UPLOAD_SIZE = 50 * 1024 * 1024;

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        TableObjectContainer.setApplicationRunning(true);
    }

//    @PostConstruct
//    public void init() {
//        // Setting Spring Boot SetTimeZone
//        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
//    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        setRegisterErrorPageFilter(false);
        TableObjectContainer.setApplicationRunning(true);
        return application.sources(Application.class);
    }

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setMaxFileSize(DataSize.ofMegabytes(50));
        factory.setMaxRequestSize(DataSize.ofMegabytes(50));
        return factory.createMultipartConfig();
    }


    @Bean
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(20);
        return taskScheduler;
    }

    @Bean
    public JavaMailSenderImpl mailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost("smtp.office365.com");
        mailSender.setPort(587);
        mailSender.setUsername("<EMAIL>");
        mailSender.setPassword("451Tech##");
        mailSender.setJavaMailProperties(getJavaMailProperties());
        return mailSender;
    }

    private Properties getJavaMailProperties() {
        Properties props = new Properties();
        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.transport.protocol", "smtp");
        props.setProperty("mail.smtp.starttls.enable", "true");
        props.setProperty("mail.smtp.ssl.trust", "smtp.office365.com");
        props.setProperty("mail.smtp.ssl.protocols", "TLSv1.2");
        props.setProperty("mail.debug", "false");
        return props;
    }

    @Bean
    public MailService mailService() {
        MailService mailService = new MailService();
        mailService.setMailSender(mailSender());
        return mailService;
    }


    //SCRUM-2894:
//    @Bean
//    public CacheManager cacheManager() {
//        return new ConcurrentMapCacheManager("users", "companies");
//    }

    @Bean
    public RemoteIpFilter remoteIpFilter() {
        return new RemoteIpFilter();
    }

    @Bean
    public FilterRegistrationBean<NoIndexFilter> noIndexFilterRegistration(NoIndexFilter noIndexFilter) {
        FilterRegistrationBean<NoIndexFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(noIndexFilter);
        registration.addUrlPatterns("/*");
        registration.setName("noIndexFilter");
        registration.setOrder(1); // High priority to make sure it's applied to all responses
        return registration;
    }

    // Jackson configuration is now handled in WebConfig and CustomJacksonMessageConverter
}
