package com.nymbl.config.controller;

import com.nymbl.config.Constants;
import com.nymbl.config.clearingHouse.WaystarAPI;
import com.nymbl.config.service.DataDictionaryService;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.ClaimFile;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimFileService;
import com.nymbl.tenant.service.ClaimService;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Bradley Moore on 03/10/2020.
 */
@RestController
@RequestMapping("/api/download")
public class DownloadController {

    private static final Logger logger = LoggerFactory.getLogger(DownloadController.class);

    private final ClaimService claimService;
    private final Factory837 factory837;
    private final WaystarAPI clearingHouseAPI;
    private final DataDictionaryService dataDictionaryService;
    private final BulkClaimJobService bulkClaimJobService;
    private final ClaimFileService claimFileService;

    @Autowired
    public DownloadController(ClaimService claimService,
                              Factory837 factory837,
                              WaystarAPI clearingHouseAPI,
                              DataDictionaryService dataDictionaryService,
                              BulkClaimJobService bulkClaimJobService,
                              ClaimFileService claimFileService) {
        this.claimService = claimService;
        this.factory837 = factory837;
        this.clearingHouseAPI = clearingHouseAPI;
        this.dataDictionaryService = dataDictionaryService;
        this.bulkClaimJobService = bulkClaimJobService;
        this.claimFileService = claimFileService;
    }

    @GetMapping(value = "/x12/837/claim/{claimId}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<?> download(@PathVariable Long claimId, HttpServletRequest request) {
        logger.info("Downloading X12 file for claim ID: {}", claimId);

        // Check if this is an actual download request or just a page load
        String userAgent = request.getHeader("User-Agent");
        String acceptHeader = request.getHeader("Accept");

        // Check for specific download request indicators
        boolean isExplicitDownloadRequest =
            // Check if this is a direct download request (has specific Accept header)
            (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_OCTET_STREAM_VALUE)) ||
            // Check for download attribute in request parameters
            request.getParameter("download") != null ||
            // Check for specific referer patterns that indicate user-initiated downloads
            (request.getHeader("Referer") != null &&
             request.getHeader("Referer").contains("download=true"));

        // Only generate X12 file for actual download requests
        if (!isExplicitDownloadRequest && userAgent != null && userAgent.contains("Mozilla")) {
            logger.info("Page load request detected, not generating X12 file");
            return ResponseEntity.ok().build();
        }

        try {
            Claim claim = claimService.findOne(claimId);
            if (claim == null) {
                logger.error("Claim not found with ID: {}", claimId);
                return ResponseEntity.notFound().build();
            }

            // Check if this is a bulk claim
            Boolean isBulkSubmission = false;
            String bulkClaimJobId = null;

            try {
                // Use reflection to get the properties
                java.lang.reflect.Method getBulkSubmissionMethod = claim.getClass().getMethod("getBulkSubmission");
                Object bulkSubmissionObj = getBulkSubmissionMethod.invoke(claim);
                if (bulkSubmissionObj instanceof Boolean) {
                    isBulkSubmission = (Boolean) bulkSubmissionObj;
                }

                java.lang.reflect.Method getBulkClaimJobIdMethod = claim.getClass().getMethod("getBulkClaimJobId");
                Object bulkClaimJobIdObj = getBulkClaimJobIdMethod.invoke(claim);
                if (bulkClaimJobIdObj instanceof String) {
                    bulkClaimJobId = (String) bulkClaimJobIdObj;
                }
            } catch (Exception e) {
                logger.warn("Error checking bulk claim properties: {}", e.getMessage());
            }

            logger.info("Claim {} bulk info - bulkSubmission: {}, bulkClaimJobId: {}",
                claimId, isBulkSubmission, bulkClaimJobId);

            // If this is a bulk claim and we have a job ID, redirect to the bulk download endpoint
            if (Boolean.TRUE.equals(isBulkSubmission) && bulkClaimJobId != null) {
                logger.info("Redirecting to bulk download endpoint for job ID: {}", bulkClaimJobId);
                return ResponseEntity
                    .status(HttpStatus.TEMPORARY_REDIRECT)
                    .header(HttpHeaders.LOCATION, "/api/download/x12/837/bulk/" + bulkClaimJobId)
                    .build();
            }

            // Process as a regular claim
            String timestamp = DateUtil.getStringDate(new Date(), Constants.DF_YYYYMMDDHHmmssSSS);

            // Get claim ID
            Long claimIdValue = null;
            try {
                java.lang.reflect.Method getIdMethod = claim.getClass().getMethod("getId");
                Object idObj = getIdMethod.invoke(claim);
                if (idObj instanceof Long) {
                    claimIdValue = (Long) idObj;
                }
            } catch (Exception e) {
                logger.warn("Error getting claim ID: {}", e.getMessage());
            }

            String controlNumber = (claimIdValue != null ? claimIdValue.toString() : claimId.toString())
                .concat("_").concat(timestamp);

            // Add insurance company ID if available
            try {
                java.lang.reflect.Method getPatientInsuranceMethod = claim.getClass().getMethod("getPatientInsurance");
                Object patientInsuranceObj = getPatientInsuranceMethod.invoke(claim);
                if (patientInsuranceObj != null) {
                    java.lang.reflect.Method getInsuranceCompanyIdMethod = patientInsuranceObj.getClass().getMethod("getInsuranceCompanyId");
                    Object insuranceCompanyIdObj = getInsuranceCompanyIdMethod.invoke(patientInsuranceObj);
                    if (insuranceCompanyIdObj != null) {
                        controlNumber = controlNumber.concat("_").concat(insuranceCompanyIdObj.toString());
                    }
                }
            } catch (Exception e) {
                logger.warn("Error getting insurance company ID: {}", e.getMessage());
            }

            String filename = controlNumber.concat(".CLP");
            logger.info("Generating X12 file for claim ID: {} with filename: {}", claimId, filename);

            Factory837Parameters parameters = factory837.build(claimId, timestamp, null, null, null, null);

            // Get X12 claim string using reflection
            String _837 = "";
            try {
                java.lang.reflect.Method getX12ClaimMethod = parameters.getClass().getMethod("getX12Claim");
                Object x12ClaimObj = getX12ClaimMethod.invoke(parameters);
                if (x12ClaimObj != null) {
                    java.lang.reflect.Method toX12StringMethod = x12ClaimObj.getClass().getMethod("toX12String");
                    Object x12StringObj = toX12StringMethod.invoke(x12ClaimObj);
                    if (x12StringObj instanceof String) {
                        _837 = (String) x12StringObj;
                    }
                }
            } catch (Exception e) {
                logger.error("Error getting X12 claim string: {}", e.getMessage());
                return ResponseEntity.status(500).body("Error generating X12 file: " + e.getMessage());
            }

            // Check for validation errors
            boolean hasValidationErrors = false;
            String validationErrorMessages = "";
            try {
                java.lang.reflect.Method getValidationErrorsMethod = parameters.getClass().getMethod("getValidationErrors");
                Object validationErrorsObj = getValidationErrorsMethod.invoke(parameters);
                if (validationErrorsObj instanceof List) {
                    List<?> validationErrors = (List<?>) validationErrorsObj;
                    hasValidationErrors = !validationErrors.isEmpty();
                    if (hasValidationErrors) {
                        validationErrorMessages = String.join("<br>", validationErrors.stream()
                            .map(Object::toString)
                            .collect(java.util.stream.Collectors.toList()));
                    }
                }
            } catch (Exception e) {
                logger.warn("Error checking validation errors: {}", e.getMessage());
            }

            if (hasValidationErrors) {
                logger.error("Validation errors for claim ID: {}: {}", claimId, validationErrorMessages);
                return ResponseEntity.badRequest().body(validationErrorMessages);
            } else {
                // Check if real-time rules engine should be used
                boolean useRealTimeRulesEngine = false;
                boolean overrideRTRulesEngine = false;

                try {
                    java.lang.reflect.Method getBillingBranchMethod = claim.getClass().getMethod("getBillingBranch");
                    Object billingBranchObj = getBillingBranchMethod.invoke(claim);

                    if (billingBranchObj != null) {
                        java.lang.reflect.Method getUseRealTimeRulesEngineMethod = billingBranchObj.getClass().getMethod("getUseRealTimeRulesEngine");
                        Object useRealTimeRulesEngineObj = getUseRealTimeRulesEngineMethod.invoke(billingBranchObj);
                        useRealTimeRulesEngine = BooleanUtils.toBooleanDefaultIfNull((Boolean) useRealTimeRulesEngineObj, false);

                        java.lang.reflect.Method getOverrideRTRulesEngineMethod = claim.getClass().getMethod("getOverrideRTRulesEngine");
                        Object overrideRTRulesEngineObj = getOverrideRTRulesEngineMethod.invoke(claim);
                        overrideRTRulesEngine = BooleanUtils.toBooleanDefaultIfNull((Boolean) overrideRTRulesEngineObj, false);

                        if (useRealTimeRulesEngine && !overrideRTRulesEngine) {
                            logger.info("Using real-time rules engine for claim ID: {}", claimId);

                            // Get clearing house credentials
                            java.lang.reflect.Method getOutClearingHouseMethod = billingBranchObj.getClass().getMethod("getOutClearingHouse");
                            Object outClearingHouseObj = getOutClearingHouseMethod.invoke(billingBranchObj);

                            if (outClearingHouseObj != null) {
                                java.lang.reflect.Method getRestUserMethod = outClearingHouseObj.getClass().getMethod("getRestUser");
                                Object restUserObj = getRestUserMethod.invoke(outClearingHouseObj);
                                String restUser = restUserObj != null ? restUserObj.toString() : "";

                                java.lang.reflect.Method getRestPasswordMethod = outClearingHouseObj.getClass().getMethod("getRestPassword");
                                Object restPasswordObj = getRestPasswordMethod.invoke(outClearingHouseObj);
                                String restPassword = restPasswordObj != null ? restPasswordObj.toString() : "";

                                Map<String, String> response = clearingHouseAPI.realTimeRulesEngineRun(restUser, restPassword, _837);
                                String error = response.get("Error");

                                if (error == null) {
                                    String cId = response.get("Claim");
                                    String status = response.get("Status");
                                    String messages = response.get("ErrorMessages");
                                    logger.info("Real-time rules engine returned status: {} for claim ID: {}", status, claimId);

                                    return ResponseEntity
                                            .ok()
                                            .contentType(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE))
                                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                                            .header("x-claim", cId)
                                            .header("x-status", status)
                                            .header("x-messages", messages)
                                            .header("x-filename", filename)
                                            .body(_837);
                                } else {
                                    logger.error("Real-time rules engine returned error for claim ID: {}: {}", claimId, error);
                                    return ResponseEntity.badRequest().body(error);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.warn("Error checking real-time rules engine: {}", e.getMessage());
                }

                // Default response without real-time rules engine
                logger.info("Not using real-time rules engine for claim ID: {}", claimId);
                return ResponseEntity
                        .ok()
                        .contentType(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                        .header("x-filename", filename)
                        .body(_837);
            }
        } catch (Exception e) {
            logger.error("Error downloading X12 file for claim ID: {}: {}", claimId, e.getMessage());
            return ResponseEntity.status(500).body("Error downloading X12 file: " + e.getMessage());
        }
    }

    @GetMapping(value = "/x12/837/bulk/{bulkClaimJobId}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<?> downloadBulk(@PathVariable String bulkClaimJobId, HttpServletRequest request) {
        logger.info("Downloading bulk X12 file for job ID: {}", bulkClaimJobId);

        // Check if this is an actual download request or just a page load
        String userAgent = request.getHeader("User-Agent");
        String acceptHeader = request.getHeader("Accept");

        // Check for specific download request indicators
        boolean isExplicitDownloadRequest =
            // Check if this is a direct download request (has specific Accept header)
            (acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_OCTET_STREAM_VALUE)) ||
            // Check for download attribute in request parameters
            request.getParameter("download") != null ||
            // Check for specific referer patterns that indicate user-initiated downloads
            (request.getHeader("Referer") != null &&
             request.getHeader("Referer").contains("download=true"));

        // Only generate X12 file for actual download requests
        if (!isExplicitDownloadRequest && userAgent != null && userAgent.contains("Mozilla")) {
            logger.info("Page load request detected, not generating X12 file");
            return ResponseEntity.ok().build();
        }

        try {
            // Find the bulk claim job
            BulkClaimJob bulkClaimJob = bulkClaimJobService.findByJobId(bulkClaimJobId);

            if (bulkClaimJob == null) {
                logger.error("Bulk claim job not found with ID: {}", bulkClaimJobId);
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Bulk claim job not found. The original X12 file may not be stored in the system.");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Get the X12 file content
            String x12Content = "";
            try {
                // Try reflection to get the X12 content
                java.lang.reflect.Method getX12FileContentMethod = bulkClaimJob.getClass().getMethod("getX12FileContent");
                Object x12ContentObj = getX12FileContentMethod.invoke(bulkClaimJob);
                if (x12ContentObj instanceof String) {
                    x12Content = (String) x12ContentObj;
                }
            } catch (Exception e) {
                logger.error("Error getting X12 content: {}", e.getMessage());
            }

            // If X12 content is empty, try to get it from the claim file
            if (x12Content == null || x12Content.isEmpty()) {
                logger.info("X12 content not found in bulk claim job. Trying to get it from claim file...");

                try {
                    // Extract the claim ID from the job ID
                    String[] parts = bulkClaimJobId.split("_");
                    if (parts.length > 0) {
                        Long claimId = Long.parseLong(parts[0]);

                        // Find claim files for this claim
                        List<ClaimFile> claimFiles = claimFileService.findByClaimId(claimId);
                        if (claimFiles != null && !claimFiles.isEmpty()) {
                            // Get the most recent claim file
                            ClaimFile claimFile = claimFiles.get(0);

                            // Get the X12 content from the claim file
                            // Use reflection to get the contents
                            java.lang.reflect.Method getContentsMethod = claimFile.getClass().getMethod("getContents");
                            Object contentsObj = getContentsMethod.invoke(claimFile);
                            if (contentsObj instanceof String) {
                                x12Content = (String) contentsObj;
                            }
                            logger.info("Found X12 content in claim file for claim ID: {}, length: {}",
                                claimId, (x12Content != null ? x12Content.length() : 0));
                        }
                    }
                } catch (Exception e) {
                    logger.error("Error retrieving X12 content from claim file: {}", e.getMessage());
                }
            }

            if (x12Content == null || x12Content.isEmpty()) {
                logger.error("X12 content is empty for bulk claim job ID: {}", bulkClaimJobId);
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "X12 content is empty for this bulk claim job. The original X12 file may not be stored in the system.");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Generate filename
            String timestamp = DateUtil.getStringDate(new Date(), Constants.DF_YYYYMMDDHHmmssSSS);
            String filename = "bulk_" + bulkClaimJobId + "_" + timestamp + ".txt";

            logger.info("Returning bulk X12 file with filename: {}", filename);

            return ResponseEntity
                    .ok()
                    .contentType(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header("x-filename", filename)
                    .body(x12Content);
        } catch (Exception e) {
            logger.error("Error downloading bulk X12 file: {}", e.getMessage());
            return ResponseEntity.status(500).body("Error downloading bulk X12 file: " + e.getMessage());
        }
    }

    @GetMapping(value = "/data-dictionary", produces = MediaType.TEXT_HTML_VALUE)
    public ResponseEntity<?> dataDictionary(HttpServletRequest request) throws Exception {
        String html = dataDictionaryService.generate();
        return ResponseEntity
                .ok()
                .contentType(MediaType.parseMediaType(MediaType.TEXT_HTML_VALUE))
                .body(html);
    }



    /**
     * Test endpoint to check if a claim is part of a bulk submission.
     *
     * @param claimId the claim ID
     * @return the ResponseEntity with status 200 (OK) and information about the claim
     */
    @GetMapping(value = "/test/bulk-claim/{claimId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> testBulkClaim(@PathVariable Long claimId) {
        System.out.println("Testing bulk claim with ID: " + claimId);

        try {
            // Get the claim from the ClaimService
            Claim claim = claimService.findOne(claimId);
            if (claim == null) {
                System.out.println("Claim not found with ID: " + claimId);
                return ResponseEntity.notFound().build();
            }

            // Check if the claim is part of a bulk submission
            Boolean isBulkSubmission = false;
            String bulkClaimJobId = null;

            try {
                // Use reflection to get the properties if they exist
                java.lang.reflect.Method getBulkSubmissionMethod = claim.getClass().getMethod("getBulkSubmission");
                Object bulkSubmissionObj = getBulkSubmissionMethod.invoke(claim);
                if (bulkSubmissionObj instanceof Boolean) {
                    isBulkSubmission = (Boolean) bulkSubmissionObj;
                }

                java.lang.reflect.Method getBulkClaimJobIdMethod = claim.getClass().getMethod("getBulkClaimJobId");
                Object bulkClaimJobIdObj = getBulkClaimJobIdMethod.invoke(claim);
                if (bulkClaimJobIdObj instanceof String) {
                    bulkClaimJobId = (String) bulkClaimJobIdObj;
                }

                System.out.println("Claim " + claimId + " bulk info - bulkSubmission: " + isBulkSubmission + ", bulkClaimJobId: " + bulkClaimJobId);
            } catch (Exception e) {
                System.out.println("Error getting bulk claim properties: " + e.getMessage());
            }

            // If the claim is part of a bulk submission, get the bulk claim job
            BulkClaimJob job = null;
            if (bulkClaimJobId != null) {
                job = bulkClaimJobService.findByJobId(bulkClaimJobId);
                System.out.println("Bulk claim job found: " + (job != null));

                if (job != null) {
                    try {
                        // Use reflection to get the X12FileContent property
                        java.lang.reflect.Method getX12FileContentMethod = job.getClass().getMethod("getX12FileContent");
                        Object x12ContentObj = getX12FileContentMethod.invoke(job);
                        if (x12ContentObj instanceof String) {
                            String x12Content = (String) x12ContentObj;
                            logger.info("X12 content length: {}", (x12Content != null ? x12Content.length() : 0));
                        }
                    } catch (Exception e) {
                        logger.warn("Error getting X12 file content: {}", e.getMessage());
                    }
                }
            }

            // Return information about the claim
            Map<String, Object> result = new HashMap<>();
            result.put("claimId", claimId);
            result.put("bulkSubmission", isBulkSubmission);
            result.put("bulkClaimJobId", bulkClaimJobId);
            result.put("bulkJobFound", job != null);

            int contentLength = 0;
            if (job != null) {
                try {
                    // Use reflection to get the X12FileContent property if it exists
                    java.lang.reflect.Method getX12FileContentMethod = job.getClass().getMethod("getX12FileContent");
                    Object x12ContentObj = getX12FileContentMethod.invoke(job);
                    if (x12ContentObj instanceof String) {
                        String content = (String) x12ContentObj;
                        if (content != null) {
                            contentLength = content.length();
                        }
                    }
                } catch (Exception e) {
                    System.out.println("Error getting X12 content length: " + e.getMessage());
                }
            }
            result.put("x12ContentLength", contentLength);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            System.out.println("Error testing bulk claim: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body("Error testing bulk claim: " + e.getMessage());
        }
    }
}
