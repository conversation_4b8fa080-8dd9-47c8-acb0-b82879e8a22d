package com.nymbl.config.filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for the JSON response filter.
 * This class registers the JsonResponseFilter as a servlet filter.
 */
@Configuration
public class JsonResponseFilterConfig {

    /**
     * Register the JsonResponseFilter as a servlet filter.
     *
     * @return the FilterRegistrationBean
     */
    @Bean
    public FilterRegistrationBean<JsonResponseFilter> jsonResponseFilter() {
        FilterRegistrationBean<JsonResponseFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new JsonResponseFilter());
        registration.addUrlPatterns("/api/*");
        registration.setName("jsonResponseFilter");
        registration.setOrder(1); // High priority to make sure it's applied to all responses
        return registration;
    }
}
