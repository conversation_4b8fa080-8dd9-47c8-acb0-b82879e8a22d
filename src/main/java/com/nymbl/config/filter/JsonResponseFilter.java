package com.nymbl.config.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Filter to intercept and fix malformed JSON responses.
 * This filter wraps the response to cache its content, then checks if the response
 * is JSON and if it contains any syntax errors.
 */
@Component
@Slf4j
public class JsonResponseFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        
        try {
            filterChain.doFilter(request, responseWrapper);
            
            // Check if response is JSON
            String contentType = responseWrapper.getContentType();
            if (contentType != null && contentType.contains("application/json")) {
                byte[] content = responseWrapper.getContentAsByteArray();
                if (content.length > 0) {
                    String jsonContent = new String(content, StandardCharsets.UTF_8);
                    
                    // Check for common JSON syntax errors
                    if (jsonContent.contains(",}") || jsonContent.contains(",]")) {
                        log.error("Detected malformed JSON in response: {}", jsonContent);
                        
                        // Fix common JSON syntax errors
                        String fixedJson = jsonContent.replace(",}", "}").replace(",]", "]");
                        
                        // Replace the response content
                        responseWrapper.resetBuffer();
                        responseWrapper.getWriter().write(fixedJson);
                        responseWrapper.setContentLength(fixedJson.length());
                    }
                }
            }
        } finally {
            responseWrapper.copyBodyToResponse();
        }
    }
}
