package com.nymbl.config.x12.util;

import java.util.HashMap;
import java.util.Map;

/**
 * Utility class for managing segment counting in X12 files,
 * particularly for bulk claim submissions where multiple claims
 * need to maintain proper hierarchical relationships.
 */
public class SegmentCounter {
    private int count = 0;
    private Map<String, Integer> hlSegmentMap = new HashMap<>();
    private int nextHlId = 1;

    /**
     * Increment the segment count and return the new value.
     *
     * @return the incremented count
     */
    public int increment() {
        return ++count;
    }

    /**
     * Get the current segment count.
     *
     * @return the current count
     */
    public int getCount() {
        return count;
    }

    /**
     * Get the next hierarchical level ID and increment the counter.
     *
     * @return the next hierarchical level ID
     */
    public int getNextHlId() {
        return nextHlId++;
    }

    /**
     * Register a hierarchical level segment with a type and ID.
     *
     * @param type the hierarchical level type
     * @param id the hierarchical level ID
     */
    public void registerHlSegment(String type, int id) {
        hlSegmentMap.put(type, id);
    }

    /**
     * Get the hierarchical level ID for a given type.
     *
     * @param type the hierarchical level type
     * @return the hierarchical level ID, or 0 if not found
     */
    public int getHlSegmentId(String type) {
        return hlSegmentMap.getOrDefault(type, 0);
    }
}
