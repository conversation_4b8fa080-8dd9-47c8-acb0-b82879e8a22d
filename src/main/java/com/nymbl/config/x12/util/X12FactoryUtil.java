package com.nymbl.config.x12.util;

import com.nymbl.config.Constants;
import com.nymbl.config.enums.form1500.RenderingProviderOtherId;
import com.nymbl.config.model.FullName;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.master.model.User;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.DeliveryLocationRepository;
import com.nymbl.tenant.service.*;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.*;

import static com.nymbl.config.enums.form1500.ProviderInformation.FACILITY_INFORMATION;

@Component
@Slf4j
public class X12FactoryUtil {

    private final DeliveryLocationRepository deliveryLocationRepository;
    private final SystemSettingService systemSettingService;
    private final UserService userService;
    private final InsuranceVerificationService insuranceVerificationService;
    private final InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    private final PrescriptionDiagnosisCodeService prescriptionDiagnosisCodeService;
    private final ClaimService claimService;
    private final PrescriptionService prescriptionService;
    private final AppliedPaymentService appliedPaymentService;
    private final AppliedPaymentL_CodeService appliedPaymentLCodeService;
    private final Prescription_L_CodeService prescriptionLCodeService;
    private final ClearingHousePayerService clearingHousePayerService;
    private final BranchService branchService;
    private final PatientInsuranceService patientInsuranceService;
    private final Form1500TemplateService form1500TemplateService;
    private final PhysicianService physicianService;
    private final FeatureFlagService featureFlagService;

    private final PurchaseOrder_ItemService purchaseOrderItemService;

    public X12FactoryUtil(DeliveryLocationRepository deliveryLocationRepository,
                          SystemSettingService systemSettingService,
                          UserService userService,
                          InsuranceVerificationService insuranceVerificationService,
                          InsuranceVerificationLCodeService insuranceVerificationLCodeService,
                          PrescriptionDiagnosisCodeService prescriptionDiagnosisCodeService,
                          ClaimService claimService,
                          PrescriptionService prescriptionService,
                          AppliedPaymentService appliedPaymentService,
                          AppliedPaymentL_CodeService appliedPaymentLCodeService,
                          Prescription_L_CodeService prescriptionLCodeService,
                          ClearingHousePayerService clearingHousePayerService,
                          BranchService branchService,
                          PatientInsuranceService patientInsuranceService,
                          Form1500TemplateService form1500TemplateService,
                          PhysicianService physicianService,
                          FeatureFlagService featureFlagService,
                          PurchaseOrder_ItemService purchaseOrderItemService) {
        this.deliveryLocationRepository = deliveryLocationRepository;
        this.systemSettingService = systemSettingService;
        this.userService = userService;
        this.insuranceVerificationService = insuranceVerificationService;
        this.insuranceVerificationLCodeService = insuranceVerificationLCodeService;
        this.prescriptionDiagnosisCodeService = prescriptionDiagnosisCodeService;
        this.claimService = claimService;
        this.prescriptionService = prescriptionService;
        this.appliedPaymentService = appliedPaymentService;
        this.appliedPaymentLCodeService = appliedPaymentLCodeService;
        this.prescriptionLCodeService = prescriptionLCodeService;
        this.clearingHousePayerService = clearingHousePayerService;
        this.branchService = branchService;
        this.patientInsuranceService = patientInsuranceService;
        this.form1500TemplateService = form1500TemplateService;
        this.physicianService = physicianService;
        this.featureFlagService = featureFlagService;
        this.purchaseOrderItemService = purchaseOrderItemService;
    }

    /**
     * ******************************** DISCLAIMER *******************************
     * ***************************************************************************
     * DO NOT CHANGE THE ORDER OF CALLS IN THIS METHOD, OTHERWISE CHAOS WILL ENSUE
     * ***************************************************************************
     *
     * @param claimId
     * @param timestamp
     * @param billingBranchId
     * @param patientInsuranceId
     * @param otherPatientInsuranceId
     * @param form1500TemplateId
     * @return Factory837Parameters
     */
    public Factory837Parameters loadParameters(Long claimId,
                                               String timestamp,
                                               Long billingBranchId,
                                               Long patientInsuranceId,
                                               Long otherPatientInsuranceId,
                                               Long form1500TemplateId) {
        Factory837Parameters params = new Factory837Parameters();
        String tenant = TenantContext.getCurrentTenant();

        String companyName = userService.getCurrentCompany().getName();

        Claim claim = claimService.findOne(claimId);
        params.setClaim(claim);
        Prescription prescription = prescriptionService.findOne(claim.getPrescriptionId());
        params.setPrescription(prescription);
        List<PurchaseOrder_Item> purchaseOrderItems = purchaseOrderItemService.findByPrescriptionId(prescription.getId());
        params.setPurchaseOrderItems(purchaseOrderItems);

        tenantValidation(params, tenant);

        Branch currentBranch = setCurrentBranch(billingBranchId, params);
        if (currentBranch != null && currentBranch.getOutClearingHouse() != null)
            //TODO Must complete this.  There is a default that I am unsure if we should use. "*********"
            params.setAccountNumber(currentBranch.getOutClearingHouse().getAccountNumber());

        //TODO: Every customer should be using single claim now
        boolean useSingleClaim = "Y".equals(systemSettingService.findBySectionAndField("claim", "use_single_claim").getValue());
        //TODO: Every customer should be using single claim now
        PatientInsurance patientInsurance = useSingleClaim ? claim.getResponsiblePatientInsurance() : claim.getPatientInsurance();
        if (patientInsuranceId != null && !patientInsuranceId.equals(patientInsurance.getId())) {
            patientInsurance = patientInsuranceService.findOne(patientInsuranceId);
        }
        params.setPatientInsurance(patientInsurance);
        params.setCurrentIv(insuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(patientInsurance.getId(), prescription.getId()));

        params.setPrimaryIv(loadInsuranceVerification(prescription.getId(), "primary"));
        if (otherPatientInsuranceId != null) {
            params.setSecondaryIv(insuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(otherPatientInsuranceId, prescription.getId()));
        } else {
            params.setSecondaryIv(loadInsuranceVerification(prescription.getId(), "secondary"));
        }
        params.setTertiaryIv(loadInsuranceVerification(prescription.getId(), "tertiary"));
        params.setQuaternaryIv(loadInsuranceVerification(prescription.getId(), "quaternary"));
        params.setQuinaryIv(loadInsuranceVerification(prescription.getId(), "quinary"));
        params.setSenaryIv(loadInsuranceVerification(prescription.getId(), "senary"));
        params.setSeptenaryIv(loadInsuranceVerification(prescription.getId(), "septenary"));
        params.setOctonaryIv(loadInsuranceVerification(prescription.getId(), "octonary"));
        params.setNonaryIv(loadInsuranceVerification(prescription.getId(), "nonary"));
        params.setDenaryIv(loadInsuranceVerification(prescription.getId(), "denary"));
        params.setPatientControlNumber("P" + prescription.getPatientId() + "C" + claim.getId());
        // First check if patient insurance was specified in the method parameter
        params.setControlNumber(claim.getId().toString().concat("_").concat(timestamp).concat("_").concat(patientInsurance.getInsuranceCompanyId().toString()));
        params.setRelationshipCode(StringUtil.relationToSubscriber(patientInsurance.getRelationToSubscriber()));
        params.setPatientInsuranceCompany(patientInsurance.getInsuranceCompany());
        params.setPatientInsuranceCompanyBranch(patientInsurance.getInsuranceCompanyBranch());
        params.setIvlcs(insuranceVerificationLCodeService.findByInsuranceVerificationIdAndBillTrue(params.getCurrentIv().getId()));
        params.setPlcs(prescriptionLCodeService.findByPrescriptionId(prescription.getId()));

        // NC-491: claim overrides
        if (form1500TemplateId == null && featureFlagService.findFeatureFlagByFeature("claim_level_1500_overrides") != null && claim.getForm1500TemplateId() != null) {
            form1500TemplateId = claim.getForm1500TemplateId();
        }
        params.setForm1500Template(form1500TemplateService.findOne(form1500TemplateId == null ? patientInsurance.getInsuranceCompany().getForm1500TemplateId() : form1500TemplateId));

        List<PrescriptionDiagnosisCode> t = prescriptionDiagnosisCodeService.findByPrescriptionId(prescription.getId());
        for (PrescriptionDiagnosisCode o : t) {
            params.addPrescriptionDiagnosisCodes(o.getDiagnosisCode());
            params.addPrescriptionDiagnosisCodeNames(o.getDiagnosisCode().getName());
        }
        for (Prescription_L_Code plc : params.getPlcs()) {
            if (plc.getDiagnosisCode1() != null) {
                params.addPrescriptionDiagnosisCodes(plc.getDiagnosisCode1());
                params.addPrescriptionDiagnosisCodeNames(plc.getDiagnosisCode1().getName());
            }
            if (plc.getDiagnosisCode2() != null) {
                params.addPrescriptionDiagnosisCodes(plc.getDiagnosisCode2());
                params.addPrescriptionDiagnosisCodeNames(plc.getDiagnosisCode2().getName());
            }
            if (plc.getDiagnosisCode3() != null) {
                params.addPrescriptionDiagnosisCodes(plc.getDiagnosisCode3());
                params.addPrescriptionDiagnosisCodeNames(plc.getDiagnosisCode3().getName());
            }
            if (plc.getDiagnosisCode4() != null && StringUtils.isNotBlank(plc.getDiagnosisCode4().getName())) {
                params.addPrescriptionDiagnosisCodes(plc.getDiagnosisCode4());
                params.addPrescriptionDiagnosisCodeNames(plc.getDiagnosisCode4().getName());
            }
        }

        Date d = new Date();
        params.setDate(DateUtil.getStringDate(d, Constants.DF_YYMMDD));
        params.setDate8(DateUtil.getStringDate(d, Constants.DF_YYYYMMDD));
        params.setTime(DateUtil.getStringDate(d, "HHmm"));
        params.setFacilityName(getFacilityName(params, companyName));
        params.setPrimaryAppliedPayments(loadCarrierAppliedPayments(params.getPrimaryIv()));
        params.setSecondaryAppliedPayments(loadCarrierAppliedPayments(params.getSecondaryIv()));
        params.setTertiaryAppliedPayments(loadCarrierAppliedPayments(params.getTertiaryIv()));
        params.setQuaternaryAppliedPayments(loadCarrierAppliedPayments(params.getQuaternaryIv()));
        params.setQuinaryAppliedPayments(loadCarrierAppliedPayments(params.getQuinaryIv()));
        params.setSenaryAppliedPayments(loadCarrierAppliedPayments(params.getSenaryIv()));
        params.setSeptenaryAppliedPayments(loadCarrierAppliedPayments(params.getSeptenaryIv()));
        params.setOctonaryAppliedPayments(loadCarrierAppliedPayments(params.getOctonaryIv()));
        params.setNonaryAppliedPayments(loadCarrierAppliedPayments(params.getNonaryIv()));
        params.setDenaryAppliedPayments(loadCarrierAppliedPayments(params.getDenaryIv()));
        return params;
    }

    public Branch setCurrentBranch(Long billingBranchId, Factory837Parameters params) {
        // First check if billing branch was specified in the method parameter
        if (billingBranchId != null && billingBranchId > 0) {
            params.setCurrentBranch(branchService.findOne(billingBranchId));
        }
        if (params.getCurrentBranch() == null) {
            if (params.getClaim().getBillingBranchId() != null) {
                params.setCurrentBranch(params.getClaim().getBillingBranch());
            } else if (params.getPatient().getPrimaryBranch() != null) {
                params.setCurrentBranch(params.getPatient().getPrimaryBranch());
            } else {
                params.setCurrentBranch(userService.getCurrentBranchUnsafeDoNotUse());
            }
        }
        return params.getCurrentBranch();
    }

    public void tenantValidation(Factory837Parameters params, String tenant) {
        User user = userService.getCurrentUser();
        Set<String> allTenants = new HashSet<>();
        Claim claim = params.getClaim();
        Prescription prescription = params.getPrescription();

        User claimCreatedBy = claim.getCreatedBy();
        if (claimCreatedBy != null) {
            allTenants.add(claimCreatedBy.getCompany().getKey());
            claimCreatedBy.getCompanies().stream().forEach((c) -> allTenants.add(c.getKey()));
        }
        User prescriptionCreatedBy = prescription.getCreatedBy();
        if (prescriptionCreatedBy != null) {
            allTenants.add(prescriptionCreatedBy.getCompany().getKey());
            prescriptionCreatedBy.getCompanies().forEach((c) -> allTenants.add(c.getKey()));
        }

        // If user is null, we're likely in a background process without security context
        if (user == null) {
            log.warn("tenantValidation called with null user. Using system context for tenant validation.");
            // In background processing, we trust the tenant context that was propagated
            return;
        }

        // Tenant check to ensure that submission is valid for the current user
        if (!allTenants.contains(tenant) && !user.getIsSuperAdmin()) {
            String message = MessageFormat.format("Current tenant {0} not matching for available tenants {1} for user id {3} username {4}",
                    tenant, String.join(",", allTenants), user.getId(), user.getUsername());
            Sentry.captureMessage(message);
            params.addValidationError("Error building submission data.");
        } else if (user.getIsSuperAdmin()) {
            String message = MessageFormat.format("SuperAdmin #{0} {1} {2} is downloading x12 file for claim #{3}.",
                    user.getId(), user.getFirstName(), user.getLastName(), claim.getId());
            log.info(message);
        }
    }

    public InsuranceVerification loadInsuranceVerification(Long prescriptionId, String carrierType) {
        InsuranceVerification result = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescriptionId, carrierType);
        return result;
    }

    public String getFacilityName(Factory837Parameters params, String companyName) {
        String facilityName;
        Prescription prescription = params.getPrescription();
        Branch currentBranch = params.getCurrentBranch();
        if (prescription.getUseAddress() != null && prescription.getUseAddress() && "other".equals(prescription.getDeliveryLocation())) {
            String[] array = prescription.getDeliveryLocationAddress().split(",");
            facilityName = array.length > 0 ? array[0] : "";
        } else if (prescription.getUseAddress() != null && prescription.getUseAddress() && StringUtils.isNumeric(prescription.getDeliveryLocation())) {
            DeliveryLocation deliveryLocation = deliveryLocationRepository.getReferenceById(Long.valueOf(prescription.getDeliveryLocation()));
            facilityName = StringUtils.isNotBlank(deliveryLocation.getName()) ? deliveryLocation.getName() : " ";
        } else {
            User tp = userService.getUserById(prescription.getTreatingPractitionerId());
            FullName name = (tp != null) ? new FullName(tp.getFirstName(), tp.getMiddleName(), tp.getLastName(), tp.getCredentials()) : new FullName();
            facilityName = Boolean.TRUE.equals(currentBranch != null && currentBranch.getUseBranchName()) ? currentBranch.getName() : companyName;
            facilityName = FACILITY_INFORMATION.equals(params.getForm1500Template().getBox31ProviderInformation()) ? facilityName : StringUtil.formatName(name, "FMiLC", true);
        }
        return facilityName;
    }

    public List<AppliedPayment> loadCarrierAppliedPayments(InsuranceVerification insuranceVerification) {
        List<AppliedPayment> results = new ArrayList<>();
        if (insuranceVerification != null) {
            Claim c = claimService.findByPrescriptionIdAndPatientInsuranceId(insuranceVerification.getPrescriptionId(), insuranceVerification.getPatientInsuranceId());
            results = c != null ? appliedPaymentService.getByClaimId(c.getId()) : new ArrayList<>();
        }
        return results;
    }

    public String getTaxIdFrom1500FormTemplate(Form1500Template template, Branch branch) {
        Boolean useBranchTaxId = template.getUseBranchTaxId();
        String taxId = "";
        if (Boolean.TRUE.equals(useBranchTaxId)) {
            taxId = branch.getTaxId();
            if (StringUtils.isNotBlank(taxId)) {
                taxId = taxId.replaceAll("-", "");
            } else {
                taxId = "**********";
            }
        } else {
            if (StringUtils.isNotBlank(template.getTaxId())) {
                taxId = template.getTaxId();
            }
            else {
                taxId = "**********";
            }
        }
        return taxId;
    }

    public String getRenderingProviderOtherId(RenderingProviderOtherId mapping, Factory837Parameters params, User tp, InsuranceVerification_L_Code insuranceVerificationLCode, Boolean row1) {
        String providerOtherId = null;
        Physician referringPhysician = physicianService.findOne(params.getPrescription().getReferringPhysicianId());
        Physician primaryCarePhysician = physicianService.findOne(params.getPrescription().getPrimaryCarePhysicianId());
        if (mapping != null) {
            switch (mapping) {
                case BRANCH_NPI:
                    providerOtherId = params.getCurrentBranch().getNpi();
                    break;
                case PCP_NPI:
                    if (primaryCarePhysician != null && StringUtils.isNotBlank(primaryCarePhysician.getNpi())) {
                        providerOtherId = primaryCarePhysician.getNpi();
                    } else {
                        params.addValidationError("Primary care physician was not set on the prescription or the Primary care physician's NPI is missing.");
                    }
                    break;
                case REF_NPI:
                    if (referringPhysician != null &&
                            StringUtils.isNotBlank(referringPhysician.getNpi())) {
                        providerOtherId = referringPhysician.getNpi();
                    } else {
                        params.addValidationError("Referring care physician was not set on the prescription or the Referring care physician's NPI is missing.");
                    }
                    break;
                case BILLING_NPI:
                    providerOtherId = params.getForm1500Template().getBillingNpi();
                    break;
                case FACILITY_NPI:
                    providerOtherId = params.getForm1500Template().getBox32AFacilityNpi();
                    break;
                case OTHER:
                    if (row1) {
                        providerOtherId = params.getForm1500Template().getBox24JOtherId();
                    } else {
                        providerOtherId = params.getForm1500Template().getBox24JOtherNpi();
                    }
                    break;
                case BRANCH_OTHER_ID_1:
                    providerOtherId = params.getCurrentBranch().getOtherId1();
                    break;
                case BRANCH_OTHER_ID_2:
                    providerOtherId = params.getCurrentBranch().getOtherId2();
                    break;
                case REF_OTHER_ID_1:
                    providerOtherId = referringPhysician.getOtherId1();
                    break;
                case REF_OTHER_ID_2:
                    providerOtherId = referringPhysician.getOtherId2();
                    break;
                case PCP_UPIN:
                    providerOtherId = primaryCarePhysician.getUpin();
                    break;
                case PCP_MEDICAID:
                    providerOtherId = primaryCarePhysician.getMedicaidNumber();
                    break;
                case PCP_LICENSE:
                    providerOtherId = primaryCarePhysician.getLicenseNumber();
                    break;
                case REF_UPIN:
                    providerOtherId = referringPhysician.getUpin();
                    break;
                case REF_MEDICAID:
                    providerOtherId = referringPhysician.getMedicaidNumber();
                    break;
                case REF_LICENSE:
                    providerOtherId = referringPhysician.getLicenseNumber();
                    break;
                case BILLING_TAXONOMY:
                    providerOtherId = params.getForm1500Template().getBox33BBillingTaxonomy();
                    break;
                case FACILITY_TAXONOMY:
                    providerOtherId = params.getForm1500Template().getBox32BFacilityTaxonomy();
                    break;
                case AUTH_NUMBER:
                    providerOtherId = insuranceVerificationLCode.getInsuranceVerification().getReferralNumber();
                    break;
                case PRACTITIONER_NPI:
                    if (tp != null && StringUtils.isNotBlank(tp.getNpi())) {
                        providerOtherId = tp.getNpi();
                    } else {
                        providerOtherId = params.getClaim().getBillingBranch().getNpi();
                    }
                    break;
            }
        }

        return providerOtherId;
    }

}
