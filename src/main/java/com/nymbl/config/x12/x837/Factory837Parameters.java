package com.nymbl.config.x12.x837;

import com.nymbl.config.x12.x835.ClaimPayment;
import com.nymbl.tenant.model.*;
import lombok.Data;

import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON> on 9/30/23.
 */
@Data
public class Factory837Parameters {

    private LinkedList<String> validationErrors = new LinkedList<>();
    private Form1500Template form1500Template;
    private int segmentCount = 0;
    private String facilityName;
    private String controlNumber;
    private String patientControlNumber;
    private String relationshipCode;
    private String accountNumber;
    private String date;
    private String date8;
    private String time;
    private boolean bulkSubmission = false;

    private Claim claim;
    private Prescription prescription;
    private Branch currentBranch;
    private PatientInsurance patientInsurance;
    private InsuranceCompany patientInsuranceCompany;
    private InsuranceCompanyBranch patientInsuranceCompanyBranch;
    private Set<DiagnosisCode> prescriptionDiagnosisCodes = new LinkedHashSet<>();
    private Set<String> prescriptionDiagnosisCodeNames = new LinkedHashSet<>();
    ;
    private List<InsuranceVerification_L_Code> ivlcs;
    private List<Prescription_L_Code> plcs;
    private InsuranceVerification currentIv;
    private InsuranceVerification otherIv;
    private InsuranceVerification primaryIv;
    private InsuranceVerification secondaryIv;
    private InsuranceVerification tertiaryIv;
    private InsuranceVerification quaternaryIv;
    private InsuranceVerification quinaryIv;
    private InsuranceVerification senaryIv;
    private InsuranceVerification septenaryIv;
    private InsuranceVerification octonaryIv;
    private InsuranceVerification nonaryIv;
    private InsuranceVerification denaryIv;
    private List<AppliedPayment> primaryAppliedPayments;
    private List<AppliedPayment> secondaryAppliedPayments;
    private List<AppliedPayment> tertiaryAppliedPayments;
    private List<AppliedPayment> quaternaryAppliedPayments;
    private List<AppliedPayment> quinaryAppliedPayments;
    private List<AppliedPayment> senaryAppliedPayments;
    private List<AppliedPayment> septenaryAppliedPayments;
    private List<AppliedPayment> octonaryAppliedPayments;
    private List<AppliedPayment> nonaryAppliedPayments;
    private List<AppliedPayment> denaryAppliedPayments;
    private List<PurchaseOrder_Item> purchaseOrderItems;

    private X12Claim x12Claim = new X12Claim();
    private ClaimPayment claimPayment = new ClaimPayment();

    public Patient getPatient() {
        return prescription.getPatient();
    }

    public void addValidationError(String error) {
        validationErrors.add(error);
    }

    public void addPrescriptionDiagnosisCodes(DiagnosisCode diagnosisCode) {
        prescriptionDiagnosisCodes.add(diagnosisCode);
    }

    public void addPrescriptionDiagnosisCodeNames(String name) {
        prescriptionDiagnosisCodeNames.add(name);
    }

    public void incrementSegmentCount() {
        segmentCount++;
    }
}
