package com.nymbl.config.exception;

import java.io.Serial;
import java.util.LinkedList;
import java.util.List;

public class X12Exception extends Exception {

    @Serial
    private static final long serialVersionUID = 1L;

    public X12Exception(LinkedList<String> validationErrors) {
        super(String.join("<br>", validationErrors));
    }

    public X12Exception(List<String> validationErrors) {
        super(String.join("<br>", validationErrors));
    }
}
