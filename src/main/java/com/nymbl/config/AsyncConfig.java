package com.nymbl.config;

import com.nymbl.tenant.TenantContextCopyingDecorator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * Configuration for asynchronous task execution with tenant context propagation.
 * This ensures that background tasks maintain the correct tenant context.
 */
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

    /**
     * Configure the async executor with tenant context propagation.
     * 
     * @return the configured executor
     */
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("nymbl-async-");
        executor.setTaskDecorator(tenantContextCopyingDecorator());
        executor.initialize();
        return executor;
    }

    /**
     * Create a task decorator that copies the tenant context to new threads.
     * 
     * @return the tenant context copying decorator
     */
    @Bean
    public TaskDecorator tenantContextCopyingDecorator() {
        return new TenantContextCopyingDecorator();
    }
}
