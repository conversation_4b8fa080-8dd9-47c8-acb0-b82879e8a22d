package com.nymbl.config.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * Utility class for checking user privileges.
 */
@Component
public class PrivilegeChecker {

    private static final Logger logger = LoggerFactory.getLogger(PrivilegeChecker.class);

    /**
     * Check if the current user has the specified privilege.
     *
     * @param privilege the privilege to check
     * @return true if the user has the privilege, false otherwise
     */
    public boolean hasPrivilege(String privilege) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null) {
            logger.warn("No authentication found in security context");
            return false;
        }
        
        // Check for superadmin privilege which grants all access
        boolean hasSuperAdmin = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> "superadmin".equals(authority));
        
        if (hasSuperAdmin) {
            return true;
        }
        
        // Check for the specific privilege
        boolean hasSpecificPrivilege = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> privilege.equals(authority));
        
        return hasSpecificPrivilege;
    }
}
