package com.nymbl.config.x12.util;

import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.master.model.Company;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.Prescription;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * Tests for the X12FactoryUtil class.
 * These tests verify that the tenantValidation method handles null users gracefully.
 */
@ExtendWith(MockitoExtension.class)
class X12FactoryUtilTest {

    @Mock
    private UserService userService;

    @Mock
    private Factory837Parameters params;

    @Mock
    private Claim claim;

    @Mock
    private Prescription prescription;

    @Mock
    private User claimCreatedBy;

    @Mock
    private User prescriptionCreatedBy;

    @Mock
    private Company company;

    @InjectMocks
    private X12FactoryUtil x12FactoryUtil;

    private final String tenant = "test-tenant";

    @BeforeEach
    void setUp() {
        when(params.getClaim()).thenReturn(claim);
        when(params.getPrescription()).thenReturn(prescription);
        when(claim.getCreatedBy()).thenReturn(claimCreatedBy);
        when(prescription.getCreatedBy()).thenReturn(prescriptionCreatedBy);
    }

    @Test
    void testTenantValidationWithNullUser() {
        // Arrange
        when(userService.getCurrentUser()).thenReturn(null);

        List<Company> companies = new ArrayList<>();
        companies.add(company);

        when(claimCreatedBy.getCompany()).thenReturn(company);
        when(claimCreatedBy.getCompanies()).thenReturn(companies);
        when(company.getKey()).thenReturn(tenant);

        when(prescriptionCreatedBy.getCompany()).thenReturn(company);
        when(prescriptionCreatedBy.getCompanies()).thenReturn(companies);

        // Act
        x12FactoryUtil.tenantValidation(params, tenant);

        // Assert
        // Verify that no validation error was added
        verify(params, never()).addValidationError(anyString());
    }

    @Test
    void testTenantValidationWithSuperAdmin() {
        // Arrange
        User user = mock(User.class);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getIsSuperAdmin()).thenReturn(true);
        when(user.getId()).thenReturn(1L);
        when(user.getFirstName()).thenReturn("Admin");
        when(user.getLastName()).thenReturn("User");
        when(claim.getId()).thenReturn(123L);

        List<Company> companies = new ArrayList<>();
        companies.add(company);

        when(claimCreatedBy.getCompany()).thenReturn(company);
        when(claimCreatedBy.getCompanies()).thenReturn(companies);
        when(company.getKey()).thenReturn(tenant);

        when(prescriptionCreatedBy.getCompany()).thenReturn(company);
        when(prescriptionCreatedBy.getCompanies()).thenReturn(companies);

        // Act
        x12FactoryUtil.tenantValidation(params, tenant);

        // Assert
        // Verify that no validation error was added
        verify(params, never()).addValidationError(anyString());
    }

    @Test
    void testTenantValidationWithNonMatchingTenant() {
        // Arrange
        User user = mock(User.class);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getIsSuperAdmin()).thenReturn(false);
        when(user.getId()).thenReturn(1L);
        when(user.getUsername()).thenReturn("user");

        List<Company> companies = new ArrayList<>();
        companies.add(company);

        when(claimCreatedBy.getCompany()).thenReturn(company);
        when(claimCreatedBy.getCompanies()).thenReturn(companies);
        when(company.getKey()).thenReturn("different-tenant");

        when(prescriptionCreatedBy.getCompany()).thenReturn(company);
        when(prescriptionCreatedBy.getCompanies()).thenReturn(companies);

        // Act
        x12FactoryUtil.tenantValidation(params, tenant);

        // Assert
        // Verify that a validation error was added
        verify(params).addValidationError("Error building submission data.");
    }
}
