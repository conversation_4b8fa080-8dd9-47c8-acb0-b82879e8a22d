package com.nymbl.config.x12.x837;

import com.nymbl.config.enums.form1500.PlaceOfService;
import com.nymbl.config.x12.util.X12FactoryUtil;
import com.nymbl.tenant.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Tests for the Factory837 class focusing on special cases like special characters,
 * field validation, and segment limitations.
 */
class Factory837SpecialCasesTest {

    @Mock
    private X12FactoryUtil x12FactoryUtil;

    @InjectMocks
    private Factory837 factory837;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    /**
     * Test X12 generation with special characters in patient/provider names.
     */
    @Test
    void testSpecialCharactersInNames() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L);
        String timestamp = "20240101120000";
        Long billingBranchId = 1L;

        // Create parameters with special characters in names
        Factory837Parameters params = createParametersWithSpecialChars();

        // Mock the loadParameters method
        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("O'Connor-Smith", result.get(0).getPrescription().getPatient().getLastName());
        assertTrue(result.get(0).isBulkSubmission());

        // Create a spy of the Factory837 class to test the createBulkX12Claim method
        Factory837 spyFactory837 = spy(factory837);
        X12Claim mockX12Claim = new X12Claim();
        doReturn(mockX12Claim).when(spyFactory837).createBulkX12Claim(any());

        // Execute createBulkX12Claim
        X12Claim x12Claim = spyFactory837.createBulkX12Claim(result);

        // Verify the X12Claim was created
        assertNotNull(x12Claim);
    }

    /**
     * Test X12 generation with missing but non-required fields.
     */
    @Test
    void testMissingNonRequiredFields() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L);
        String timestamp = "20240101120000";
        Long billingBranchId = 1L;

        // Create parameters with missing non-required fields
        Factory837Parameters params = createParametersWithMissingNonRequiredFields();

        // Mock the loadParameters method
        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getPrescription().getPatient().getMiddleName());
        assertTrue(result.get(0).isBulkSubmission());
        assertTrue(result.get(0).getValidationErrors().isEmpty());
    }

    /**
     * Test validation of minimum required fields across multiple claims.
     */
    @Test
    void testValidationOfRequiredFields() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L);
        String timestamp = "20240101120000";
        Long billingBranchId = 1L;

        // Create parameters with missing required fields
        Factory837Parameters params = createParametersWithMissingRequiredFields();

        // Mock the loadParameters method
        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());
        assertFalse(result.get(0).getValidationErrors().isEmpty());
        assertEquals("Missing required field: NPI", result.get(0).getValidationErrors().get(0));
    }

    /**
     * Helper method to create parameters with special characters in names.
     */
    private Factory837Parameters createParametersWithSpecialChars() {
        Factory837Parameters params = new Factory837Parameters();

        // Create Branch
        Branch branch = new Branch();
        branch.setId(1L);
        branch.setName("O'Reilly & Sons");
        branch.setBillingPhoneNumber("************");
        branch.setNpi("**********");

        // Create Patient with special characters
        Patient patient = new Patient();
        patient.setId(1L);
        patient.setFirstName("María-José");
        patient.setLastName("O'Connor-Smith");
        patient.setMiddleName("D'Angelo");

        // Create Prescription
        Prescription prescription = new Prescription();
        prescription.setId(1L);
        prescription.setPatient(patient);

        // Create Claim
        Claim claim = new Claim();
        claim.setId(1L);
        claim.setBillingBranch(branch);
        claim.setBillingBranchId(branch.getId());
        claim.setPrescriptionId(prescription.getId());
        claim.setTotalClaimAmount(new BigDecimal("100.00"));

        // Create Form1500Template
        Form1500Template template = new Form1500Template();
        template.setId(1L);
        template.setUseCapitatedPayerSpecialEdits(false);
        template.setBox24BPlaceOfService(PlaceOfService.Office);
        template.setBox33BBillingTaxonomy("123456789X");

        // Create InsuranceCompany with special characters
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(1L);
        insuranceCompany.setName("Blue Cross & Blue Shield");

        // Create InsuranceCompanyBranch
        InsuranceCompanyBranch insuranceCompanyBranch = new InsuranceCompanyBranch();
        insuranceCompanyBranch.setId(1L);
        insuranceCompanyBranch.setName("Main Branch");

        // Create PatientInsurance
        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(1L);
        patientInsurance.setGroupNumber("Group1");
        patientInsurance.setInsuranceNumber("INS1");
        patientInsurance.setRelationToSubscriber("SELF");
        patientInsurance.setInsuranceCompany(insuranceCompany);
        patientInsurance.setInsuranceCompanyBranch(insuranceCompanyBranch);

        // Create InsuranceVerification
        InsuranceVerification iv = new InsuranceVerification();
        iv.setId(1L);
        iv.setCarrierType("PRIMARY");
        iv.setPatientInsurance(patientInsurance);

        // Set all fields in the parameters object
        params.setClaim(claim);
        params.setPrescription(prescription);
        params.setCurrentBranch(branch);
        params.setForm1500Template(template);
        params.setCurrentIv(iv);
        params.setPrimaryIv(iv);
        params.setPatientInsurance(patientInsurance);
        params.setPatientInsuranceCompany(insuranceCompany);
        params.setPatientInsuranceCompanyBranch(insuranceCompanyBranch);
        params.setAccountNumber("ACC1");
        params.setDate("230101");
        params.setTime("1200");
        params.setDate8("********");
        params.setBulkSubmission(true);
        params.setPatientControlNumber("PCN1");

        return params;
    }

    /**
     * Helper method to create parameters with missing non-required fields.
     */
    private Factory837Parameters createParametersWithMissingNonRequiredFields() {
        Factory837Parameters params = createParametersWithSpecialChars();

        // Set some non-required fields to null
        Patient patient = params.getPrescription().getPatient();
        patient.setMiddleName(null);
        // Other non-required fields that might be null in a real scenario

        return params;
    }

    /**
     * Helper method to create parameters with missing required fields.
     */
    private Factory837Parameters createParametersWithMissingRequiredFields() {
        Factory837Parameters params = createParametersWithSpecialChars();

        // Set a required field to null
        Branch branch = params.getCurrentBranch();
        branch.setNpi(null);

        // Add validation error
        params.addValidationError("Missing required field: NPI");

        return params;
    }
}
