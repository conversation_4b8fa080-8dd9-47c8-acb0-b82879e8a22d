package com.nymbl.config.x12.x837;

import com.nymbl.config.enums.form1500.PlaceOfService;
import com.nymbl.config.x12.util.SegmentCounter;
import com.nymbl.config.x12.util.X12FactoryUtil;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.*;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Tests for the Factory837 class focusing on segment limitations and timezone handling.
 */
class Factory837SegmentLimitTest {

    @Mock
    private X12FactoryUtil x12FactoryUtil;

    @InjectMocks
    private Factory837 factory837;

    private String originalTimezone;

    @BeforeEach
    void setUp() {
        openMocks(this);
        // Save the original timezone
        originalTimezone = TenantContext.getUserTimezoneId();
    }

    @AfterEach
    void tearDown() {
        // Restore the original timezone
        TenantContext.setUserTimezoneId(originalTimezone);
    }

    /**
     * Test proper handling of segment count limitations.
     */
    @Test
    void testSegmentCountLimitations() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        String timestamp = "20240101120000";
        Long billingBranchId = 1L;

        // Create parameters with a high segment count
        Factory837Parameters params1 = createParametersWithSegmentCount(1L, 500);
        Factory837Parameters params2 = createParametersWithSegmentCount(2L, 600);

        // Mock the loadParameters method
        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params1);
        when(x12FactoryUtil.loadParameters(eq(2L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params2);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(500, result.get(0).getSegmentCount());
        assertEquals(600, result.get(1).getSegmentCount());

        // Create a spy of the Factory837 class to test the createBulkX12Claim method
        Factory837 spyFactory837 = spy(factory837);
        X12Claim mockX12Claim = new X12Claim();
        doReturn(mockX12Claim).when(spyFactory837).createBulkX12Claim(any());

        // Execute createBulkX12Claim
        X12Claim x12Claim = spyFactory837.createBulkX12Claim(result);

        // Verify the X12Claim was created
        assertNotNull(x12Claim);
    }

    /**
     * Test the SegmentCounter utility class.
     */
    @Test
    void testSegmentCounter() {
        // Setup
        SegmentCounter counter = new SegmentCounter();

        // Execute
        counter.increment();
        counter.increment();
        counter.increment();

        // Verify
        assertEquals(3, counter.getCount());

        // Create a new counter to verify initial count
        SegmentCounter newCounter = new SegmentCounter();
        assertEquals(0, newCounter.getCount());
    }

    /**
     * Test X12 generation with different timezone settings.
     */
    @Test
    void testTimezoneHandling() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L);
        String timestamp = "20240101120000";
        Long billingBranchId = 1L;

        // Set a specific timezone
        TenantContext.setUserTimezoneId("America/New_York");

        // Create parameters
        Factory837Parameters params = createBasicParameters(1L);

        // Mock the loadParameters method
        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());

        // Change timezone
        TenantContext.setUserTimezoneId("America/Los_Angeles");

        // Execute again with different timezone
        List<Factory837Parameters> result2 = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result2);
        assertEquals(1, result2.size());

        // Create a spy of the Factory837 class to test the createBulkX12Claim method
        Factory837 spyFactory837 = spy(factory837);
        X12Claim mockX12Claim = new X12Claim();
        doReturn(mockX12Claim).when(spyFactory837).createBulkX12Claim(any());

        // Execute createBulkX12Claim with both parameter sets
        X12Claim x12Claim1 = spyFactory837.createBulkX12Claim(result);
        X12Claim x12Claim2 = spyFactory837.createBulkX12Claim(result2);

        // Verify the X12Claims were created
        assertNotNull(x12Claim1);
        assertNotNull(x12Claim2);
    }

    /**
     * Helper method to create parameters with a specific segment count.
     */
    private Factory837Parameters createParametersWithSegmentCount(Long id, int segmentCount) {
        Factory837Parameters params = createBasicParameters(id);
        params.setSegmentCount(segmentCount);
        return params;
    }

    /**
     * Helper method to create basic parameters.
     */
    private Factory837Parameters createBasicParameters(Long id) {
        Factory837Parameters params = new Factory837Parameters();

        // Create Branch
        Branch branch = new Branch();
        branch.setId(id);
        branch.setName("Test Branch " + id);
        branch.setBillingPhoneNumber("************");
        branch.setNpi("**********");

        // Create Patient
        Patient patient = new Patient();
        patient.setId(id);
        patient.setFirstName("Test");
        patient.setLastName("Patient" + id);

        // Create Prescription
        Prescription prescription = new Prescription();
        prescription.setId(id);
        prescription.setPatient(patient);

        // Create Claim
        Claim claim = new Claim();
        claim.setId(id);
        claim.setBillingBranch(branch);
        claim.setBillingBranchId(branch.getId());
        claim.setPrescriptionId(prescription.getId());
        claim.setTotalClaimAmount(new BigDecimal("100.00"));

        // Create Form1500Template
        Form1500Template template = new Form1500Template();
        template.setId(id);
        template.setUseCapitatedPayerSpecialEdits(false);
        template.setBox24BPlaceOfService(PlaceOfService.Office);
        template.setBox33BBillingTaxonomy("123456789X");

        // Create InsuranceCompany
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(id);
        insuranceCompany.setName("Test Insurance Company " + id);

        // Create InsuranceCompanyBranch
        InsuranceCompanyBranch insuranceCompanyBranch = new InsuranceCompanyBranch();
        insuranceCompanyBranch.setId(id);
        insuranceCompanyBranch.setName("Test Insurance Company Branch " + id);

        // Create PatientInsurance
        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(id);
        patientInsurance.setGroupNumber("Group" + id);
        patientInsurance.setInsuranceNumber("INS" + id);
        patientInsurance.setRelationToSubscriber("SELF");
        patientInsurance.setInsuranceCompany(insuranceCompany);
        patientInsurance.setInsuranceCompanyBranch(insuranceCompanyBranch);

        // Create InsuranceVerification
        InsuranceVerification iv = new InsuranceVerification();
        iv.setId(id);
        iv.setCarrierType("PRIMARY");
        iv.setPatientInsurance(patientInsurance);

        // Set all fields in the parameters object
        params.setClaim(claim);
        params.setPrescription(prescription);
        params.setCurrentBranch(branch);
        params.setForm1500Template(template);
        params.setCurrentIv(iv);
        params.setPrimaryIv(iv);
        params.setPatientInsurance(patientInsurance);
        params.setPatientInsuranceCompany(insuranceCompany);
        params.setPatientInsuranceCompanyBranch(insuranceCompanyBranch);
        params.setAccountNumber("ACC" + id);
        params.setDate("230101");
        params.setTime("1200");
        params.setDate8("********");
        params.setBulkSubmission(true);
        params.setPatientControlNumber("PCN" + id);

        return params;
    }
}
