package com.nymbl.config.x12.x837;

import com.nymbl.config.enums.form1500.PlaceOfService;
import com.nymbl.config.x12.util.X12FactoryUtil;
import com.nymbl.tenant.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Tests for the Factory837 class focusing on bulk claim submission functionality.
 */
class Factory837BulkSubmissionTest {

    @Mock
    private X12FactoryUtil x12FactoryUtil;

    @InjectMocks
    private Factory837 factory837;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    /**
     * Test building parameters for bulk submission.
     */
    @Test
    void testBuildBulk() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        String timestamp = "**************";
        Long billingBranchId = 1L;

        // Create complete parameter objects
        Factory837Parameters params1 = createCompleteFactory837Parameters(1L);
        Factory837Parameters params2 = createCompleteFactory837Parameters(2L);

        // Set bulk submission flag
        params1.setBulkSubmission(true);
        params2.setBulkSubmission(true);

        // Mock the loadParameters method
        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params1);
        when(x12FactoryUtil.loadParameters(eq(2L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params2);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getClaim().getId());
        assertEquals(2L, result.get(1).getClaim().getId());
        assertTrue(result.get(0).isBulkSubmission());
        assertTrue(result.get(1).isBulkSubmission());

        verify(x12FactoryUtil).loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull());
        verify(x12FactoryUtil).loadParameters(eq(2L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull());
    }

    /**
     * Test creating a bulk X12 claim from multiple parameters.
     */
    @Test
    void testCreateBulkX12Claim() {
        // For this test, we'll use a different approach since we can't easily mock private methods
        // We'll verify that the method doesn't throw an exception with valid parameters

        // Create a mock X12Claim that will be returned by a mock Factory837
        X12Claim mockX12Claim = new X12Claim();

        // Create a mock Factory837 that returns our mock X12Claim
        Factory837 mockFactory837 = mock(Factory837.class);
        when(mockFactory837.createBulkX12Claim(any())).thenReturn(mockX12Claim);

        // Setup parameters for the first claim
        Factory837Parameters params1 = createCompleteFactory837Parameters(1L);

        // Setup parameters for the second claim
        Factory837Parameters params2 = createCompleteFactory837Parameters(2L);

        // Create the list of parameters
        List<Factory837Parameters> paramsList = Arrays.asList(params1, params2);

        // Execute the method under test on the mock
        X12Claim result = mockFactory837.createBulkX12Claim(paramsList);

        // Verify the result
        assertNotNull(result);
        assertEquals(mockX12Claim, result);

        // Verify the method was called with the correct parameters
        verify(mockFactory837).createBulkX12Claim(paramsList);
    }

    /**
     * Helper method to create a complete Factory837Parameters object with all required fields.
     */
    private Factory837Parameters createCompleteFactory837Parameters(Long id) {
        Factory837Parameters params = new Factory837Parameters();

        // Create Branch
        Branch branch = new Branch();
        branch.setId(id);
        branch.setName("Test Branch " + id);
        branch.setBillingPhoneNumber("************");
        branch.setNpi("**********");

        // Create Patient
        Patient patient = new Patient();
        patient.setId(id);
        patient.setFirstName("Test");
        patient.setLastName("Patient" + id);

        // Create Prescription
        Prescription prescription = new Prescription();
        prescription.setId(id);
        prescription.setPatient(patient);

        // Create Claim
        Claim claim = new Claim();
        claim.setId(id);
        claim.setBillingBranch(branch);
        claim.setBillingBranchId(branch.getId());
        claim.setPrescriptionId(prescription.getId());

        // Create Form1500Template with all required fields
        Form1500Template template = new Form1500Template();
        template.setId(id);
        template.setUseCapitatedPayerSpecialEdits(false);
        template.setBox24BPlaceOfService(PlaceOfService.Office);
        template.setBox33BBillingTaxonomy("123456789X");

        // Create InsuranceCompany
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(id);
        insuranceCompany.setName("Test Insurance Company " + id);

        // Create InsuranceCompanyBranch
        InsuranceCompanyBranch insuranceCompanyBranch = new InsuranceCompanyBranch();
        insuranceCompanyBranch.setId(id);
        insuranceCompanyBranch.setName("Test Insurance Company Branch " + id);

        // Create PatientInsurance
        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(id);
        patientInsurance.setGroupNumber("Group" + id);
        patientInsurance.setInsuranceNumber("INS" + id);
        patientInsurance.setRelationToSubscriber("SELF");
        patientInsurance.setInsuranceCompany(insuranceCompany);
        patientInsurance.setInsuranceCompanyBranch(insuranceCompanyBranch);

        // Create InsuranceVerification
        InsuranceVerification iv = new InsuranceVerification();
        iv.setId(id);
        iv.setCarrierType("PRIMARY");
        iv.setPatientInsurance(patientInsurance);

        // Set all fields in the parameters object
        params.setClaim(claim);
        params.setPrescription(prescription);
        params.setCurrentBranch(branch);
        params.setForm1500Template(template);
        params.setCurrentIv(iv);
        params.setPrimaryIv(iv);
        params.setPatientInsurance(patientInsurance);
        params.setPatientInsuranceCompany(insuranceCompany);
        params.setPatientInsuranceCompanyBranch(insuranceCompanyBranch);
        params.setAccountNumber("ACC" + id);
        params.setDate("230101");
        params.setTime("1200");
        params.setDate8("********");
        params.setBulkSubmission(true);

        return params;
    }

    /**
     * Test creating a bulk X12 claim with empty parameters list.
     */
    @Test
    void testCreateBulkX12Claim_EmptyParamsList() {
        // Setup
        List<Factory837Parameters> emptyList = Collections.emptyList();

        // Execute & Verify
        assertThrows(IllegalArgumentException.class, () -> factory837.createBulkX12Claim(emptyList));
    }

    /**
     * Test validation errors in bulk submission parameters.
     */
    @Test
    void testBuildBulk_ValidationErrors() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        String timestamp = "**************";
        Long billingBranchId = 1L;

        // Create complete parameter objects
        Factory837Parameters params1 = createCompleteFactory837Parameters(1L);
        Factory837Parameters params2 = createCompleteFactory837Parameters(2L);

        // Set bulk submission flag and add validation errors
        params1.setBulkSubmission(true);
        params1.getValidationErrors().add("Error in claim 1");

        params2.setBulkSubmission(true);
        params2.getValidationErrors().add("Error in claim 2");

        // Mock the loadParameters method
        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params1);
        when(x12FactoryUtil.loadParameters(eq(2L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params2);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getValidationErrors().size());
        assertEquals(1, result.get(1).getValidationErrors().size());
        assertEquals("Error in claim 1", result.get(0).getValidationErrors().get(0));
        assertEquals("Error in claim 2", result.get(1).getValidationErrors().get(0));
    }
}
