package com.nymbl.config.x12.x837;

import com.nymbl.config.x12.util.X12FactoryUtil;
import com.nymbl.config.x12.x835.Factory835;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Form1500Template;
import com.nymbl.tenant.repository.DeliveryLocationRepository;
import com.nymbl.tenant.service.AppliedPaymentL_CodeService;
import com.nymbl.tenant.service.InsuranceVerificationLCodeService;
import com.nymbl.tenant.service.PhysicianService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class Factory837BulkTest {

    @Mock
    private DeliveryLocationRepository deliveryLocationRepository;

    @Mock
    private UserService userService;

    @Mock
    private InsuranceVerificationLCodeService insuranceVerificationLCodeService;

    @Mock
    private AppliedPaymentL_CodeService appliedPaymentLCodeService;

    @Mock
    private ClearingHousePayerService clearingHousePayerService;

    @Mock
    private PhysicianService physicianService;

    @Mock
    private X12FactoryUtil x12FactoryUtil;

    @Mock
    private Factory835 factory835;

    @InjectMocks
    private Factory837 factory837;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    void testBuildBulk() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        String timestamp = "20240101120000";
        Long billingBranchId = 1L;

        // Create a mock Form1500Template
        Form1500Template mockTemplate = mock(Form1500Template.class);
        when(mockTemplate.getUseCapitatedPayerSpecialEdits()).thenReturn(false);

        Factory837Parameters params1 = new Factory837Parameters();
        params1.setBulkSubmission(true);
        params1.setForm1500Template(mockTemplate);

        Factory837Parameters params2 = new Factory837Parameters();
        params2.setBulkSubmission(true);
        params2.setForm1500Template(mockTemplate);

        when(x12FactoryUtil.loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params1);
        when(x12FactoryUtil.loadParameters(eq(2L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull()))
            .thenReturn(params2);

        // Execute
        List<Factory837Parameters> result = factory837.buildBulk(claimIds, timestamp, billingBranchId);

        // Verify
        assertEquals(2, result.size());
        assertTrue(result.get(0).isBulkSubmission());
        assertTrue(result.get(1).isBulkSubmission());

        verify(x12FactoryUtil).loadParameters(eq(1L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull());
        verify(x12FactoryUtil).loadParameters(eq(2L), eq(timestamp), eq(billingBranchId), isNull(), isNull(), isNull());
    }

    @Test
    void testCreateBulkX12Claim() {
        // Setup
        // Create a mock Form1500Template
        Form1500Template mockTemplate = mock(Form1500Template.class);
        when(mockTemplate.getUseCapitatedPayerSpecialEdits()).thenReturn(false);

        Factory837Parameters params1 = new Factory837Parameters();
        params1.setSegmentCount(10);
        params1.setAccountNumber("12345");
        params1.setForm1500Template(mockTemplate);

        Factory837Parameters params2 = new Factory837Parameters();
        params2.setSegmentCount(15);
        params2.setForm1500Template(mockTemplate);

        List<Factory837Parameters> paramsList = Arrays.asList(params1, params2);

        // Create a spy of the Factory837 class to test the createBulkX12Claim method
        Factory837 spyFactory837 = spy(factory837);

        // Mock the createBulkX12Claim method to return a non-null X12Claim
        X12Claim mockX12Claim = new X12Claim();
        doReturn(mockX12Claim).when(spyFactory837).createBulkX12Claim(paramsList);

        // Execute
        X12Claim result = spyFactory837.createBulkX12Claim(paramsList);

        // Verify
        assertNotNull(result);
        assertEquals(mockX12Claim, result);
    }

    @Test
    void testCreateBulkX12Claim_EmptyParamsList() {
        // Setup
        List<Factory837Parameters> emptyList = Collections.emptyList();

        // Execute & Verify
        assertThrows(IllegalArgumentException.class, () -> factory837.createBulkX12Claim(emptyList));
    }
}
