package com.nymbl.config.service;

import com.nymbl.config.model.ShortUrl;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
public class ShortUrlServiceTest {

    @Mock
    private RedisTemplate<String, ShortUrl> redisTemplate;

    @Mock
    private ValueOperations<String, ShortUrl> valueOperations;

    private ShortUrlService shortUrlService;



    @BeforeEach
    public void setUp() {
        // With @ExtendWith(MockitoExtension.class), mocks are automatically initialized
        // Only set up the redisTemplate.opsForValue() stub which is used by both tests
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        shortUrlService = new ShortUrlService(redisTemplate);
        shortUrlService.setBaseUrl("http://localhost:8080/");
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void createShortUrl() {
        // Set up the specific mock behavior needed for this test
        Mockito.doNothing().when(valueOperations).set(anyString(), any(ShortUrl.class));

        String testUrl = "http://google.com/blabla";
        try {
            ShortUrl newUrl = shortUrlService.createShortUrl(testUrl);
            assertEquals(testUrl, newUrl.getUrl());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Test
    void getShortUrl() {
        // Implement a simple test to avoid "No runnable methods" error
        ShortUrl mockShortUrl = new ShortUrl();
        mockShortUrl.setUrl("http://google.com/test");
        mockShortUrl.setId("abc123");
        mockShortUrl.setShortLink("http://localhost:8080/abc123");
        mockShortUrl.setBaseUrl("http://localhost:8080/");

        Mockito.when(valueOperations.get(anyString())).thenReturn(mockShortUrl);

        try {
            ShortUrl result = shortUrlService.getShortUrl("abc123");
            assertEquals("http://google.com/test", result.getUrl());
            assertEquals("abc123", result.getId());
            assertEquals("http://localhost:8080/abc123", result.getShortLink());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

