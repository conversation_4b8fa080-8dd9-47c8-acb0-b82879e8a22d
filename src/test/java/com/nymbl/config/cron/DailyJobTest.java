package com.nymbl.config.cron;

import com.nymbl.tenant.model.InsuranceCompany;
import com.nymbl.tenant.model.PatientInsurance;
import com.nymbl.tenant.model.Prescription;
import com.nymbl.tenant.model.Task;
import com.nymbl.tenant.service.*;
import com.nymbl.tenant.service.Fake;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

public class DailyJobTest {
    @Mock
    private PrescriptionService mockPrescriptionService;
    @Mock
    private TaskService mockTaskService;
    @Mock
    private InsuranceCompanyService mockInsuranceCompanyService;
    @Mock
    private DailyJobExecutor mockDailyJobExecutor;

    private DailyJob dailyJobUnderTest;

    @BeforeEach
    void setUp() {
        openMocks(this);
        dailyJobUnderTest = new DailyJob(mockDailyJobExecutor);

        // Set up the mocked methods for timelyFilingJob
        doAnswer(invocation -> {
            mockInsuranceCompanyService.findByTimelyFilingDaysIsNotNull();
            return null;
        }).when(mockDailyJobExecutor).timelyFilingJob();

        // Set up the mocked methods for dueTasksJob
        doAnswer(invocation -> {
            mockTaskService.findByDueDateAndCompleted(any(Date.class));
            return null;
        }).when(mockDailyJobExecutor).dueTasksJob();
    }

//    @Test
//    void testProcess() {
//        // Setup
//        when(mockCompanyRepository.findAllByActiveTrue()).thenReturn(Arrays.asList());
//        when(mockPrescriptionService.findByPatientRecallIsTrue()).thenReturn(Arrays.asList());
//
//        // Configure NotificationService.save(...).
//        final Notification notification = new Notification();
//        when(mockNotificationService.save(new Notification())).thenReturn(notification);
//
//        // Configure UserNotificationService.save(...).
//        final UserNotification userNotification = new UserNotification();
//        when(mockUserNotificationService.save(new UserNotification())).thenReturn(userNotification);
//
//        // Configure UserNotificationService.findOne(...).
//        final UserNotification userNotification = new UserNotification();
//        when(mockUserNotificationService.findOne(0L)).thenReturn(userNotification);
//
//        // Configure PrescriptionService.save(...).
//        final Prescription prescription = new Prescription();
//        when(mockPrescriptionService.save(new Prescription())).thenReturn(prescription);
//
//        when(mockFollowUpService.findByFollowUpDate(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime())).thenReturn(Arrays.asList());
//        when(mockInsuranceVerificationService.findByExpirationDate(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime())).thenReturn(Arrays.asList());
//
//        // Configure CriticalMessageService.save(...).
//        final CriticalMessage criticalMessage = new CriticalMessage();
//        when(mockCriticalMessageService.save(new CriticalMessage())).thenReturn(criticalMessage);
//
//        when(mockInsuranceCompanyService.findByTimelyFilingDaysIsNotNull()).thenReturn(Arrays.asList());
//        when(mockPrescriptionService.findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingToday(0L)).thenReturn(Arrays.asList());
//
//        // Run the test
//        dailyJobUnderTest.process();
//
//        // Verify the results
//        verify(mockTemplate).convertAndSend("destination", "payload");
//        verify(mockNotificationService).createTimelyFilingNotification(new Prescription());
//    }

    // @Test
    // void testPecosVerificationUpdate() {
    //
    //     // Setup
    //     // Create Physician that is PECOS Verified
    //     Physician pecosVerifiedPhysician = new Physician();
    //     pecosVerifiedPhysician.setId(1L);
    //     pecosVerifiedPhysician.setNpi("**********");
    //
    //     // Create Physician that is NOT PECOS Verified
    //     Physician nonPecosVerifiedPhysician = new Physician();
    //     nonPecosVerifiedPhysician.setId(2L);
    //     nonPecosVerifiedPhysician.setNpi("9");
    //
    //     // Add fake physicians to a list
    //     List<Physician> physicians = new ArrayList<>();
    //     physicians.add(pecosVerifiedPhysician);
    //     physicians.add(nonPecosVerifiedPhysician);
    //
    //     // Mock findAll() to return the fake physicians created above
    //     when(mockPhysicianService.findAll()).thenReturn(physicians);
    // }
//
//    @Test
//    void testProcess_SimpMessagingTemplateThrowsMessagingException() {
//        // Setup
//        when(mockCompanyRepository.findAllByActiveTrue()).thenReturn(Arrays.asList());
//        when(mockPrescriptionService.findByPatientRecallIsTrue()).thenReturn(Arrays.asList());
//
//        // Configure NotificationService.save(...).
//        final Notification notification = new Notification();
//        when(mockNotificationService.save(new Notification())).thenReturn(notification);
//
//        // Configure UserNotificationService.save(...).
//        final UserNotification userNotification = new UserNotification();
//        when(mockUserNotificationService.save(new UserNotification())).thenReturn(userNotification);
//
//        // Configure UserNotificationService.findOne(...).
//        final UserNotification userNotification = new UserNotification();
//        when(mockUserNotificationService.findOne(0L)).thenReturn(userNotification);
//
//        doThrow(MessagingException.class).when(mockTemplate).convertAndSend("destination", "payload");
//
//        // Configure PrescriptionService.save(...).
//        final Prescription prescription = new Prescription();
//        when(mockPrescriptionService.save(new Prescription())).thenReturn(prescription);
//
//        when(mockFollowUpService.findByFollowUpDate(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime())).thenReturn(Arrays.asList());
//        when(mockInsuranceVerificationService.findByExpirationDate(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime())).thenReturn(Arrays.asList());
//
//        // Configure CriticalMessageService.save(...).
//        final CriticalMessage criticalMessage = new CriticalMessage();
//        when(mockCriticalMessageService.save(new CriticalMessage())).thenReturn(criticalMessage);
//
//        when(mockInsuranceCompanyService.findByTimelyFilingDaysIsNotNull()).thenReturn(Arrays.asList());
//        when(mockPrescriptionService.findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingToday(0L)).thenReturn(Arrays.asList());
//
//        // Run the test
//        dailyJobUnderTest.process();
//
//        // Verify the results
//        verify(mockNotificationService).createTimelyFilingNotification(new Prescription());
//    }

    @Test
    public void testTimelyFiling() {

        InsuranceCompany ic = new InsuranceCompany();
        ic.setId(Fake.ID);
        ic.setTimelyFilingDays(30);

        InsuranceCompany ic2 = new InsuranceCompany();
        ic2.setId(Fake.ID2);
        ic2.setTimelyFilingDays(60);

        PatientInsurance pi = new PatientInsurance();
        pi.setId(Fake.ID);
        pi.setInsuranceCompanyId(Fake.ID);

        Prescription rx = new Prescription();
        rx.setId(Fake.ID);
        rx.setPatientInsuranceId(Fake.ID);
        rx.setCreatedAt(Timestamp.valueOf(LocalDateTime.now().minusDays(ic.getTimelyFilingDays())));

        when(mockInsuranceCompanyService.findByTimelyFilingDaysIsNotNull()).thenReturn(Arrays.asList(ic, ic2));
        when(mockPrescriptionService.findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingToday(Fake.ID)).thenReturn(Collections.singletonList(rx));

        // Run the test
        dailyJobUnderTest.timelyFilingJob();
    }

    @Test
    public void dueTasksTest() {
        Task task1 = new Task();
        task1.setId(Fake.ID);
        task1.setName("Test Task 1");
        task1.setPatientId(Fake.ID);
        task1.setUserId(Fake.ID);
        Task task2 = new Task();
        task2.setId(Fake.ID2);
        task2.setName("Test Task 2");
        task2.setPatientId(Fake.ID2);
        task2.setUserId(Fake.ID2);
        List<Task> tasks = Arrays.asList(task1, task2);
        when(mockTaskService.findByDueDateAndCompleted(new GregorianCalendar(2019, Calendar.JANUARY, 1, 0, 0, 0).getTime())).thenReturn(tasks);

        dailyJobUnderTest.dueTasksJob();
    }

}
