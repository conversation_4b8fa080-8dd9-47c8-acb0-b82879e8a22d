package com.nymbl.config;

import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.TenantContextCopyingDecorator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the AsyncConfig class.
 * These tests verify that the async executor is properly configured with tenant context propagation.
 */
class AsyncConfigTest {

    private AsyncConfig asyncConfig;

    @BeforeEach
    void setUp() {
        asyncConfig = new AsyncConfig();
    }

    @Test
    void testTenantContextCopyingDecorator() {
        // Execute
        TaskDecorator decorator = asyncConfig.tenantContextCopyingDecorator();

        // Verify
        assertNotNull(decorator);
        assertTrue(decorator instanceof TenantContextCopyingDecorator);
    }

    @Test
    void testGetAsyncExecutor() {
        // Execute
        Executor executor = asyncConfig.getAsyncExecutor();

        // Verify
        assertNotNull(executor);
        assertTrue(executor instanceof ThreadPoolTaskExecutor);

        ThreadPoolTaskExecutor taskExecutor = (ThreadPoolTaskExecutor) executor;
        assertEquals(5, taskExecutor.getCorePoolSize());
        assertEquals(10, taskExecutor.getMaxPoolSize());
        assertEquals(25, taskExecutor.getQueueCapacity());
        assertEquals("nymbl-async-", taskExecutor.getThreadNamePrefix());
    }

    @Test
    void testTenantContextCopyingDecoratorBehavior() {
        // Setup
        TaskDecorator decorator = asyncConfig.tenantContextCopyingDecorator();
        String expectedTenant = "test-tenant";
        String expectedTimezone = "America/New_York";
        TenantContext.setCurrentTenant(expectedTenant);
        TenantContext.setUserTimezoneId(expectedTimezone);

        // Variables to capture values
        final String[] capturedTenant = new String[1];
        final String[] capturedTimezone = new String[1];

        try {
            // Create a runnable that will capture the tenant context
            Runnable originalRunnable = () -> {
                capturedTenant[0] = TenantContext.getCurrentTenant();
                capturedTimezone[0] = TenantContext.getUserTimezoneId();
            };

            // Decorate the runnable
            Runnable decoratedRunnable = decorator.decorate(originalRunnable);

            // Execute the decorated runnable directly (not in a separate thread)
            decoratedRunnable.run();

            // Verify
            assertEquals(expectedTenant, capturedTenant[0], "Tenant ID was not properly propagated");
            assertEquals(expectedTimezone, capturedTimezone[0], "Timezone ID was not properly propagated");
        } finally {
            // Clean up
            TenantContext.clear();
        }
    }
}
