package com.nymbl.config.exception;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class X12ExceptionTest {

    @Test
    void testConstructorWithLinkedList() {
        // Setup
        LinkedList<String> errors = new LinkedList<>();
        errors.add("Error 1");
        errors.add("Error 2");
        
        // Execute
        X12Exception exception = new X12Exception(errors);
        
        // Verify
        assertEquals("Error 1<br>Error 2", exception.getMessage());
    }
    
    @Test
    void testConstructorWithList() {
        // Setup
        List<String> errors = Arrays.asList("Error 1", "Error 2");
        
        // Execute
        X12Exception exception = new X12Exception(errors);
        
        // Verify
        assertEquals("Error 1<br>Error 2", exception.getMessage());
    }
    
    @Test
    void testConstructorWithEmptyLinkedList() {
        // Setup
        LinkedList<String> errors = new LinkedList<>();
        
        // Execute
        X12Exception exception = new X12Exception(errors);
        
        // Verify
        assertEquals("", exception.getMessage());
    }
    
    @Test
    void testConstructorWithEmptyList() {
        // Setup
        List<String> errors = new ArrayList<>();
        
        // Execute
        X12Exception exception = new X12Exception(errors);
        
        // Verify
        assertEquals("", exception.getMessage());
    }
    
    @Test
    void testConstructorWithSingleItemLinkedList() {
        // Setup
        LinkedList<String> errors = new LinkedList<>();
        errors.add("Single Error");
        
        // Execute
        X12Exception exception = new X12Exception(errors);
        
        // Verify
        assertEquals("Single Error", exception.getMessage());
    }
    
    @Test
    void testConstructorWithSingleItemList() {
        // Setup
        List<String> errors = Arrays.asList("Single Error");
        
        // Execute
        X12Exception exception = new X12Exception(errors);
        
        // Verify
        assertEquals("Single Error", exception.getMessage());
    }
}
