package com.nymbl.config.controller;

import com.nymbl.config.Constants;
import com.nymbl.config.service.DataDictionaryService;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.service.BulkClaimJobService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class DownloadControllerTest {

    @Mock
    private DataDictionaryService dataDictionaryService;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @InjectMocks
    private DownloadController downloadController;

    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new MockHttpServletRequest();
        request.addHeader("User-Agent", "Mozilla/5.0");
        request.addHeader("Accept", MediaType.APPLICATION_OCTET_STREAM_VALUE);
    }

    @Test
    void testDownloadBulkX12_Success() {
        // Arrange
        String jobId = "test-job-id";
        String x12Content = "X12 content for bulk claim job";

        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setX12FileContent(x12Content);

        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);

        // Act
        ResponseEntity<?> response = downloadController.downloadBulk(jobId, request);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(MediaType.TEXT_PLAIN_VALUE, response.getHeaders().getContentType().toString());
        assertTrue(response.getHeaders().getContentDisposition().toString().contains("attachment"));
        assertTrue(response.getHeaders().getContentDisposition().toString().contains("bulk_" + jobId));
        assertEquals(x12Content, response.getBody());

        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }

    @Test
    void testDownloadBulkX12_JobNotFound() {
        // Arrange
        String jobId = "non-existent-job-id";
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(null);

        // Act
        ResponseEntity<?> response = downloadController.downloadBulk(jobId, request);

        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody()); // The body contains an error message

        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }

    @Test
    void testDownloadBulkX12_EmptyContent() {
        // Arrange
        String jobId = "test-job-id";

        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setX12FileContent("");

        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);

        // Act
        ResponseEntity<?> response = downloadController.downloadBulk(jobId, request);

        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody()); // The body contains an error message

        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }

    @Test
    void testDownloadBulkX12_PageLoadRequest() {
        // Arrange
        String jobId = "test-job-id";
        MockHttpServletRequest pageLoadRequest = new MockHttpServletRequest();
        pageLoadRequest.addHeader("User-Agent", "Mozilla/5.0");

        // Act
        ResponseEntity<?> response = downloadController.downloadBulk(jobId, pageLoadRequest);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());

        // Verify that we don't try to find the job for page load requests
        verify(bulkClaimJobService, never()).findByJobId(anyString());
    }
    @Test
    void testRedirectToBulkDownload() {
        // This test would require mocking the Claim service and other dependencies
        // Since we're focusing on the bulk claim job functionality, this is a placeholder
        // for a test that would verify the redirect from individual claim download to bulk download
        // when a claim is part of a bulk submission
    }
}