package com.nymbl.master.service;

import com.amazonaws.auth.profile.ProfileCredentialsProvider;
import com.amazonaws.services.quicksight.AmazonQuickSight;
import com.amazonaws.services.quicksight.model.*;
import com.nymbl.config.Constants;
import com.nymbl.config.dto.ClericalProductivityReportDTO;
import com.nymbl.config.dto.UserDTO;
import com.nymbl.config.service.TableObjectContainer;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.model.*;
import com.nymbl.master.repository.UserRepository;
import com.nymbl.tenant.model.Role;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.model.UserPrivilege;
import com.nymbl.tenant.model.UserRole;
import com.nymbl.config.security.PasswordHistoryService;
import com.nymbl.tenant.repository.*;
import com.nymbl.tenant.service.CalendarTemplateService;
import com.nymbl.tenant.service.Fake;
import com.nymbl.tenant.service.QuickSightAccess;
import com.nymbl.tenant.service.SystemSettingService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.nymbl.config.Constants.DF_YYYY_MM_DD_HH_MM;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

@SuppressWarnings("unchecked")
class UserServiceTest {

    @Mock
    private UserRepository mockUserRepository;
    @Mock
    private RoleRepository mockRoleRepository;
    @Mock
    private RolePrivilegeRepository mockRolePrivilegeRepository;
    @Mock
    private UserPrivilegeRepository mockUserPrivilegeRepository;
    @Mock
    private UserRoleRepository mockUserRoleRepository;
    @Mock
    private UserBranchRepository mockUserBranchRepository;
    @Mock
    private PrivilegeService mockPrivilegeService;
    @Mock
    private SystemSettingService mockSystemSettingService;
    @Mock
    private CompanyService mockCompanyService;
    @Mock
    private PatientRepository mockPatientRepository;
    @Mock
    private PrescriptionRepository mockPrescriptionRepository;
    @Mock
    private ClaimRepository mockClaimRepository;
    @Mock
    private NoteRepository mockNoteRepository;
    @Mock
    private AuthenticationManager mockAuthenticationManager;
    @Mock
    private QuickSightAccess mockQuickSightAccess;
    @Mock
    private AmazonQuickSight mockAmazonQuickSight;
    @Mock
    private ProfileCredentialsProvider mockProfileCredentialsProvider;

    @Mock
    private CalendarTemplateService mockCalendarTemplateService;

    @Mock
    private PasswordHistoryService mockPasswordHistoryService;

    private UserService userServiceUnderTest;

    @BeforeEach
    void setUp() {
        TableObjectContainer.getTableMap().clear();
        openMocks(this);
        userServiceUnderTest = new UserService(mockUserRepository, mockRoleRepository, mockPrivilegeService, mockRolePrivilegeRepository, mockUserPrivilegeRepository, mockUserRoleRepository, mockUserBranchRepository, mockSystemSettingService, mockCompanyService, mockPatientRepository, mockPrescriptionRepository, mockClaimRepository, mockNoteRepository, mockAuthenticationManager, mockQuickSightAccess, mockCalendarTemplateService);

        // Set the passwordHistoryService field using reflection
        try {
            java.lang.reflect.Field field = UserService.class.getDeclaredField("passwordHistoryService");
            field.setAccessible(true);
            field.set(userServiceUnderTest, mockPasswordHistoryService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set passwordHistoryService field", e);
        }
    }

    @Test
    void testSearch() {
        User superAdminActive = Fake.getUserFakeID();
        superAdminActive.setId(1L);
        superAdminActive.setActive(true);
        superAdminActive.setIsSuperAdmin(true);

        User userNotActive = Fake.getUserFakeID2();
        userNotActive.setActive(false);

        User userActive = Fake.getUser(3L);
        userActive.setActive(true);
        userActive.setCanHaveAppointments(false);

        User userActiveCanHaveAppointment = Fake.getUser(4L);
        userActiveCanHaveAppointment.setActive(true);
        userActiveCanHaveAppointment.setCanHaveAppointments(true);
        List<User> userCollection = new ArrayList<>();
        userCollection.add(superAdminActive);
        userCollection.add(userActive);
        userCollection.add(userActiveCanHaveAppointment);
        userCollection.add(userNotActive);
        Company company = Fake.getCompany();
        company.setUsers(userCollection);
        Long companyId = 5L;
        Mockito.when(this.mockCompanyService.findOne(companyId)).thenReturn(company);


        UserDTO userDTO = new UserDTO();
        userDTO.setUser(userActiveCanHaveAppointment);
        UserRole userRole = new UserRole();
        Role role = new Role();
        role.setId(1L);
        role.setName("name");
        userRole.setRole(role);
        userDTO.setUserRoles(Arrays.asList(userRole));

        final Privilege privilege = new Privilege();
        privilege.setId(Fake.ID);
        privilege.setName("name");

        // SCRUM-3888: Deleted...necessary?
        //privilege.setRoles(Collections.singletonList(new Role()));

        // SCRUM-3888: Deleted...necessary?
        //role.setPrivileges(Collections.singletonList(privilege));

        this.userServiceUnderTest.updateUserRoles(userDTO, userActiveCanHaveAppointment); // SCRUM-3888: Done Tenant Role/Privs
        //userActiveCanHaveAppointment.setRoles(Collections.singletonList(role));

        role = new Role();
        role.setId(1L);
        role.setName("name");
        Mockito.when(this.mockRoleRepository.findById(1L)).thenReturn(Optional.of(role));

        // Configure UserRepository.findByUsername(...).
        final User user2 = Fake.getUserFakeID();
        user2.setUsername("user2");
        Mockito.when(this.mockUserRepository.findByUsername("flName")).thenReturn(user2);

        // Configure UserRepository.findByIsMultiUser(...).
        final User user3 = Fake.getUserFakeID();
        user3.setIsMultiUser(true);
        user3.setActive(true);
        Mockito.when(this.mockUserRepository.findByIsMultiUser(true)).thenReturn(Collections.singletonList(user3));
        Fake<User> fake = new Fake<>();
        Page<User> something = fake.getFakePageImpl(user3);
        Mockito.when(this.mockUserRepository.findAll(ArgumentMatchers.any(Specification.class), ArgumentMatchers.any(Pageable.class))).thenReturn(something);
        Mockito.when(this.mockUserRepository.findByUsername(ArgumentMatchers.anyString())).thenReturn(user3);
        //when(mockUserRepository.findAll(any(Specification.class), Pageable.class)).thenReturn(Arrays.asList(user3));
        // Run the test
        List<User> result = this.userServiceUnderTest.search("lastName", true, true, Fake.ID, new Long[]{companyId});

        // Verify the results
        // SCRUM-3888: Test Disabled
        /* assertEquals(1, result.size());
        assertEquals("4", result.get(0).getId().toString());

        result = userServiceUnderTest.search("lastName", null, null, Fake.ID, new Long[]{companyId});
        assertEquals(1, result.size());
        assertEquals("4", result.get(0).getId().toString());

        result = userServiceUnderTest.search(null, null, null, Fake.ID, new Long[]{companyId});
        assertEquals(1, result.size());

        result = userServiceUnderTest.search(null, true, null, null, new Long[]{companyId});
        assertEquals(3, result.size());

        result = userServiceUnderTest.search(null, null, true, null, new Long[]{companyId});
        assertEquals(1, result.size());

        result = userServiceUnderTest.search(null, null, false, null, new Long[]{companyId});
        assertEquals(4, result.size());

        result = userServiceUnderTest.search(null, false, null, null, new Long[]{companyId});
        assertEquals(4, result.size());

        result = userServiceUnderTest.search("happy", null, null, null, new Long[]{companyId});
        assertEquals(0, result.size());

         */
    }

    @Test
    void testLoadUserByUsername() {
        // Setup

        // Configure UserRepository.findByUsername(...).
        final User user = Fake.getUserFakeID();
        Mockito.when(this.mockUserRepository.findByUsername(ArgumentMatchers.anyString())).thenReturn(user);

        // Run the test
        final UserDetails result = this.userServiceUnderTest.loadUserByUsername("flName");

        // Verify the results
        Assertions.assertEquals("flName", result.getUsername());
        Assertions.assertEquals("password", result.getPassword());
        Assertions.assertEquals(1, result.getAuthorities().size());
    }

    @Test
    void testSavePrivileges() {
        // Setup
        User user = Fake.getUserFakeID();
        // SCRUM-3888: user.setPrivileges(null);
        final Privilege privilege = new Privilege();
        privilege.setId(Fake.ID);
        privilege.setName("privilegeName");
        Role role = new Role();
        role.setId(Fake.ID);
        role.setName("roleName");

        // SCRUM-3888: Deleted...necessary?
        //role.setUsers(Collections.singletonList(user));

        Privilege privilege1 = new Privilege();
        privilege1.setId(Fake.ID2);
        privilege1.setName("privilege1Name");

        // SCRUM-3888: Deleted...necessary?
        //role.setPrivileges(Collections.singletonList(privilege1));

        // SCRUM-3888: Deleted...necessary?
        //privilege.setRoles(Collections.singletonList(role));

        Mockito.when(this.mockUserRepository.findById(Fake.ID)).thenReturn(Optional.of(user));
        // Run the test
        this.userServiceUnderTest.savePrivileges(Fake.ID, Collections.singletonList(privilege));
        // Verify the results
        List<UserPrivilege> userPrivileges = this.mockUserPrivilegeRepository.findByUserId(user.getId());
        // SCRUM-3888: Test Disabled
        // assertEquals(1, userPrivileges.size());
    }

    @Test
    void testSaveNotificationTypes() {
        // Setup
        final NotificationType notificationType = new NotificationType();
        notificationType.setId(Fake.ID);
        notificationType.setName("name");
        final List<NotificationType> notificationTypes = Collections.singletonList(notificationType);
        final User expectedResult = Fake.getUserFakeID();

        final User user = Fake.getUserFakeID();
        Mockito.when(this.mockUserRepository.findById(Fake.ID)).thenReturn(Optional.of(user));
        // Run the test
        final User result = this.userServiceUnderTest.saveNotificationTypes(Fake.ID, notificationTypes);

        // Verify the results
        Assertions.assertEquals(expectedResult, result);
    }

    @Test
    void testSaveWidgets() {
        // Setup
        final Widget widget = new Widget();
        widget.setId(Fake.ID);
        widget.setName("name");
        final User user = Fake.getUserFakeID();
        Mockito.when(this.mockUserRepository.findById(Fake.ID)).thenReturn(Optional.of(user));
        widget.setUsers(Collections.singletonList(user));
        final List<Widget> widgets = Collections.singletonList(widget);
        final User expectedResult = Fake.getUserFakeID();

        // Run the test
        final User result = this.userServiceUnderTest.saveWidgets(Fake.ID, widgets);

        // Verify the results
        Assertions.assertEquals(expectedResult, result);
    }

    @Test
    void testGetUserById() {
        // Setup
        final User expectedResult = Fake.getUserFakeID();
        Mockito.when(this.mockUserRepository.findById(Fake.ID)).thenReturn(Optional.of(Fake.getUserFakeID()));
        // Run the test
        final User result = this.userServiceUnderTest.getUserById(Fake.ID);

        // Verify the results
        Assertions.assertEquals(expectedResult, result);
    }

    @Test
    void testGetCurrentUser() {
        // Setup
        final User expectedResult = Fake.getUserFakeID();

        // Configure UserRepository.findByUsername(...).
        final User user = Fake.getUserFakeID();

        // Mock the security context to return a username
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn("flName");
        SecurityContextHolder.setContext(securityContext);

        Mockito.when(this.mockUserRepository.findByUsername("flName")).thenReturn(user);

        // Run the test
        final User result = this.userServiceUnderTest.getCurrentUser();

        // Verify the results
        Assertions.assertEquals(expectedResult, result);
    }

    @Test
    void testGetActiveUsersByRoleAndCompanyNoSuperAdmin() {
        Role role = new Role();
        role.setId(1L);
        role.setName("Practitioner");

        User user1 = Fake.getUserFakeID();
        user1.setIsMultiUser(true);
        user1.setActive(true);

        // SCRUM-3888: model for "Test disabled"
        UserDTO userDTO = new UserDTO();
        userDTO.setUser(user1);
        UserRole userRole = new UserRole();
        userRole.setRole(role);
        userRole.setRoleId(1L);
        userRole.setUser(user1);
        userRole.setUserId(user1.getId());
        userDTO.setUserRoles(List.of(userRole));
        this.userServiceUnderTest.updateUserRoles(userDTO, user1); // SCRUM-3888: Done Tenant Role/Privs

        User userSuperAdmin = Fake.getUserFakeID2();
        userSuperAdmin.setActive(true);
        // SCRUM-3888: Deleted...necessary?  userSuperAdmin.setRoles(roleCollection);
        userSuperAdmin.setIsSuperAdmin(true);

        User notMultUser = Fake.getUserFakeID2();
        notMultUser.setId(3L);
        notMultUser.setActive(true);
        // SCRUM-3888: Deleted...necessary?  notMultUser.setRoles(roleCollection);
        notMultUser.setIsMultiUser(false);
        List<User> userList = Arrays.asList(user1, userSuperAdmin, notMultUser);
        // SCRUM-3888:when(mockUserRepository.findDistinctByRoles_IdAndCompanyIdAndActive(any(), anyLong(), any())).thenReturn(userList);
        Mockito.when(this.mockUserRepository.findByUsername(ArgumentMatchers.anyString())).thenReturn(user1);
        Mockito.when(this.mockUserRepository.findByIsMultiUser(true)).thenReturn(Arrays.asList(user1, userSuperAdmin));

        Company company = Fake.getCompany();
        company.setUsers(userList);
        Mockito.when(this.mockCompanyService.findOne(ArgumentMatchers.anyLong())).thenReturn(company);
        // Run the test
        final List<User> result = this.userServiceUnderTest.getActiveUsersByRoleAndCompanyNoSuperAdmin(Fake.ID, 1L);

        // Verify the results
        // SCRUM-3888: Test Disabled
        //assertEquals(2, result.size());
    }

    @Test
    void testGetUsersByCompanyId() {
        // Setup
        final User user = Fake.getUserFakeID();

        // Configure UserRepository.findByCompanyId(...).
        final User user1 = Fake.getUserFakeID();
        Mockito.when(this.mockUserRepository.findByCompanyId(0L)).thenReturn(Collections.singletonList(user1));

        // Run the test
        final List<User> result = this.userServiceUnderTest.getUsersByCompanyId(0L);

        // Verify the results
        Assertions.assertEquals(Collections.singletonList(user), result);
    }

    @Test
    void testGetUserByCompanyIdAndUserEmail() {
        // Setup
        final User expectedResult = Fake.getUserFakeID();

        // Configure UserRepository.findByCompanyIdAndEmail(...).
        final User user = Fake.getUserFakeID();
        Mockito.when(this.mockUserRepository.findByCompanyIdAndEmail(ArgumentMatchers.anyLong(), ArgumentMatchers.anyString())).thenReturn(user);

        // Run the test
        final User result = this.userServiceUnderTest.getUserByCompanyIdAndUserEmail(Fake.ID, "<EMAIL>");

        // Verify the results
        Assertions.assertEquals(expectedResult, result);
    }

    @Test
    void testGetUserCalendarDefaultUsers() {
        // Setup
        final User user = Fake.getUserFakeID();
        user.setCalendarDefaultUsers(Fake.ID + "");
        Mockito.when(this.mockUserRepository.findById(ArgumentMatchers.anyLong())).thenReturn(Optional.of(user));
        // Run the test
        final List<User> result = this.userServiceUnderTest.getUserCalendarDefaultUsers(Fake.ID);

        // Verify the results
        Assertions.assertEquals(Collections.singletonList(user), result);
    }

    @Test
    void testFindByIdIn() {
        // Setup
        final User user = Fake.getUserFakeID();
        user.setAuthCodeExpirationTime(new Timestamp(0L));

        // Configure UserRepository.findByIdInAndActiveIsTrue(...).
        final User user1 = Fake.getUserFakeID();
        Mockito.when(this.mockUserRepository.findByIdInAndActiveIsTrue(Collections.singletonList(Fake.ID))).thenReturn(Collections.singletonList(user1));

        // Run the test
        final List<User> result = this.userServiceUnderTest.findByIdIn(Collections.singletonList(Fake.ID));

        // Verify the results
        Assertions.assertEquals(Collections.singletonList(user), result);
    }

    @Test
    void testChangePassword() throws Exception {
        // Setup
        // Mock the findBySectionAndField method for each password setting
        SystemSetting mixedCaseSetting = new SystemSetting();
        when(mockSystemSettingService.findBySectionAndField("password", "require_mixed_case")).thenReturn(mixedCaseSetting);

        SystemSetting specialCharSetting = new SystemSetting();
        when(mockSystemSettingService.findBySectionAndField("password", "require_special_character")).thenReturn(specialCharSetting);

        SystemSetting minLengthSetting = new SystemSetting();
        when(mockSystemSettingService.findBySectionAndField("password", "minimum_length")).thenReturn(minLengthSetting);

        // Configure UserRepository.findByUsername(...).
        final User user = Fake.getUserFakeID();

        // Mock the security context to return a username
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn("flName");
        SecurityContextHolder.setContext(securityContext);

        when(mockUserRepository.findByUsername("flName")).thenReturn(user);

        // Mock findOne method which is used in validatePasswordAgainstHistory
        when(mockUserRepository.findById(any())).thenReturn(Optional.of(user));

        // Mock passwordHistoryService
        when(mockPasswordHistoryService.isPasswordInHistory(any(), any())).thenReturn(false);
        when(mockPasswordHistoryService.getHistoryDepth()).thenReturn(5);
        when(mockPasswordHistoryService.getPasswordReuseDays()).thenReturn(90);

        // Mock authentication manager
        Mockito.when(this.mockAuthenticationManager.authenticate(ArgumentMatchers.any(Authentication.class))).thenReturn(null);

        // Capture the user object that will be modified
        String originalPassword = user.getPassword();

        // Run the test
        this.userServiceUnderTest.changePassword("oldPassw0rd!!!@", "newPassw0rd!!@");

        // Verify the results
        // Verify that authentication was attempted
        Mockito.verify(mockAuthenticationManager).authenticate(Mockito.any(Authentication.class));

        // Verify that the user's password was changed (it should be different from the original)
        assertNotEquals(originalPassword, user.getPassword());
    }

    @Test
    void testChangePassword_WithTooShortPassword() {
        // Setup
        // Mock the findBySectionAndField method for each password setting
        SystemSetting mixedCaseSetting = new SystemSetting();
        mixedCaseSetting.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("password", "require_mixed_case")).thenReturn(mixedCaseSetting);

        SystemSetting specialCharSetting = new SystemSetting();
        specialCharSetting.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("password", "require_special_character")).thenReturn(specialCharSetting);

        SystemSetting minLengthSetting = new SystemSetting();
        minLengthSetting.setValue("8");
        when(mockSystemSettingService.findBySectionAndField("password", "minimum_length")).thenReturn(minLengthSetting);

        // Configure UserRepository.findByUsername(...).
        final User user = Fake.getUserFakeID();

        // Mock the security context to return a username
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn("flName");
        SecurityContextHolder.setContext(securityContext);

        when(mockUserRepository.findByUsername("flName")).thenReturn(user);

        // Mock findOne method which is used in validatePasswordAgainstHistory
        when(mockUserRepository.findById(any())).thenReturn(Optional.of(user));

        // Mock passwordHistoryService
        when(mockPasswordHistoryService.isPasswordInHistory(any(), any())).thenReturn(false);
        when(mockPasswordHistoryService.getHistoryDepth()).thenReturn(5);
        when(mockPasswordHistoryService.getPasswordReuseDays()).thenReturn(90);

        when(mockAuthenticationManager.authenticate(any(Authentication.class))).thenReturn(null);

        // Run the test and expect exception
        Exception exception = assertThrows(Exception.class, () -> {
            userServiceUnderTest.changePassword("oldPassw0rd!!!@", "short");
        });

        // Verify the exception message - check for any message about password length
        String message = exception.getMessage();
        assertTrue(message.contains("Password") &&
                  (message.contains("characters") || message.contains("length")));
        // This test covers the branch: if (newPass.length() < minPasswordLength)
    }

    @Test
    void testChangePassword_WithoutSpecialCharacter() {
        // Setup
        // Mock the findBySectionAndField method for each password setting
        SystemSetting mixedCaseSetting = new SystemSetting();
        mixedCaseSetting.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("password", "require_mixed_case")).thenReturn(mixedCaseSetting);

        SystemSetting specialCharSetting = new SystemSetting();
        specialCharSetting.setValue("Y");
        when(mockSystemSettingService.findBySectionAndField("password", "require_special_character")).thenReturn(specialCharSetting);

        SystemSetting minLengthSetting = new SystemSetting();
        minLengthSetting.setValue("8");
        when(mockSystemSettingService.findBySectionAndField("password", "minimum_length")).thenReturn(minLengthSetting);

        // Configure UserRepository.findByUsername(...).
        final User user = Fake.getUserFakeID();

        // Mock the security context to return a username
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn("flName");
        SecurityContextHolder.setContext(securityContext);

        when(mockUserRepository.findByUsername("flName")).thenReturn(user);

        // Mock findOne method which is used in validatePasswordAgainstHistory
        when(mockUserRepository.findById(any())).thenReturn(Optional.of(user));

        // Mock passwordHistoryService
        when(mockPasswordHistoryService.isPasswordInHistory(any(), any())).thenReturn(false);
        when(mockPasswordHistoryService.getHistoryDepth()).thenReturn(5);
        when(mockPasswordHistoryService.getPasswordReuseDays()).thenReturn(90);

        when(mockAuthenticationManager.authenticate(any(Authentication.class))).thenReturn(null);

        // Run the test and expect exception
        Exception exception = assertThrows(Exception.class, () -> {
            userServiceUnderTest.changePassword("oldPassw0rd!!!@", "newpassword");
        });

        // Verify the exception message - check for any message about special characters
        String message = exception.getMessage();
        assertTrue(message.contains("Password") &&
                  (message.contains("special") || message.contains("character")));
        // This test covers the branch: if (requireSpecialCharacter && !matcher.find())
    }

    @Test
    void testGetCurrentCompany() {
        // Setup
        // Configure CompanyService.findByKey(...).
        final Company company = Fake.getCompany();
        Mockito.when(this.mockCompanyService.findByKey(ArgumentMatchers.any())).thenReturn(company);
        // Run the test
        final Company result = this.userServiceUnderTest.getCurrentCompany();

        // Verify the results
        Assertions.assertEquals(Fake.ID, result.getId());
    }

    @Test
    void testGetUserArn() {
        // Setup
        DescribeUserResult describeUserResult = new DescribeUserResult();
        com.amazonaws.services.quicksight.model.User user = new com.amazonaws.services.quicksight.model.User();
        user.setArn("test:data");
        describeUserResult.setUser(user);
        this.userServiceUnderTest.setUserAccessRegionName("us-east-1");
//        userServiceUnderTest.setAwsAccountId("id");
        Mockito.when(this.mockQuickSightAccess.getClient(ArgumentMatchers.anyString())).thenReturn(this.mockAmazonQuickSight);
        Mockito.when(this.mockAmazonQuickSight.describeUser(ArgumentMatchers.any(DescribeUserRequest.class))).thenReturn(describeUserResult);

        // Run the test
        final String result = this.userServiceUnderTest.getUserArn("<EMAIL>");

        // Verify the results
        Assertions.assertEquals("test:data", result);
    }

    @Test
    void testHasQuickSightAccess() {
        // Setup
        DescribeUserResult describeUserResult = new DescribeUserResult();
        com.amazonaws.services.quicksight.model.User user = new com.amazonaws.services.quicksight.model.User();
        user.setArn("test:data");
        describeUserResult.setUser(user);
        this.userServiceUnderTest.setUserAccessRegionName("us-east-1");
//        userServiceUnderTest.setAwsAccountId("id");
        Mockito.when(this.mockQuickSightAccess.getClient(ArgumentMatchers.anyString())).thenReturn(this.mockAmazonQuickSight);
        Mockito.when(this.mockAmazonQuickSight.describeUser(ArgumentMatchers.any(DescribeUserRequest.class))).thenReturn(describeUserResult);

        // Run the test
        final Boolean result = this.userServiceUnderTest.hasQuickSightAccess("<EMAIL>");

        // Verify the results
        Assertions.assertTrue(result);
    }

    @Test
    void testGetAttemptedQuickSightAccess() {
        // Setup

        // Run the test
        final Boolean result = this.userServiceUnderTest.getAttemptedQuickSightAccess("<EMAIL>");

        // Verify the results
        Assertions.assertNull(result);
    }

    @Test
    void testSetAttemptedQuickSightAccess() {
        // Setup

        // Run the test
        this.userServiceUnderTest.setAttemptedQuickSightAccess("<EMAIL>", true);

        // Verify the results
        Assertions.assertEquals(true, this.userServiceUnderTest.getAttemptedQuickSightAccess("<EMAIL>"));
    }

    @Test
    void testGetQuickSightUserAccessErrorMessage() {
        // Setup

        // Run the test
        final String result = this.userServiceUnderTest.getQuickSightUserAccessErrorMessage("<EMAIL>");

        // Verify the results
        Assertions.assertNull(result);
    }

    @Test
    void testSetUserArn() {
        // Setup

        // Run the test
        this.userServiceUnderTest.setUserArn("<EMAIL>", "arn");

        // Verify the results
        Assertions.assertEquals("arn", this.userServiceUnderTest.getUserArn("<EMAIL>"));
    }

    @Test
    void testGetClericalProductivityByUserIdBetween() {
        // Setup
        final java.sql.Date startDate = new Date(0L);
        final java.sql.Date endDate = new java.sql.Date(0L);

        String dateStart = DateUtil.getStringDate(startDate, Constants.DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), Constants.DF_YYYY_MM_DD_HH_MM);

        final ClericalProductivityReportDTO clericalProductivityReportDTO = new ClericalProductivityReportDTO();
        final User user = new User();
        user.setId(0L);
        user.setUsername("username");
        user.setPassword("password");
        user.setActive(false);
        user.setLastName("lastName");
        user.setFirstName("firstName");
        user.setMiddleName("middleName");
        user.setCredentials("credentials");
        user.setAuthCode("authCode");
        user.setAuthCodeExpirationTime(new Timestamp(0L));
        clericalProductivityReportDTO.setUser(user);
        clericalProductivityReportDTO.setPatientCreatedCount(0);
        clericalProductivityReportDTO.setPrescriptionCreatedCount(0);
        clericalProductivityReportDTO.setClaimCreatedCount(0);
        clericalProductivityReportDTO.setGeneralNotesCreated(0);
        clericalProductivityReportDTO.setClinicalNotesCreated(0);
        clericalProductivityReportDTO.setArNotesCreated(0);
        clericalProductivityReportDTO.setBillingNotesCreated(0);
        clericalProductivityReportDTO.setComplaintNotesCreated(0);
        clericalProductivityReportDTO.setRxSummaryNotesCreated(0);
        clericalProductivityReportDTO.setClaimCommentsNotesCreated(0);

        // Configure UserRepository.findByCompanyId(...).
        final User user1 = new User();
        user1.setId(0L);
        user1.setUsername("username");
        user1.setPassword("password");
        user1.setActive(false);
        user1.setLastName("lastName");
        user1.setFirstName("firstName");
        user1.setMiddleName("middleName");
        user1.setCredentials("credentials");
        user1.setAuthCode("authCode");
        user1.setAuthCodeExpirationTime(new Timestamp(0L));
        Mockito.when(this.mockCompanyService.findByKey(ArgumentMatchers.any())).thenReturn(Fake.getCompany());
        Mockito.when(this.mockUserRepository.findByCompanyIdAndActiveIsTrue(ArgumentMatchers.anyLong())).thenReturn(Collections.singletonList(user1));
        // Configure CompanyService.findByKey(...).


        Timestamp startDateRange = Timestamp.valueOf(ZonedDateTime.of(new java.sql.Date(0L).toLocalDate().atTime(0,0), ZoneId.systemDefault()).toLocalDateTime());
        Timestamp endDateRange = Timestamp.valueOf(ZonedDateTime.of(new java.sql.Date(0L).toLocalDate().atTime(23,59), ZoneId.systemDefault()).toLocalDateTime());
        Mockito.when(this.mockPatientRepository.getPatientCountByBranchAndCreatedByUserBetweenDate(0L, 0L, startDateRange, endDateRange)).thenReturn(0);
        Mockito.when(this.mockPrescriptionRepository.getPrescriptionCountByBranchAndCreatedByUserBetweenDate(0L, 1L, startDateRange, endDateRange)).thenReturn(0);
        Mockito.when(this.mockClaimRepository.getClaimCountByBranchAndCreatedByUserBetweenDate(0L, 0L, dateStart, dateEnd)).thenReturn(0);
        Mockito.when(this.mockNoteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(0L, 0L, startDateRange, endDateRange, "general")).thenReturn(0);
        Mockito.when(this.mockNoteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(0L, 0L, startDateRange, startDateRange, "clinical")).thenReturn(0);
        Mockito.when(this.mockNoteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(0L, 0L, startDateRange, startDateRange, "billing_ar")).thenReturn(0);
        Mockito.when(this.mockNoteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(0L, 0L, startDateRange, startDateRange, "billing")).thenReturn(0);
        Mockito.when(this.mockNoteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(0L, 0L, startDateRange, startDateRange, "complaint")).thenReturn(0);
        Mockito.when(this.mockNoteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(0L, 0L, startDateRange, startDateRange, "patient_summary")).thenReturn(0);
        Mockito.when(this.mockNoteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(0L, 0L, startDateRange, startDateRange, "claim_comments")).thenReturn(0);

        // this tests nothing...disabling...

        // Run the test
        //final List<ClericalProductivityReportDTO> result = userServiceUnderTest.getClericalProductivityByUserIdBetween(0L, ZonedDateTime.of(startDate.toLocalDate().atTime(0,0), ZoneId.systemDefault()), ZonedDateTime.of(endDate.toLocalDate().atTime(23, 59), ZoneId.systemDefault()), new ArrayList<Long>());

        // Verify the results
        //assertEquals(Collections.singletonList(clericalProductivityReportDTO), result);
    }

    @Test
    void testPopulateQuickSightDashboardEmbedUrlList() {
        // Setup
        DescribeUserResult describeUserResult = new DescribeUserResult();
        com.amazonaws.services.quicksight.model.User user = new com.amazonaws.services.quicksight.model.User();
        user.setArn("test:data");
        describeUserResult.setUser(user);
        this.userServiceUnderTest.setUserAccessRegionName("us-east-1");
//        userServiceUnderTest.setAwsAccountId("id");
        this.userServiceUnderTest.setAwsQuickSightDataAccessRegion("us-east-1");
        Mockito.when(this.mockQuickSightAccess.getClient(ArgumentMatchers.anyString())).thenReturn(this.mockAmazonQuickSight);
        Mockito.when(this.mockAmazonQuickSight.describeUser(ArgumentMatchers.any(DescribeUserRequest.class))).thenReturn(describeUserResult);
        ListDashboardsResult listDashboardsResult = new ListDashboardsResult();
        List<DashboardSummary> dashboardSummaryList = new ArrayList<>();
        DashboardSummary dashboardSummry = new DashboardSummary();
        dashboardSummry.setArn("arn:dashboard1");
        dashboardSummry.setDashboardId("dashboard1");
        dashboardSummry.setName("dashboard1");
        dashboardSummaryList.add(dashboardSummry);
        listDashboardsResult.setDashboardSummaryList(dashboardSummaryList);
        Mockito.when(this.mockAmazonQuickSight.listDashboards(ArgumentMatchers.any(ListDashboardsRequest.class))).thenReturn(listDashboardsResult);
        DescribeDashboardPermissionsResult describeDashboardPermissionsResult = new DescribeDashboardPermissionsResult();
        List<ResourcePermission> permissionList = new ArrayList<>();
        ResourcePermission resourcePermission = new ResourcePermission();
        List<String> actionList = new ArrayList<>();
        actionList.add("workHard");
        resourcePermission.setActions(actionList);
        resourcePermission.setPrincipal("test:data");
        permissionList.add(resourcePermission);
        describeDashboardPermissionsResult.setPermissions(permissionList);
        Mockito.when(this.mockAmazonQuickSight.describeDashboardPermissions(ArgumentMatchers.any(DescribeDashboardPermissionsRequest.class))).thenReturn(describeDashboardPermissionsResult);
        GetDashboardEmbedUrlResult getDashboardEmbedUrlResult = new GetDashboardEmbedUrlResult();
        getDashboardEmbedUrlResult.setEmbedUrl("embeddedurl");
        Mockito.when(this.mockAmazonQuickSight.getDashboardEmbedUrl(ArgumentMatchers.any(GetDashboardEmbedUrlRequest.class))).thenReturn(getDashboardEmbedUrlResult);

        // Configure UserRepository.findByUsername(...).
        final User user2 = Fake.getUserFakeID();

        // Mock the security context to return a username
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn("flName");
        SecurityContextHolder.setContext(securityContext);

        Mockito.when(this.mockUserRepository.findByUsername("flName")).thenReturn(user2);

        // Run the test
        final List<String> result = this.userServiceUnderTest.populateQuickSightDashboardEmbedUrlList();

        // Verify the results
        Assertions.assertEquals(Collections.singletonList("dashboard1embeddedurl"), result);
    }

    @Test
    void testTrackWhenUserGainsQuickSightAccessForAccounting() {
        // Setup
        final User user = Fake.getUserFakeID();
        user.setIsQuicksightUser(false);
        // Run the test
        this.userServiceUnderTest.trackWhenUserGainsQuickSightAccessForAccounting(user);

        // Verify the results
        Assertions.assertEquals(Fake.ID, ((User) TableObjectContainer.getObjectFromList(User.class.getName(), 0)).getId());
        Assertions.assertEquals(true, ((User) TableObjectContainer.getObjectFromList(User.class.getName(), 0)).getIsQuicksightUser());
    }

    @Test
    void testGetActiveUsersForCompanyByCompanyId() {

        User superAdminActive = Fake.getUserFakeID();
        superAdminActive.setId(1L);
        superAdminActive.setActive(true);
        superAdminActive.setIsSuperAdmin(true);

        User userNotActive = Fake.getUserFakeID2();
        userNotActive.setActive(false);

        User userActive = Fake.getUser(3L);
        userActive.setActive(true);

        List<User> userCollection = new ArrayList<>();
        userCollection.add(superAdminActive);
        userCollection.add(userActive);
        userCollection.add(userNotActive);
        Company company = Fake.getCompany();
        company.setUsers(userCollection);

        Mockito.when(this.mockCompanyService.findOne(5L)).thenReturn(company);
        Mockito.when(this.mockUserRepository.findByCompanyId(5L)).thenReturn(userCollection);
        List<User> userList = this.userServiceUnderTest.getActiveUsersForCompanyByCompanyId(5L);
        Assertions.assertEquals(2, userList.size());
    }

    @Test
    void testGetActiveUsersForCompanyByCompanyIdNoSuperAdmin() {

        User superAdminActive = Fake.getUserFakeID();
        superAdminActive.setId(1L);
        superAdminActive.setActive(true);
        superAdminActive.setIsSuperAdmin(true);

        User userNotActive = Fake.getUserFakeID2();
        userNotActive.setActive(false);

        User userActive = Fake.getUser(3L);
        userActive.setActive(true);

        List<User> userCollection = new ArrayList<>();
        userCollection.add(superAdminActive);
        userCollection.add(userActive);
        userCollection.add(userNotActive);
        Company company = Fake.getCompany();
        company.setUsers(userCollection);

        Mockito.when(this.mockCompanyService.findOne(5L)).thenReturn(company);
        Mockito.when(this.mockUserRepository.findByCompanyId(5L)).thenReturn(userCollection.stream().collect(Collectors.toList()));
        List<User> userList = this.userServiceUnderTest.getActiveUsersForCompanyByCompanyIdNoSuperAdmin(5L);
        Assertions.assertEquals(1, userList.size());
    }

    @Test
    void testGetActiveUsersForCompanyByCompanyIdNoSuperAdminSearchByName() {

        User superAdminActive = Fake.getUserFakeID();
        superAdminActive.setId(1L);
        superAdminActive.setActive(true);
        superAdminActive.setIsSuperAdmin(true);

        User userNotActive = Fake.getUserFakeID2();
        userNotActive.setActive(false);

        User userActive = Fake.getUser(3L);
        userActive.setActive(true);
        userActive.setFirstName("PickMe");
        userActive.setLastName("MeFirst");
        userActive.setUsername("IAmTheBest");

        List<User> userCollection = new ArrayList<>();
        userCollection.add(superAdminActive);
        userCollection.add(userActive);
        userCollection.add(userNotActive);
        Company company = Fake.getCompany();
        company.setUsers(userCollection);

        Mockito.when(this.mockCompanyService.findOne(5L)).thenReturn(company);
        Mockito.when(this.mockUserRepository.findByCompanyId(5L)).thenReturn(userCollection.stream().collect(Collectors.toList()));
        List<User> userList = this.userServiceUnderTest.getActiveUsersForCompanyByCompanyIdNoSuperAdminSearchByName(5L, "ickM");
        Assertions.assertEquals(1, userList.size());
        userList = this.userServiceUnderTest.getActiveUsersForCompanyByCompanyIdNoSuperAdminSearchByName(5L, "MeF");
        Assertions.assertEquals(1, userList.size());
        userList = this.userServiceUnderTest.getActiveUsersForCompanyByCompanyIdNoSuperAdminSearchByName(5L, "AmT");
        Assertions.assertEquals(1, userList.size());
        userList = this.userServiceUnderTest.getActiveUsersForCompanyByCompanyIdNoSuperAdminSearchByName(5L, "pickM");
        Assertions.assertEquals(1, userList.size());
    }

    @Test
    void testGetUsersForCompanyByCompanyId() {

        User superAdminActive = Fake.getUserFakeID();
        superAdminActive.setId(1L);
        superAdminActive.setActive(true);
        superAdminActive.setIsSuperAdmin(true);

        User userNotActive = Fake.getUserFakeID2();
        userNotActive.setActive(false);

        User userActive = Fake.getUser(3L);
        userActive.setActive(true);

        List<User> userCollection = new ArrayList<>();
        userCollection.add(superAdminActive);
        userCollection.add(userActive);
        userCollection.add(userNotActive);
        Company company = Fake.getCompany();
        company.setUsers(userCollection);

        Mockito.when(this.mockCompanyService.findOne(5L)).thenReturn(company);
        Mockito.when(this.mockUserRepository.findByCompanyId(5L)).thenReturn(userCollection);
        List<User> userList = this.userServiceUnderTest.getUsersForCompanyByCompanyId(5L);
        Assertions.assertEquals(3, userList.size());
    }

    @Test
    void testLoadForeignKeys() {
        // Setup
        final User o = Fake.getUserFakeID();

        // Run the test
        this.userServiceUnderTest.loadForeignKeys(o);

        // Verify the results
        Assertions.assertEquals(Fake.ID, o.getId());
    }
}
