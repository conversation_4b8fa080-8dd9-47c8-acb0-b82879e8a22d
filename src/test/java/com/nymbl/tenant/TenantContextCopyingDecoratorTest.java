package com.nymbl.tenant;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the TenantContextCopyingDecorator class.
 * These tests verify that both tenant context and security context are properly propagated to background threads,
 * and that thread isolation, context restoration, and security boundaries are maintained.
 */
class TenantContextCopyingDecoratorTest {

    @Test
    void testTenantContextPropagation() throws InterruptedException {
        // Set up tenant context in the main thread
        String expectedTenantId = "test-tenant";
        String expectedTimezoneId = "America/New_York";
        TenantContext.setCurrentTenant(expectedTenantId);
        TenantContext.setUserTimezoneId(expectedTimezoneId);

        // Create a latch to wait for the background thread to complete
        CountDownLatch latch = new CountDownLatch(1);

        // Variables to capture values in the background thread
        final String[] capturedTenantId = new String[1];
        final String[] capturedTimezoneId = new String[1];

        // Create a task decorator
        TenantContextCopyingDecorator decorator = new TenantContextCopyingDecorator();

        // Create a runnable that will be executed in a background thread
        Runnable originalRunnable = () -> {
            capturedTenantId[0] = TenantContext.getCurrentTenant();
            capturedTimezoneId[0] = TenantContext.getUserTimezoneId();
            latch.countDown();
        };

        // Decorate the runnable
        Runnable decoratedRunnable = decorator.decorate(originalRunnable);

        // Execute the decorated runnable in a background thread
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(decoratedRunnable);

        // Wait for the background thread to complete
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Background thread did not complete in time");

        // Verify that the tenant context was properly propagated
        assertEquals(expectedTenantId, capturedTenantId[0], "Tenant ID was not properly propagated");
        assertEquals(expectedTimezoneId, capturedTimezoneId[0], "Timezone ID was not properly propagated");

        // Clean up
        executor.shutdown();
        TenantContext.clear();
    }

    @Test
    void testSecurityContextPropagation() throws InterruptedException {
        // Set up security context in the main thread
        String expectedUsername = "test-user";
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                expectedUsername,
                "password",
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
        );
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        securityContext.setAuthentication(authentication);
        SecurityContextHolder.setContext(securityContext);

        // Create a latch to wait for the background thread to complete
        CountDownLatch latch = new CountDownLatch(1);

        // Variables to capture values in the background thread
        final String[] capturedUsername = new String[1];

        // Create a task decorator
        TenantContextCopyingDecorator decorator = new TenantContextCopyingDecorator();

        // Create a runnable that will be executed in a background thread
        Runnable originalRunnable = () -> {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            capturedUsername[0] = auth != null ? (String) auth.getPrincipal() : null;
            latch.countDown();
        };

        // Decorate the runnable
        Runnable decoratedRunnable = decorator.decorate(originalRunnable);

        // Execute the decorated runnable in a background thread
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(decoratedRunnable);

        // Wait for the background thread to complete
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Background thread did not complete in time");

        // Verify that the security context was properly propagated
        assertEquals(expectedUsername, capturedUsername[0], "Username was not properly propagated");

        // Clean up
        executor.shutdown();
        SecurityContextHolder.clearContext();
    }

    /**
     * Test that tenant context is properly isolated between threads.
     * Each thread should have its own tenant context that doesn't affect other threads.
     */
    @Test
    void testThreadIsolation() throws InterruptedException {
        // Set up tenant context in the main thread
        String mainThreadTenant = "main-tenant";
        TenantContext.setCurrentTenant(mainThreadTenant);

        // Create a latch to wait for all background threads to complete
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);

        // Create a task decorator
        TenantContextCopyingDecorator decorator = new TenantContextCopyingDecorator();

        // Create an executor service with multiple threads
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // Create and execute multiple tasks with different tenant contexts
        for (int i = 0; i < threadCount; i++) {
            final String expectedTenant = "tenant-" + i;
            final int threadIndex = i;

            // Create a runnable that will set its own tenant context
            Runnable originalRunnable = () -> {
                try {
                    // Set a unique tenant context for this thread
                    TenantContext.setCurrentTenant(expectedTenant);

                    // Sleep to allow other threads to run concurrently
                    Thread.sleep(100);

                    // Verify that the tenant context is still what we set
                    String currentTenant = TenantContext.getCurrentTenant();
                    assertEquals(expectedTenant, currentTenant,
                            "Thread " + threadIndex + " tenant context was modified by another thread");
                } catch (Exception e) {
                    fail("Exception in thread " + threadIndex + ": " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            };

            // Decorate and execute the runnable
            Runnable decoratedRunnable = decorator.decorate(originalRunnable);
            executor.execute(decoratedRunnable);
        }

        // Wait for all threads to complete
        assertTrue(latch.await(10, TimeUnit.SECONDS), "Not all threads completed in time");

        // Verify that the main thread's tenant context is unchanged
        assertEquals(mainThreadTenant, TenantContext.getCurrentTenant(),
                "Main thread tenant context was modified");

        // Clean up
        executor.shutdown();
        TenantContext.clear();
    }

    /**
     * Test that tenant context is properly restored after async processing.
     * This simulates what happens in the BulkClaimJobService when processing claims asynchronously.
     */
    @Test
    void testContextRestoration() throws InterruptedException {
        // Set up tenant context in the main thread
        String originalTenant = "original-tenant";
        TenantContext.setCurrentTenant(originalTenant);

        // Create a latch to wait for the background thread to complete
        CountDownLatch latch = new CountDownLatch(1);

        // Create a task decorator
        TenantContextCopyingDecorator decorator = new TenantContextCopyingDecorator();

        // Create a runnable that will modify the tenant context and then restore it
        Runnable originalRunnable = () -> {
            try {
                // Verify the tenant context was propagated correctly
                assertEquals(originalTenant, TenantContext.getCurrentTenant(),
                        "Tenant context was not propagated correctly");

                // Change the tenant context
                String newTenant = "new-tenant";
                TenantContext.setCurrentTenant(newTenant);

                // Verify the change took effect
                assertEquals(newTenant, TenantContext.getCurrentTenant(),
                        "Failed to change tenant context");
            } finally {
                latch.countDown();
            }
        };

        // Decorate and execute the runnable
        Runnable decoratedRunnable = decorator.decorate(originalRunnable);
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(decoratedRunnable);

        // Wait for the background thread to complete
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Background thread did not complete in time");

        // Verify that the main thread's tenant context is unchanged
        assertEquals(originalTenant, TenantContext.getCurrentTenant(),
                "Main thread tenant context was modified");

        // Clean up
        executor.shutdown();
        TenantContext.clear();
    }

    /**
     * Test that tenant context is properly handled in error scenarios.
     * The tenant context should be cleared even if the task throws an exception.
     */
    @Test
    void testContextHandlingInErrorScenarios() throws InterruptedException {
        // Set up tenant context in the main thread
        String originalTenant = "original-tenant";
        TenantContext.setCurrentTenant(originalTenant);

        // Create a latch to wait for the background thread to complete
        CountDownLatch latch = new CountDownLatch(1);

        // Create a task decorator
        TenantContextCopyingDecorator decorator = new TenantContextCopyingDecorator();

        // Create a runnable that will throw an exception
        Runnable originalRunnable = () -> {
            try {
                // Verify the tenant context was propagated correctly
                assertEquals(originalTenant, TenantContext.getCurrentTenant(),
                        "Tenant context was not propagated correctly");

                // Throw an exception
                throw new RuntimeException("Test exception");
            } finally {
                // This should still execute
                latch.countDown();
            }
        };

        // Decorate and execute the runnable
        Runnable decoratedRunnable = decorator.decorate(originalRunnable);
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(decoratedRunnable);

        // Wait for the background thread to complete
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Background thread did not complete in time");

        // Verify that the main thread's tenant context is unchanged
        assertEquals(originalTenant, TenantContext.getCurrentTenant(),
                "Main thread tenant context was modified");

        // Clean up
        executor.shutdown();
        TenantContext.clear();
    }

    /**
     * Test that security boundaries are properly enforced between different tenant contexts.
     * This simulates what happens when multiple tenants are processed concurrently.
     */
    @Test
    void testSecurityBoundaries() throws InterruptedException {
        // Set up security context in the main thread for tenant A
        String tenantA = "tenant-a";
        String userA = "user-a";
        TenantContext.setCurrentTenant(tenantA);

        Authentication authA = new UsernamePasswordAuthenticationToken(
                userA,
                "password",
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
        );
        SecurityContext securityContextA = SecurityContextHolder.createEmptyContext();
        securityContextA.setAuthentication(authA);
        SecurityContextHolder.setContext(securityContextA);

        // Create a latch to wait for the background thread to complete
        CountDownLatch latch = new CountDownLatch(1);

        // Variables to capture values in the background thread
        final String[] capturedTenant = new String[1];
        final String[] capturedUsername = new String[1];

        // Create a task decorator
        TenantContextCopyingDecorator decorator = new TenantContextCopyingDecorator();

        // Create a runnable that will execute with tenant B context
        Runnable originalRunnable = () -> {
            try {
                // Change to tenant B
                String tenantB = "tenant-b";
                String userB = "user-b";
                TenantContext.setCurrentTenant(tenantB);

                Authentication authB = new UsernamePasswordAuthenticationToken(
                        userB,
                        "password",
                        Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
                );
                SecurityContext securityContextB = SecurityContextHolder.createEmptyContext();
                securityContextB.setAuthentication(authB);
                SecurityContextHolder.setContext(securityContextB);

                // Capture the current tenant and user
                capturedTenant[0] = TenantContext.getCurrentTenant();
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                capturedUsername[0] = auth != null ? (String) auth.getPrincipal() : null;
            } finally {
                latch.countDown();
            }
        };

        // Decorate and execute the runnable
        Runnable decoratedRunnable = decorator.decorate(originalRunnable);
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(decoratedRunnable);

        // Wait for the background thread to complete
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Background thread did not complete in time");

        // Verify that the background thread had tenant B context
        assertEquals("tenant-b", capturedTenant[0], "Background thread had wrong tenant");
        assertEquals("user-b", capturedUsername[0], "Background thread had wrong user");

        // Verify that the main thread still has tenant A context
        assertEquals(tenantA, TenantContext.getCurrentTenant(), "Main thread tenant was modified");
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = auth != null ? (String) auth.getPrincipal() : null;
        assertEquals(userA, currentUsername, "Main thread user was modified");

        // Clean up
        executor.shutdown();
        TenantContext.clear();
        SecurityContextHolder.clearContext();
    }
}
