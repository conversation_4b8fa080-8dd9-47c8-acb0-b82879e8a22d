package com.nymbl.tenant.dto;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BulkClaimRequestTest {

    @Test
    void testBulkClaimRequest() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        
        // Execute
        BulkClaimRequest request = new BulkClaimRequest();
        request.setClaimIds(claimIds);
        
        // Verify
        assertEquals(claimIds, request.getClaimIds());
    }
    
    @Test
    void testBulkClaimRequest_DefaultValues() {
        // Execute
        BulkClaimRequest request = new BulkClaimRequest();
        
        // Verify
        assertNull(request.getClaimIds());
    }
    
    @Test
    void testBulkClaimRequest_Builder() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        
        // Execute
        BulkClaimRequest request = BulkClaimRequest.builder()
            .claimIds(claimIds)
            .build();
        
        // Verify
        assertEquals(claimIds, request.getClaimIds());
    }
    
    @Test
    void testBulkClaimRequest_AllArgsConstructor() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        
        // Execute
        BulkClaimRequest request = new BulkClaimRequest(claimIds);
        
        // Verify
        assertEquals(claimIds, request.getClaimIds());
    }
    
    @Test
    void testBulkClaimRequest_ToString() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        
        // Execute
        BulkClaimRequest request = new BulkClaimRequest(claimIds);
        
        // Verify
        String toString = request.toString();
        assertTrue(toString.contains("claimIds=" + claimIds));
    }
    
    @Test
    void testBulkClaimRequest_EqualsAndHashCode() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        
        // Execute
        BulkClaimRequest request1 = new BulkClaimRequest(claimIds);
        BulkClaimRequest request2 = new BulkClaimRequest(claimIds);
        BulkClaimRequest request3 = new BulkClaimRequest(Arrays.asList(3L, 4L));
        
        // Verify
        assertEquals(request1, request2);
        assertEquals(request1.hashCode(), request2.hashCode());
        assertNotEquals(request1, request3);
        assertNotEquals(request1.hashCode(), request3.hashCode());
    }
}
