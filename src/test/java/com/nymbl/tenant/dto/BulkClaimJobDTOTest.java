package com.nymbl.tenant.dto;

import com.nymbl.tenant.model.BulkClaimJob;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the BulkClaimJobDTO class.
 */
class BulkClaimJobDTOTest {

    @Test
    void testBulkClaimJobDTO_DefaultConstructor() {
        // Execute
        BulkClaimJobDTO dto = new BulkClaimJobDTO();

        // Verify
        assertNull(dto.getJobId());
        assertNull(dto.getStatus());
        assertNull(dto.getTotalClaims());
        assertNull(dto.getProcessedClaims());
        assertNull(dto.getSuccessfulClaims());
        assertNull(dto.getFailedClaims());
        assertNull(dto.getCurrentPayer());
        assertNull(dto.getErrorMessage());
        assertNull(dto.getCompleted());
    }

    @Test
    void testBulkClaimJobDTO_FromEntity() {
        // Setup
        BulkClaimJob entity = new BulkClaimJob();
        entity.setJobId("test-job-id");
        entity.setStatus("PROCESSING");
        entity.setTotalClaims(10);
        entity.setProcessedClaims(5);
        entity.setSuccessfulClaims(4);
        entity.setFailedClaims(1);
        entity.setCurrentPayer("Test Payer");
        entity.setErrorMessage("Test error");
        entity.setCompleted(true);
        entity.setStartTime(ZonedDateTime.now().minusMinutes(5));
        entity.setEndTime(ZonedDateTime.now());

        // Execute
        BulkClaimJobDTO dto = BulkClaimJobDTO.fromEntity(entity);

        // Verify
        assertEquals(entity.getJobId(), dto.getJobId());
        assertEquals(entity.getStatus(), dto.getStatus());
        assertEquals(entity.getTotalClaims(), dto.getTotalClaims());
        assertEquals(entity.getProcessedClaims(), dto.getProcessedClaims());
        assertEquals(entity.getSuccessfulClaims(), dto.getSuccessfulClaims());
        assertEquals(entity.getFailedClaims(), dto.getFailedClaims());
        assertEquals(entity.getCurrentPayer(), dto.getCurrentPayer());
        assertEquals(entity.getErrorMessage(), dto.getErrorMessage());
        assertEquals(entity.getCompleted(), dto.getCompleted());
        assertNotNull(dto.getStartTime());
        assertNotNull(dto.getEndTime());
    }

    @Test
    void testBulkClaimJobDTO_FromEntity_NullEntity() {
        // Execute & Verify
        assertNull(BulkClaimJobDTO.fromEntity(null));
    }

    @Test
    void testBulkClaimJobDTO_Builder() {
        // Setup
        String jobId = "test-job-id";
        String status = "PROCESSING";
        Integer totalClaims = 10;
        Integer processedClaims = 5;
        Integer successfulClaims = 4;
        Integer failedClaims = 1;
        String currentPayer = "Test Payer";
        String errorMessage = "Test error";
        Boolean completed = true;
        ZonedDateTime startTime = ZonedDateTime.now().minusMinutes(5);
        ZonedDateTime endTime = ZonedDateTime.now();

        // Execute
        BulkClaimJobDTO dto = BulkClaimJobDTO.builder()
            .jobId(jobId)
            .status(status)
            .totalClaims(totalClaims)
            .processedClaims(processedClaims)
            .successfulClaims(successfulClaims)
            .failedClaims(failedClaims)
            .currentPayer(currentPayer)
            .errorMessage(errorMessage)
            .completed(completed)
            .startTime(startTime)
            .endTime(endTime)
            .build();

        // Verify
        assertEquals(jobId, dto.getJobId());
        assertEquals(status, dto.getStatus());
        assertEquals(totalClaims, dto.getTotalClaims());
        assertEquals(processedClaims, dto.getProcessedClaims());
        assertEquals(successfulClaims, dto.getSuccessfulClaims());
        assertEquals(failedClaims, dto.getFailedClaims());
        assertEquals(currentPayer, dto.getCurrentPayer());
        assertEquals(errorMessage, dto.getErrorMessage());
        assertEquals(completed, dto.getCompleted());
        assertEquals(startTime, dto.getStartTime());
        assertEquals(endTime, dto.getEndTime());
    }

    @Test
    void testBulkClaimJobDTO_ToString() {
        // Setup
        BulkClaimJobDTO dto = BulkClaimJobDTO.builder()
            .jobId("test-job-id")
            .status("PROCESSING")
            .totalClaims(10)
            .build();

        // Execute
        String toString = dto.toString();

        // Verify
        assertTrue(toString.contains("jobId=test-job-id"));
        assertTrue(toString.contains("status=PROCESSING"));
        assertTrue(toString.contains("totalClaims=10"));
    }

    @Test
    void testBulkClaimJobDTO_EqualsAndHashCode() {
        // Setup
        BulkClaimJobDTO dto1 = BulkClaimJobDTO.builder()
            .jobId("test-job-id")
            .status("PROCESSING")
            .build();

        BulkClaimJobDTO dto2 = BulkClaimJobDTO.builder()
            .jobId("test-job-id")
            .status("PROCESSING")
            .build();

        BulkClaimJobDTO dto3 = BulkClaimJobDTO.builder()
            .jobId("different-job-id")
            .status("PROCESSING")
            .build();

        // Verify
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }
}
