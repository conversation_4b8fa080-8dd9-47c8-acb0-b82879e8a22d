package com.nymbl.tenant.dto;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ClaimFilesRequestTest {

    @Test
    void testClaimFilesRequest() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String submissionType = "bulk";

        // Execute
        ClaimFilesRequest request = new ClaimFilesRequest();
        request.setClaimIds(claimIds);
        request.setBillingBranchId(billingBranchId);
        request.setSubmissionType(submissionType);

        // Verify
        assertEquals(claimIds, request.getClaimIds());
        assertEquals(billingBranchId, request.getBillingBranchId());
        assertEquals(submissionType, request.getSubmissionType());
    }

    @Test
    void testClaimFilesRequest_DefaultValues() {
        // Execute
        ClaimFilesRequest request = new ClaimFilesRequest();

        // Verify
        assertNull(request.getClaimIds());
        assertNull(request.getBillingBranchId());
        assertNull(request.getSubmissionType());
    }

    @Test
    void testClaimFilesRequest_Builder() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String submissionType = "bulk";

        // Execute
        ClaimFilesRequest request = ClaimFilesRequest.builder()
            .claimIds(claimIds)
            .billingBranchId(billingBranchId)
            .submissionType(submissionType)
            .build();

        // Verify
        assertEquals(claimIds, request.getClaimIds());
        assertEquals(billingBranchId, request.getBillingBranchId());
        assertEquals(submissionType, request.getSubmissionType());
    }

    @Test
    void testClaimFilesRequest_AllArgsConstructor() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String submissionType = "bulk";

        // Execute
        ClaimFilesRequest request = new ClaimFilesRequest(claimIds, billingBranchId, submissionType);

        // Verify
        assertEquals(claimIds, request.getClaimIds());
        assertEquals(billingBranchId, request.getBillingBranchId());
        assertEquals(submissionType, request.getSubmissionType());
    }

    @Test
    void testClaimFilesRequest_ToString() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String submissionType = "bulk";

        // Execute
        ClaimFilesRequest request = new ClaimFilesRequest(claimIds, billingBranchId, submissionType);

        // Verify
        String toString = request.toString();
        // The toString method returns "ClaimFilesRequest{claimIds=[1, 2], billingBranchId=1, submissionType='bulk'}"
        // Check that it contains the expected values in some form
        assertTrue(toString.contains("claimIds="));
        assertTrue(toString.contains("1"));
        assertTrue(toString.contains("2"));
        assertTrue(toString.contains("billingBranchId="));
        assertTrue(toString.contains(billingBranchId.toString()));
        assertTrue(toString.contains("submissionType="));
        assertTrue(toString.contains(submissionType));
    }

    @Test
    void testClaimFilesRequest_EqualsAndHashCode() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String submissionType = "bulk";

        // Execute
        ClaimFilesRequest request1 = new ClaimFilesRequest(claimIds, billingBranchId, submissionType);
        ClaimFilesRequest request2 = new ClaimFilesRequest(claimIds, billingBranchId, submissionType);
        ClaimFilesRequest request3 = new ClaimFilesRequest(Arrays.asList(3L, 4L), billingBranchId, submissionType);

        // Verify
        assertEquals(request1, request2);
        assertEquals(request1.hashCode(), request2.hashCode());
        assertNotEquals(request1, request3);
        assertNotEquals(request1.hashCode(), request3.hashCode());
    }
}
