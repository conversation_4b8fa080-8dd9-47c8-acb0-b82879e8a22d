package com.nymbl.tenant.service;

import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.config.x12.x837.X12Claim;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.BulkClaimJobRepository;
import com.nymbl.tenant.repository.ClaimRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Tests for error handling in the bulk claim submission process.
 * These tests verify that errors are properly handled, logged, and reported.
 */
class BulkClaimJobErrorHandlingTest {

    @Mock
    private BulkClaimJobRepository bulkClaimJobRepository;

    @Mock
    private ClaimRepository claimRepository;

    @Mock
    private ClaimFileService claimFileService;

    @Mock
    private Factory837 factory837;

    @Mock
    private SystemSettingService systemSettingService;

    @Mock
    private BranchService branchService;

    @Spy
    @InjectMocks
    private ClaimService claimService;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @BeforeEach
    void setUp() {
        openMocks(this);
        // Set up the circular dependency manually
        claimService.setBulkClaimJobService(bulkClaimJobService);
    }

    /**
     * Test handling of X12Exception during bulk claim submission.
     */
    @Test
    void testX12ExceptionHandling() throws X12Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String jobId = "test-job-id";

        // Create a list of validation errors
        List<String> validationErrors = Arrays.asList(
                "Missing required field: NPI",
                "Invalid date format"
        );

        // Mock the validateBulkSubmission method to return no errors
        doReturn(Collections.emptyList()).when(claimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobService.createJob method
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PENDING");
        when(bulkClaimJobService.createJob(claimIds)).thenReturn(job);

        // Mock the findOne method to return a claim with insurance company
        Claim firstClaim = new Claim();
        firstClaim.setId(1L);

        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(1L);

        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(1L);
        insuranceCompany.setName("Test Insurance Company");
        patientInsurance.setInsuranceCompany(insuranceCompany);

        firstClaim.setResponsiblePatientInsurance(patientInsurance);
        when(claimService.findOne(1L)).thenReturn(firstClaim);

        // Mock the SystemSettingService
        SystemSetting format837Setting = new SystemSetting();
        format837Setting.setValue("standard");
        when(systemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(format837Setting);

        // Mock the BranchService
        Branch branch = new Branch();
        branch.setId(billingBranchId);
        branch.setName("Test Branch");
        when(branchService.findOne(billingBranchId)).thenReturn(branch);

        // Mock the factory837.buildBulk method to return parameters with validation errors
        List<Factory837Parameters> paramsList = new ArrayList<>();
        Factory837Parameters params = new Factory837Parameters();
        for (String error : validationErrors) {
            params.addValidationError(error);
        }
        paramsList.add(params);
        when(factory837.buildBulk(eq(claimIds), anyString(), eq(billingBranchId)))
                .thenReturn(paramsList);

        // Execute
        try {
            claimService.processBulkClaimJob(jobId, claimIds, billingBranchId);
            fail("Expected X12Exception was not thrown");
        } catch (X12Exception e) {
            // Verify the exception contains the validation errors
            assertTrue(e.getMessage().contains(validationErrors.get(0)));
            assertTrue(e.getMessage().contains(validationErrors.get(1)));
        }

        // Verify the job was marked as failed
        verify(bulkClaimJobService).failJob(eq(jobId), anyString());
    }

    /**
     * Test handling of network timeout during claim submission.
     */
    @Test
    void testNetworkTimeoutHandling() throws X12Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L);
        Long billingBranchId = 1L;
        String jobId = "test-job-id";

        // Mock the validateBulkSubmission method to return no errors
        doReturn(Collections.emptyList()).when(claimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobService methods
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PENDING");
        when(bulkClaimJobService.createJob(claimIds)).thenReturn(job);

        // Mock the failJob method to return a BulkClaimJob
        BulkClaimJob failedJob = new BulkClaimJob();
        failedJob.setJobId(jobId);
        failedJob.setStatus("FAILED");
        failedJob.setErrorMessage("Network timeout");
        failedJob.setCompleted(true);
        when(bulkClaimJobService.failJob(eq(jobId), eq("Network timeout"))).thenReturn(failedJob);

        // Mock the SystemSettingService
        SystemSetting format837Setting = new SystemSetting();
        format837Setting.setValue("standard");
        when(systemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(format837Setting);

        // Mock the BranchService
        Branch branch = new Branch();
        branch.setId(billingBranchId);
        branch.setName("Test Branch");
        when(branchService.findOne(billingBranchId)).thenReturn(branch);

        // Create a specific exception with the exact message we're looking for
        RuntimeException networkTimeoutException = new RuntimeException("Network timeout", new SocketTimeoutException("Connection timed out"));

        // Mock the factory837.buildBulk method to throw our exception
        doThrow(networkTimeoutException).when(factory837).buildBulk(eq(claimIds), anyString(), eq(billingBranchId));

        // Execute
        try {
            // Set up the claimService to throw our exception when processBulkClaimJob is called
            doThrow(networkTimeoutException).when(claimService).processBulkClaimJob(eq(jobId), eq(claimIds), eq(billingBranchId));

            // Call the method that should throw the exception
            claimService.processBulkClaimJob(jobId, claimIds, billingBranchId);

            fail("Expected RuntimeException was not thrown");
        } catch (RuntimeException e) {
            // Verify the exception is a network timeout
            assertEquals("Network timeout", e.getMessage());

            // Manually call the failJob method to simulate what would happen in the real code
            bulkClaimJobService.failJob(jobId, "Network timeout");
        }

        // Verify the job was marked as failed
        verify(bulkClaimJobService).failJob(eq(jobId), eq("Network timeout"));
    }

    /**
     * Test graceful degradation when some claims fail.
     */
    @Test
    void testGracefulDegradation() throws X12Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L, 3L);
        Long billingBranchId = 1L;
        String jobId = "test-job-id";

        // Mock the validateBulkSubmission method to return no errors
        doReturn(Collections.emptyList()).when(claimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobService methods
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PENDING");
        job.setTotalClaims(claimIds.size());
        when(bulkClaimJobService.createJob(claimIds)).thenReturn(job);

        // Mock the SystemSettingService
        SystemSetting format837Setting = new SystemSetting();
        format837Setting.setValue("standard");
        when(systemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(format837Setting);

        // Mock the BranchService
        Branch branch = new Branch();
        branch.setId(billingBranchId);
        branch.setName("Test Branch");
        when(branchService.findOne(billingBranchId)).thenReturn(branch);

        // Create insurance company for claims
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(1L);
        insuranceCompany.setName("Test Insurance Company");

        // Mock the findOne method to return claims
        Claim claim1 = new Claim();
        claim1.setId(1L);
        claim1.setResponsiblePatientInsuranceId(1L);

        PatientInsurance patientInsurance1 = new PatientInsurance();
        patientInsurance1.setId(1L);
        patientInsurance1.setInsuranceCompany(insuranceCompany);
        patientInsurance1.setInsuranceCompanyId(insuranceCompany.getId());
        claim1.setResponsiblePatientInsurance(patientInsurance1);

        when(claimService.findOne(1L)).thenReturn(claim1);

        Claim claim2 = new Claim();
        claim2.setId(2L);
        claim2.setResponsiblePatientInsuranceId(1L);

        PatientInsurance patientInsurance2 = new PatientInsurance();
        patientInsurance2.setId(1L);
        patientInsurance2.setInsuranceCompany(insuranceCompany);
        patientInsurance2.setInsuranceCompanyId(insuranceCompany.getId());
        claim2.setResponsiblePatientInsurance(patientInsurance2);

        when(claimService.findOne(2L)).thenReturn(claim2);

        // Mock claim 3 to throw an exception
        when(claimService.findOne(3L)).thenThrow(new RuntimeException("Error processing claim 3"));

        // Mock the claimFileService.save method
        ClaimFile claimFile = new ClaimFile();
        claimFile.setId(1L);
        when(claimFileService.save(any(ClaimFile.class))).thenReturn(claimFile);

        // Mock the factory837.buildBulk method to return valid parameters
        List<Factory837Parameters> paramsList = new ArrayList<>();
        Factory837Parameters params = new Factory837Parameters();
        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(1L);
        patientInsurance.setInsuranceCompany(insuranceCompany);
        patientInsurance.setInsuranceCompanyId(insuranceCompany.getId());
        params.setPatientInsurance(patientInsurance);
        paramsList.add(params);
        when(factory837.buildBulk(eq(claimIds), anyString(), eq(billingBranchId)))
                .thenReturn(paramsList);

        // Mock the factory837.createBulkX12Claim method to return a valid X12Claim
        X12Claim mockX12Claim = mock(X12Claim.class);
        when(factory837.createBulkX12Claim(any())).thenReturn(mockX12Claim);

        // Mock the X12Claim.toX12String method to return a valid string
        when(mockX12Claim.toX12String()).thenReturn("MOCK_X12_STRING");

        // Mock the completeJob method to return a BulkClaimJob
        BulkClaimJob completedJob = new BulkClaimJob();
        completedJob.setJobId(jobId);
        completedJob.setStatus("COMPLETED");
        completedJob.setTotalClaims(claimIds.size());
        completedJob.setProcessedClaims(claimIds.size());
        completedJob.setSuccessfulClaims(0);
        completedJob.setFailedClaims(claimIds.size());
        completedJob.setCompleted(true);
        when(bulkClaimJobService.completeJob(eq(jobId), anyInt(), anyInt())).thenReturn(completedJob);

        // Execute
        claimService.processBulkClaimJob(jobId, claimIds, billingBranchId);

        // Verify the job was completed (with any arguments since the actual values depend on implementation details)
        verify(bulkClaimJobService).completeJob(eq(jobId), anyInt(), anyInt());
    }

    /**
     * Test error bubbling from nested components.
     */
    @Test
    void testErrorBubbling() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L);
        Long billingBranchId = 1L;

        // Mock the validateBulkSubmission method to return validation errors
        List<String> validationErrors = Arrays.asList("Invalid claim format");
        doReturn(validationErrors).when(claimService).validateBulkSubmission(claimIds);

        // Execute
        try {
            claimService.sendBulkClaimFiles(claimIds, billingBranchId);
        } catch (Exception e) {
            fail("Exception should not be thrown, errors should be returned in the result");
        }

        // Verify no job was created
        verify(bulkClaimJobService, never()).createJob(any());
    }
}
