package com.nymbl.tenant.service;

import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.repository.BulkClaimJobRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class BulkClaimJobServiceTest {

    @Mock
    private BulkClaimJobRepository bulkClaimJobRepository;

    @InjectMocks
    private BulkClaimJobService bulkClaimJobService;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    void testCreateJob() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L, 3L);
        BulkClaimJob expectedJob = new BulkClaimJob();
        expectedJob.setJobId(UUID.randomUUID().toString());

        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute
        BulkClaimJob result = bulkClaimJobService.createJob(claimIds);

        // Verify
        assertNotNull(result);
        assertNotNull(result.getJobId());
        assertEquals("PENDING", result.getStatus());
        assertEquals(claimIds.size(), result.getTotalClaims());
        assertEquals(0, result.getProcessedClaims());
        assertEquals(0, result.getSuccessfulClaims());
        assertEquals(0, result.getFailedClaims());
        assertNotNull(result.getStartTime());
        assertFalse(result.getCompleted());

        verify(bulkClaimJobRepository).save(any(BulkClaimJob.class));
    }

    @Test
    void testUpdateJobProgress() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        String currentPayer = "Test Payer";
        int processedClaims = 5;
        int successfulClaims = 3;
        int failedClaims = 2;

        BulkClaimJob existingJob = new BulkClaimJob();
        existingJob.setJobId(jobId);
        existingJob.setStatus("PENDING");
        existingJob.setTotalClaims(10);
        existingJob.setProcessedClaims(0);
        existingJob.setSuccessfulClaims(0);
        existingJob.setFailedClaims(0);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(existingJob);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute
        BulkClaimJob result = bulkClaimJobService.updateJobProgress(jobId, currentPayer, processedClaims, successfulClaims, failedClaims);

        // Verify
        assertNotNull(result);
        assertEquals("PROCESSING", result.getStatus());
        assertEquals(currentPayer, result.getCurrentPayer());
        assertEquals(processedClaims, result.getProcessedClaims());
        assertEquals(successfulClaims, result.getSuccessfulClaims());
        assertEquals(failedClaims, result.getFailedClaims());

        verify(bulkClaimJobRepository).findByJobIdOrderByIdDesc(jobId);
        verify(bulkClaimJobRepository).save(any(BulkClaimJob.class));
    }

    @Test
    void testCompleteJob() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        int successfulClaims = 8;
        int failedClaims = 2;

        BulkClaimJob existingJob = new BulkClaimJob();
        existingJob.setJobId(jobId);
        existingJob.setStatus("PROCESSING");
        existingJob.setTotalClaims(10);
        existingJob.setProcessedClaims(5);
        existingJob.setSuccessfulClaims(3);
        existingJob.setFailedClaims(2);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(existingJob);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute
        BulkClaimJob result = bulkClaimJobService.completeJob(jobId, successfulClaims, failedClaims);

        // Verify
        assertNotNull(result);
        assertEquals("COMPLETED", result.getStatus());
        assertEquals(10, result.getProcessedClaims()); // Should be set to totalClaims
        assertEquals(successfulClaims, result.getSuccessfulClaims());
        assertEquals(failedClaims, result.getFailedClaims());
        assertNotNull(result.getEndTime());
        assertTrue(result.getCompleted());

        verify(bulkClaimJobRepository).findByJobIdOrderByIdDesc(jobId);
        verify(bulkClaimJobRepository).save(any(BulkClaimJob.class));
    }

    @Test
    void testFailJob() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        String errorMessage = "Test error message";

        BulkClaimJob existingJob = new BulkClaimJob();
        existingJob.setJobId(jobId);
        existingJob.setStatus("PROCESSING");

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(existingJob);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute
        BulkClaimJob result = bulkClaimJobService.failJob(jobId, errorMessage);

        // Verify
        assertNotNull(result);
        assertEquals("FAILED", result.getStatus());
        assertEquals(errorMessage, result.getErrorMessage());
        assertNotNull(result.getEndTime());
        assertTrue(result.getCompleted());

        verify(bulkClaimJobRepository).findByJobIdOrderByIdDesc(jobId);
        verify(bulkClaimJobRepository).save(any(BulkClaimJob.class));
    }

    @Test
    void testFindByJobId() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob expectedJob = new BulkClaimJob();
        expectedJob.setJobId(jobId);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(expectedJob);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);

        // Execute
        BulkClaimJob result = bulkClaimJobService.findByJobId(jobId);

        // Verify
        assertNotNull(result);
        assertEquals(jobId, result.getJobId());

        verify(bulkClaimJobRepository).findByJobIdOrderByIdDesc(jobId);
    }

    @Test
    void testFindByJobId_NotFound() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(new ArrayList<>());

        // Execute
        BulkClaimJob result = bulkClaimJobService.findByJobId(jobId);

        // Verify
        assertNull(result);
        verify(bulkClaimJobRepository).findByJobIdOrderByIdDesc(jobId);
    }

    // Job Status Transition Tests

    @Test
    void testJobStatusTransition_PendingToProcessing() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PENDING");
        job.setTotalClaims(10);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute - Update progress which should change status to PROCESSING
        BulkClaimJob result = bulkClaimJobService.updateJobProgress(jobId, "Test Payer", 1, 1, 0);

        // Verify
        assertEquals("PROCESSING", result.getStatus());
    }

    @Test
    void testJobStatusTransition_ProcessingToCompleted() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PROCESSING");
        job.setTotalClaims(10);
        job.setProcessedClaims(5);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute - Complete the job
        BulkClaimJob result = bulkClaimJobService.completeJob(jobId, 8, 2);

        // Verify
        assertEquals("COMPLETED", result.getStatus());
        assertTrue(result.getCompleted());
    }

    @Test
    void testJobStatusTransition_ProcessingToFailed() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PROCESSING");
        job.setTotalClaims(10);
        job.setProcessedClaims(5);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute - Fail the job
        String errorMessage = "Test error";
        BulkClaimJob result = bulkClaimJobService.failJob(jobId, errorMessage);

        // Verify
        assertEquals("FAILED", result.getStatus());
        assertEquals(errorMessage, result.getErrorMessage());
        assertTrue(result.getCompleted());
    }

    // Progress Calculation Tests

    @Test
    void testProgressCalculation_ZeroClaims() {
        // Setup
        List<Long> claimIds = new ArrayList<>();
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute
        BulkClaimJob result = bulkClaimJobService.createJob(claimIds);

        // Verify
        assertEquals(0, result.getTotalClaims());
        assertEquals(0, result.getProcessedClaims());
        assertEquals(0, result.getSuccessfulClaims());
        assertEquals(0, result.getFailedClaims());
    }

    @Test
    void testProgressCalculation_PartialProgress() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PROCESSING");
        job.setTotalClaims(100);
        job.setProcessedClaims(0);
        job.setSuccessfulClaims(0);
        job.setFailedClaims(0);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute - Update to 50% progress
        BulkClaimJob result = bulkClaimJobService.updateJobProgress(jobId, "Test Payer", 50, 45, 5);

        // Verify
        assertEquals(100, result.getTotalClaims());
        assertEquals(50, result.getProcessedClaims());
        assertEquals(45, result.getSuccessfulClaims());
        assertEquals(5, result.getFailedClaims());
        // If we had a getProgress() method, we would check that it returns 50.0
    }

    @Test
    void testProgressCalculation_AllFailed() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PROCESSING");
        job.setTotalClaims(10);
        job.setProcessedClaims(0);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute - Complete with all claims failed
        BulkClaimJob result = bulkClaimJobService.completeJob(jobId, 0, 10);

        // Verify
        assertEquals(10, result.getTotalClaims());
        assertEquals(10, result.getProcessedClaims());
        assertEquals(0, result.getSuccessfulClaims());
        assertEquals(10, result.getFailedClaims());
        assertEquals("COMPLETED", result.getStatus());
    }

    // Error Message Handling Tests

    @Test
    void testErrorMessageHandling_LongErrorMessage() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PROCESSING");

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Create a very long error message (over 1000 characters)
        StringBuilder longErrorBuilder = new StringBuilder();
        for (int i = 0; i < 50; i++) {
            longErrorBuilder.append("Error message part ").append(i).append(". ");
        }
        String longErrorMessage = longErrorBuilder.toString();

        // Execute
        BulkClaimJob result = bulkClaimJobService.failJob(jobId, longErrorMessage);

        // Verify
        assertEquals("FAILED", result.getStatus());
        assertEquals(longErrorMessage, result.getErrorMessage());
        assertTrue(result.getCompleted());
    }

    @Test
    void testErrorMessageHandling_SpecialCharacters() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PROCESSING");

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Error message with special characters
        String errorWithSpecialChars = "Error with special chars: <>&\"'\\/ and unicode: ñáéíóú";

        // Execute
        BulkClaimJob result = bulkClaimJobService.failJob(jobId, errorWithSpecialChars);

        // Verify
        assertEquals("FAILED", result.getStatus());
        assertEquals(errorWithSpecialChars, result.getErrorMessage());
    }

    @Test
    void testErrorMessageHandling_NullErrorMessage() {
        // Setup
        String jobId = UUID.randomUUID().toString();
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PROCESSING");

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);

        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(jobList);
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execute with null error message
        BulkClaimJob result = bulkClaimJobService.failJob(jobId, null);

        // Verify
        assertEquals("FAILED", result.getStatus());
        assertNull(result.getErrorMessage());
        assertTrue(result.getCompleted());
    }
}
