package com.nymbl.tenant.service;

import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.repository.BulkClaimJobRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Tests for the asynchronous processing in BulkClaimJobService.
 * These tests focus on tenant context propagation during async operations.
 */
class BulkClaimJobServiceAsyncTest {

    @Mock
    private BulkClaimJobRepository bulkClaimJobRepository;

    @Mock
    private ClaimService claimService;

    @InjectMocks
    private BulkClaimJobService bulkClaimJobService;

    @BeforeEach
    void setUp() {
        openMocks(this);
        // Manually set the ClaimService since it's injected via setter injection
        bulkClaimJobService.setClaimService(claimService);
    }

    /**
     * Test that tenant context is properly propagated during async processing.
     * This test uses a spy to verify the behavior without actually running asynchronously.
     */
    @Test
    void testTenantContextPropagation() throws Exception {
        // Setup
        String jobId = "test-job-id";
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String tenantId = "test-tenant";

        // Set tenant context in the main thread
        TenantContext.setCurrentTenant(tenantId);

        try {
            // Create a spy of the service to verify the method call
            BulkClaimJobService spyService = spy(bulkClaimJobService);

            // Execute the method directly (not asynchronously)
            spyService.processBulkClaimJobAsync(jobId, claimIds, billingBranchId);

            // Verify that the method was called
            verify(claimService).processBulkClaimJob(eq(jobId), eq(claimIds), eq(billingBranchId));

            // Verify that the tenant context was available in the log message
            verify(spyService).processBulkClaimJobAsync(eq(jobId), eq(claimIds), eq(billingBranchId));
        } finally {
            // Clean up
            TenantContext.clear();
        }
    }

    /**
     * Test that errors in async processing are properly handled.
     * This test verifies that exceptions in the async method are caught and the job is marked as failed.
     */
    @Test
    void testAsyncErrorHandling() throws Exception {
        // Setup
        String jobId = "test-job-id";
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String errorMessage = "Test error message";

        // Create a mock BulkClaimJob to return when findByJobIdOrderByIdDesc is called
        BulkClaimJob mockJob = new BulkClaimJob();
        mockJob.setJobId(jobId);
        mockJob.setStatus("PROCESSING");
        List<BulkClaimJob> mockJobList = Collections.singletonList(mockJob);

        // Mock the repository to return our mock job
        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(jobId)).thenReturn(mockJobList);

        // Mock the repository save method to return the job
        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Mock the claimService.processBulkClaimJob method to throw an exception
        doThrow(new RuntimeException(errorMessage))
            .when(claimService).processBulkClaimJob(anyString(), any(), any());

        // Execute the method directly (not asynchronously)
        bulkClaimJobService.processBulkClaimJobAsync(jobId, claimIds, billingBranchId);

        // Verify
        verify(claimService).processBulkClaimJob(eq(jobId), eq(claimIds), eq(billingBranchId));
        verify(bulkClaimJobRepository).findByJobIdOrderByIdDesc(eq(jobId));
        verify(bulkClaimJobRepository).save(any(BulkClaimJob.class));
    }
}
