package com.nymbl.tenant.service;

import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.ClaimFile;
import com.nymbl.tenant.model.InsuranceCompany;
import com.nymbl.tenant.model.PatientInsurance;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.repository.BulkClaimJobRepository;
import com.nymbl.tenant.repository.ClaimRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Tests for memory and resource management in the bulk claim submission process.
 * These tests verify that large batches are handled properly and resources are cleaned up.
 */
class BulkClaimJobResourceManagementTest {

    @Mock
    private BulkClaimJobRepository bulkClaimJobRepository;

    @Mock
    private ClaimRepository claimRepository;

    @Mock
    private ClaimFileService claimFileService;

    @Mock
    private Factory837 factory837;

    @Mock
    private SystemSettingService systemSettingService;

    @Mock
    private BranchService branchService;

    @Spy
    @InjectMocks
    private ClaimService claimService;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @BeforeEach
    void setUp() {
        openMocks(this);
        // Set up the circular dependency manually
        claimService.setBulkClaimJobService(bulkClaimJobService);
    }

    /**
     * Test processing of a large batch of claims (100+ claims).
     */
    @Test
    void testLargeBatchProcessing() throws X12Exception {
        // Setup
        int batchSize = 100;
        List<Long> claimIds = IntStream.rangeClosed(1, batchSize)
                .mapToObj(i -> (long) i)
                .collect(Collectors.toList());
        Long billingBranchId = 1L;
        String jobId = "test-job-id";

        // Mock the validateBulkSubmission method to return no errors
        doReturn(Collections.emptyList()).when(claimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobService.createJob method
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PENDING");
        job.setTotalClaims(batchSize);
        when(bulkClaimJobService.createJob(claimIds)).thenReturn(job);

        // Mock the factory837.buildBulk method to return valid parameters
        List<Factory837Parameters> paramsList = new ArrayList<>();
        Factory837Parameters params = new Factory837Parameters();
        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(1L);
        params.setPatientInsurance(patientInsurance);
        paramsList.add(params);
        when(factory837.buildBulk(eq(claimIds), anyString(), eq(billingBranchId)))
                .thenReturn(paramsList);

        // Mock the claimFileService.save method
        ClaimFile claimFile = new ClaimFile();
        claimFile.setId(1L);
        when(claimFileService.save(any(ClaimFile.class))).thenReturn(claimFile);

        // Mock the findOne method to return claims
        for (Long claimId : claimIds) {
            Claim claim = new Claim();
            claim.setId(claimId);
            claim.setResponsiblePatientInsuranceId(1L);
            when(claimService.findOne(claimId)).thenReturn(claim);
        }

        // Execute
        claimService.sendBulkClaimFiles(claimIds, billingBranchId);

        // Verify the job was created
        verify(bulkClaimJobService).createJob(claimIds);

        // Verify the async processing was started
        verify(bulkClaimJobService).processBulkClaimJobAsync(eq(jobId), eq(claimIds), eq(billingBranchId));
    }

    /**
     * Test resource cleanup after exceptions.
     */
    @Test
    void testResourceCleanupAfterException() throws X12Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;
        String jobId = "test-job-id";

        // Mock the validateBulkSubmission method to return no errors
        doReturn(Collections.emptyList()).when(claimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobService.createJob method
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("PENDING");
        when(bulkClaimJobService.createJob(claimIds)).thenReturn(job);

        // Mock the findOne method to return a claim
        Claim firstClaim = new Claim();
        firstClaim.setId(1L);

        // Set up PatientInsurance with InsuranceCompany
        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(1L);

        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(1L);
        insuranceCompany.setName("Test Insurance Company");
        patientInsurance.setInsuranceCompany(insuranceCompany);

        firstClaim.setResponsiblePatientInsurance(patientInsurance);
        when(claimService.findOne(1L)).thenReturn(firstClaim);

        // Mock the SystemSettingService
        SystemSetting format837Setting = new SystemSetting();
        format837Setting.setValue("standard");
        when(systemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(format837Setting);

        // Mock the BranchService
        Branch branch = new Branch();
        branch.setId(billingBranchId);
        branch.setName("Test Branch");
        when(branchService.findOne(billingBranchId)).thenReturn(branch);

        // Mock the factory837.buildBulk method to return parameters with validation errors
        List<String> validationErrors = Arrays.asList("Missing required field: NPI");
        List<Factory837Parameters> paramsList = new ArrayList<>();
        Factory837Parameters params = new Factory837Parameters();
        for (String error : validationErrors) {
            params.addValidationError(error);
        }
        paramsList.add(params);
        when(factory837.buildBulk(eq(claimIds), anyString(), eq(billingBranchId)))
                .thenReturn(paramsList);

        // Execute
        try {
            claimService.processBulkClaimJob(jobId, claimIds, billingBranchId);
            fail("Expected X12Exception was not thrown");
        } catch (X12Exception e) {
            // Expected exception
            assertEquals(String.join("<br>", validationErrors), e.getMessage());
        }

        // Verify the job was marked as failed
        verify(bulkClaimJobService).failJob(eq(jobId), anyString());
    }

    /**
     * Test concurrent job submission and resource contention.
     */
    @Test
    void testConcurrentJobSubmission() throws InterruptedException {
        // Setup
        int concurrentJobs = 5;
        CountDownLatch latch = new CountDownLatch(concurrentJobs);
        ExecutorService executor = Executors.newFixedThreadPool(concurrentJobs);

        // Mock the validateBulkSubmission method to return no errors
        doReturn(Collections.emptyList()).when(claimService).validateBulkSubmission(any());

        // Mock the bulkClaimJobService.createJob method
        when(bulkClaimJobService.createJob(any())).thenAnswer(invocation -> {
            List<Long> ids = invocation.getArgument(0);
            BulkClaimJob job = new BulkClaimJob();
            job.setJobId("job-" + ids.get(0));
            job.setStatus("PENDING");
            return job;
        });

        // Execute concurrent job submissions
        for (int i = 0; i < concurrentJobs; i++) {
            final int jobIndex = i;
            executor.execute(() -> {
                try {
                    List<Long> claimIds = Arrays.asList((long) jobIndex);
                    claimService.sendBulkClaimFiles(claimIds, 1L);
                } catch (Exception e) {
                    fail("Exception should not be thrown: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // Wait for all jobs to complete
        assertTrue(latch.await(10, TimeUnit.SECONDS), "Not all jobs completed in time");

        // Verify that all jobs were created
        verify(bulkClaimJobService, times(concurrentJobs)).createJob(any());

        // Verify that async processing was started for all jobs
        verify(bulkClaimJobService, times(concurrentJobs)).processBulkClaimJobAsync(anyString(), any(), any());

        // Clean up
        executor.shutdown();
    }
}
