package com.nymbl.tenant.service;

import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.x12.x837.Factory837;
import com.nymbl.config.x12.x837.Factory837Parameters;
import com.nymbl.config.x12.x837.X12Claim;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.BulkClaimJobRepository;
import com.nymbl.tenant.repository.ClaimRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Integration tests for the bulk claim submission feature.
 * These tests verify the end-to-end workflow from submission to completion.
 */
class BulkClaimJobIntegrationTest {

    @Mock
    private BulkClaimJobRepository bulkClaimJobRepository;

    @Mock
    private ClaimRepository claimRepository;

    @Mock
    private ClaimFileService claimFileService;

    @Mock
    private Factory837 factory837;

    @Mock
    private SystemSettingService systemSettingService;

    @Mock
    private BranchService branchService;

    @Mock
    private NymblStatusHistoryService nymblStatusHistoryService;

    @Mock
    private UserService userService;

    @Spy
    @InjectMocks
    private ClaimService claimService;

    @Spy
    @InjectMocks
    private BulkClaimJobService bulkClaimJobService;

    @BeforeEach
    void setUp() {
        openMocks(this);

        // Set up the circular dependency manually
        claimService.setBulkClaimJobService(bulkClaimJobService);
        bulkClaimJobService.setClaimService(claimService);

        // Set up the repository for BulkClaimJobService
        ReflectionTestUtils.setField(bulkClaimJobService, "bulkClaimJobRepository", bulkClaimJobRepository);

        // Set up the NymblStatusHistoryService for ClaimService
        ReflectionTestUtils.setField(claimService, "nymblStatusHistoryService", nymblStatusHistoryService);

        // Set up the UserService for ClaimService
        ReflectionTestUtils.setField(claimService, "userService", userService);

        // Mock the getUserById method to return null (we don't need actual user objects for this test)
        when(userService.getUserById(any())).thenReturn(null);

        // Mock the getCurrentUser method to return a User with ID 1
        User mockUser = new User();
        mockUser.setId(1L);
        when(userService.getCurrentUser()).thenReturn(mockUser);
    }

    /**
     * Test the end-to-end workflow for bulk claim submission.
     */
    @Test
    void testEndToEndWorkflow() throws Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;

        // Mock the validateBulkSubmission method
        doReturn(new ArrayList<>()).when(claimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobRepository
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId("test-job-id");
        job.setStatus("PENDING");
        job.setTotalClaims(claimIds.size());

        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenReturn(job);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);
        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(anyString())).thenReturn(jobList);

        // Mock the SystemSettingService
        SystemSetting format837Setting = new SystemSetting();
        format837Setting.setValue("standard");
        when(systemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(format837Setting);

        // Mock the BranchService
        Branch branch = new Branch();
        branch.setId(billingBranchId);
        branch.setName("Test Branch");
        when(branchService.findOne(billingBranchId)).thenReturn(branch);

        // Mock the factory837
        List<Factory837Parameters> paramsList = new ArrayList<>();
        Factory837Parameters params = new Factory837Parameters();

        // Set up insurance company
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(1L);
        insuranceCompany.setName("Test Insurance Company");

        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(1L);
        patientInsurance.setInsuranceCompany(insuranceCompany);
        patientInsurance.setInsuranceCompanyId(insuranceCompany.getId());
        params.setPatientInsurance(patientInsurance);

        paramsList.add(params);
        when(factory837.buildBulk(eq(claimIds), anyString(), eq(billingBranchId))).thenReturn(paramsList);

        X12Claim mockX12Claim = mock(X12Claim.class);
        when(factory837.createBulkX12Claim(any())).thenReturn(mockX12Claim);
        when(mockX12Claim.toX12String()).thenReturn("MOCK_X12_STRING");

        // Mock the claimFileService
        ClaimFile claimFile = new ClaimFile();
        claimFile.setId(1L);
        when(claimFileService.save(any(ClaimFile.class))).thenReturn(claimFile);

        // Mock the findOne method to return claims
        for (Long claimId : claimIds) {
            Claim claim = new Claim();
            claim.setId(claimId);
            claim.setResponsiblePatientInsuranceId(1L);

            PatientInsurance claimPatientInsurance = new PatientInsurance();
            claimPatientInsurance.setId(1L);
            claimPatientInsurance.setInsuranceCompany(insuranceCompany);
            claimPatientInsurance.setInsuranceCompanyId(insuranceCompany.getId());
            claim.setResponsiblePatientInsurance(claimPatientInsurance);

            when(claimService.findOne(claimId)).thenReturn(claim);
        }

        // Create a latch to wait for async processing
        CountDownLatch latch = new CountDownLatch(1);

        // Mock the processBulkClaimJobAsync method to release the latch
        doAnswer(invocation -> {
            // Call the real method
            String jobId = invocation.getArgument(0);
            List<Long> ids = invocation.getArgument(1);
            Long branchId = invocation.getArgument(2);

            try {
                claimService.processBulkClaimJob(jobId, ids, branchId);
            } finally {
                latch.countDown();
            }

            return null;
        }).when(bulkClaimJobService).processBulkClaimJobAsync(anyString(), any(), any());

        // Execute
        Map<String, Object> result = claimService.sendBulkClaimFiles(claimIds, billingBranchId);

        // Wait for async processing to complete
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Async processing did not complete in time");

        // Verify the result
        assertNotNull(result);
        assertEquals(true, result.get("success"));
        assertEquals("test-job-id", result.get("jobId"));
        // The status could be either PENDING or COMPLETED depending on timing
        assertTrue(result.get("status").equals("PENDING") || result.get("status").equals("COMPLETED"),
                "Status should be either PENDING or COMPLETED, but was: " + result.get("status"));

        // Verify the job was created and updated multiple times
        verify(bulkClaimJobRepository, atLeast(1)).save(any(BulkClaimJob.class));

        // Verify the job was processed
        verify(bulkClaimJobService).processBulkClaimJobAsync(eq("test-job-id"), eq(claimIds), eq(billingBranchId));

        // Verify the claims were processed (may be called more than once per claim)
        verify(claimService, atLeast(claimIds.size())).findOne(anyLong());

        // Verify the job was completed
        verify(bulkClaimJobService).completeJob(eq("test-job-id"), anyInt(), anyInt());
    }

    /**
     * Test database state consistency after bulk operations.
     */
    @Test
    void testDatabaseConsistency() throws X12Exception {
        // Setup similar to testEndToEndWorkflow
        List<Long> claimIds = Arrays.asList(1L, 2L);
        Long billingBranchId = 1L;

        // Mock the validateBulkSubmission method
        doReturn(new ArrayList<>()).when(claimService).validateBulkSubmission(claimIds);

        // Mock the bulkClaimJobRepository
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId("test-job-id");
        job.setStatus("PENDING");
        job.setTotalClaims(claimIds.size());

        when(bulkClaimJobRepository.save(any(BulkClaimJob.class))).thenReturn(job);

        List<BulkClaimJob> jobList = new ArrayList<>();
        jobList.add(job);
        when(bulkClaimJobRepository.findByJobIdOrderByIdDesc(anyString())).thenReturn(jobList);

        // Mock the SystemSettingService
        SystemSetting format837Setting = new SystemSetting();
        format837Setting.setValue("standard");
        when(systemSettingService.findBySectionAndField("claim", "format_837")).thenReturn(format837Setting);

        // Mock the BranchService
        Branch branch = new Branch();
        branch.setId(billingBranchId);
        branch.setName("Test Branch");
        when(branchService.findOne(billingBranchId)).thenReturn(branch);

        // Mock the factory837
        List<Factory837Parameters> paramsList = new ArrayList<>();
        Factory837Parameters params = new Factory837Parameters();

        // Set up insurance company
        InsuranceCompany insuranceCompany = new InsuranceCompany();
        insuranceCompany.setId(1L);
        insuranceCompany.setName("Test Insurance Company");

        PatientInsurance patientInsurance = new PatientInsurance();
        patientInsurance.setId(1L);
        patientInsurance.setInsuranceCompany(insuranceCompany);
        patientInsurance.setInsuranceCompanyId(insuranceCompany.getId());
        params.setPatientInsurance(patientInsurance);

        paramsList.add(params);
        when(factory837.buildBulk(eq(claimIds), anyString(), eq(billingBranchId))).thenReturn(paramsList);

        X12Claim mockX12Claim = mock(X12Claim.class);
        when(factory837.createBulkX12Claim(any())).thenReturn(mockX12Claim);
        when(mockX12Claim.toX12String()).thenReturn("MOCK_X12_STRING");

        // Mock the claimFileService
        ClaimFile claimFile = new ClaimFile();
        claimFile.setId(1L);
        when(claimFileService.save(any(ClaimFile.class))).thenReturn(claimFile);

        // Mock the findOne method to return claims
        for (Long claimId : claimIds) {
            Claim claim = new Claim();
            claim.setId(claimId);
            claim.setResponsiblePatientInsuranceId(1L);

            PatientInsurance claimPatientInsurance = new PatientInsurance();
            claimPatientInsurance.setId(1L);
            claimPatientInsurance.setInsuranceCompany(insuranceCompany);
            claimPatientInsurance.setInsuranceCompanyId(insuranceCompany.getId());
            claim.setResponsiblePatientInsurance(claimPatientInsurance);

            when(claimService.findOne(claimId)).thenReturn(claim);
            when(claimRepository.save(any(Claim.class))).thenReturn(claim);
        }

        // Execute
        claimService.processBulkClaimJob("test-job-id", claimIds, billingBranchId);

        // Verify database operations
        verify(bulkClaimJobRepository, atLeastOnce()).save(any(BulkClaimJob.class));
        verify(claimFileService).save(any(ClaimFile.class));
    }
}
