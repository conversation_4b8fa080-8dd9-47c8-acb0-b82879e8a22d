package com.nymbl.tenant.controller;

import com.nymbl.config.service.Print1500Service;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.ClaimFile;
import com.nymbl.tenant.model.ClaimSubmission;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimFileService;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.ClaimSubmissionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ClaimFileControllerTest {

    @Mock
    private ClaimFileService claimFileService;

    @Mock
    private ClaimSubmissionService claimSubmissionService;

    @Mock
    private Print1500Service print1500Service;

    @Mock
    private FileUtil fileUtil;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @Mock
    private ClaimService claimService;

    @InjectMocks
    private ClaimFileController controller;

    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new MockHttpServletRequest();
    }

    @Test
    void testFindByClaimId() {
        // Arrange
        Long claimId = 1L;
        List<ClaimFile> expectedFiles = new ArrayList<>();
        ClaimFile file1 = new ClaimFile();
        file1.setId(1L);
        expectedFiles.add(file1);
        
        when(claimFileService.findByClaimId(claimId)).thenReturn(expectedFiles);
        
        // Act
        ResponseEntity<?> response = controller.findByClaimId(claimId, request);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(expectedFiles, response.getBody());
        
        verify(claimFileService, times(1)).findByClaimId(claimId);
    }

    @Test
    void testPrintSubmittedCMS() throws Exception {
        // Arrange
        Long claimFileId = 1L;
        ClaimFile claimFile = new ClaimFile();
        claimFile.setId(claimFileId);
        claimFile.setContents("X12 content");
        
        List<ClaimSubmission> submissions = new ArrayList<>();
        ClaimSubmission submission = new ClaimSubmission();
        submission.setId(1L);
        submissions.add(submission);
        
        List<String> filePaths = Arrays.asList("path/to/file1.pdf", "path/to/file2.pdf");
        byte[] fileContents1 = "PDF content 1".getBytes();
        byte[] fileContents2 = "PDF content 2".getBytes();
        
        when(claimFileService.findOne(claimFileId)).thenReturn(claimFile);
        when(claimSubmissionService.findByClaimFileId(claimFileId)).thenReturn(submissions);
        when(print1500Service.printFromClaimFile(claimFile.getContents(), submissions)).thenReturn(filePaths);
        when(fileUtil.loadEobContents(filePaths.get(0))).thenReturn(fileContents1);
        when(fileUtil.loadEobContents(filePaths.get(1))).thenReturn(fileContents2);
        
        // Act
        ResponseEntity<?> response = controller.printSubmittedCMS(claimFileId, request);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        List<byte[]> results = (List<byte[]>) response.getBody();
        assertEquals(2, results.size());
        assertArrayEquals(fileContents1, results.get(0));
        assertArrayEquals(fileContents2, results.get(1));
        
        verify(claimFileService, times(1)).findOne(claimFileId);
        verify(claimSubmissionService, times(1)).findByClaimFileId(claimFileId);
        verify(print1500Service, times(1)).printFromClaimFile(claimFile.getContents(), submissions);
        verify(fileUtil, times(2)).loadEobContents(anyString());
    }

    @Test
    void testPrintBulkCMS_Success() throws Exception {
        // Arrange
        Long claimId = 1L;
        String jobId = "test-job-id";
        String x12Content = "X12 content for bulk claim";
        
        Claim claim = new Claim();
        claim.setId(claimId);
        claim.setBulkClaimJobId(jobId);
        claim.setBulkSubmission(true);
        
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setX12FileContent(x12Content);
        
        List<String> filePaths = Arrays.asList("path/to/bulk/file1.pdf", "path/to/bulk/file2.pdf");
        byte[] fileContents1 = "Bulk PDF content 1".getBytes();
        byte[] fileContents2 = "Bulk PDF content 2".getBytes();
        
        when(claimService.findOne(claimId)).thenReturn(claim);
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);
        when(print1500Service.printBulkX12CMS(x12Content, claimId)).thenReturn(filePaths);
        when(fileUtil.loadEobContents(filePaths.get(0))).thenReturn(fileContents1);
        when(fileUtil.loadEobContents(filePaths.get(1))).thenReturn(fileContents2);
        
        // Act
        ResponseEntity<?> response = controller.printBulkCMS(claimId, request);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        List<byte[]> results = (List<byte[]>) response.getBody();
        assertEquals(2, results.size());
        assertArrayEquals(fileContents1, results.get(0));
        assertArrayEquals(fileContents2, results.get(1));
        
        verify(claimService, times(1)).findOne(claimId);
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
        verify(print1500Service, times(1)).printBulkX12CMS(x12Content, claimId);
        verify(fileUtil, times(2)).loadEobContents(anyString());
    }

    @Test
    void testPrintBulkCMS_ClaimNotFound() throws Exception {
        // Arrange
        Long claimId = 1L;
        when(claimService.findOne(claimId)).thenReturn(null);
        
        // Act
        ResponseEntity<?> response = controller.printBulkCMS(claimId, request);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        
        verify(claimService, times(1)).findOne(claimId);
        verify(bulkClaimJobService, never()).findByJobId(anyString());
        verify(print1500Service, never()).printBulkX12CMS(anyString(), anyLong());
    }

    @Test
    void testPrintBulkCMS_NotBulkClaim() throws Exception {
        // Arrange
        Long claimId = 1L;
        
        Claim claim = new Claim();
        claim.setId(claimId);
        claim.setBulkSubmission(false);
        
        when(claimService.findOne(claimId)).thenReturn(claim);
        
        // Act
        ResponseEntity<?> response = controller.printBulkCMS(claimId, request);
        
        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Claim is not part of a bulk submission", response.getBody());
        
        verify(claimService, times(1)).findOne(claimId);
        verify(bulkClaimJobService, never()).findByJobId(anyString());
        verify(print1500Service, never()).printBulkX12CMS(anyString(), anyLong());
    }

    @Test
    void testPrintBulkCMS_BulkJobNotFound() throws Exception {
        // Arrange
        Long claimId = 1L;
        String jobId = "test-job-id";
        
        Claim claim = new Claim();
        claim.setId(claimId);
        claim.setBulkClaimJobId(jobId);
        claim.setBulkSubmission(true);
        
        when(claimService.findOne(claimId)).thenReturn(claim);
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(null);
        
        // Act
        ResponseEntity<?> response = controller.printBulkCMS(claimId, request);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        
        verify(claimService, times(1)).findOne(claimId);
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
        verify(print1500Service, never()).printBulkX12CMS(anyString(), anyLong());
    }
}
