package com.nymbl.tenant.controller;

import com.nymbl.tenant.dto.BulkClaimJobDTO;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class BulkClaimJobControllerTest {

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @Mock
    private ClaimService claimService;

    @InjectMocks
    private BulkClaimJobController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetAllBulkClaimJobs() {
        // Arrange
        BulkClaimJob job1 = new BulkClaimJob();
        job1.setJobId("job-1");
        job1.setStatus("COMPLETED");
        job1.setTotalClaims(10);
        job1.setSuccessfulClaims(8);
        job1.setFailedClaims(2);
        job1.setCompleted(true);
        
        BulkClaimJob job2 = new BulkClaimJob();
        job2.setJobId("job-2");
        job2.setStatus("PROCESSING");
        job2.setTotalClaims(5);
        job2.setSuccessfulClaims(3);
        job2.setFailedClaims(0);
        job2.setCompleted(false);
        
        List<BulkClaimJob> jobs = Arrays.asList(job1, job2);
        
        when(bulkClaimJobService.findAll()).thenReturn(jobs);
        
        // Act
        ResponseEntity<List<BulkClaimJobDTO>> response = controller.getAllBulkClaimJobs();
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals("job-1", response.getBody().get(0).getJobId());
        assertEquals("job-2", response.getBody().get(1).getJobId());
        
        verify(bulkClaimJobService, times(1)).findAll();
    }

    @Test
    void testGetBulkClaimJob() {
        // Arrange
        String jobId = "test-job-id";
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setStatus("COMPLETED");
        job.setTotalClaims(10);
        job.setSuccessfulClaims(8);
        job.setFailedClaims(2);
        job.setCompleted(true);
        job.setStartTime(ZonedDateTime.now().minusHours(1));
        job.setEndTime(ZonedDateTime.now());
        job.setX12FileContent("X12 content");
        
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);
        
        // Act
        ResponseEntity<BulkClaimJobDTO> response = controller.getBulkClaimJob(jobId);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(jobId, response.getBody().getJobId());
        assertEquals("COMPLETED", response.getBody().getStatus());
        assertEquals(10, response.getBody().getTotalClaims());
        assertEquals(8, response.getBody().getSuccessfulClaims());
        assertEquals(2, response.getBody().getFailedClaims());
        assertTrue(response.getBody().getCompleted());
        assertEquals("X12 content", response.getBody().getX12FileContent());
        
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }

    @Test
    void testGetBulkClaimJob_NotFound() {
        // Arrange
        String jobId = "non-existent-job-id";
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(null);
        
        // Act
        ResponseEntity<BulkClaimJobDTO> response = controller.getBulkClaimJob(jobId);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }

    @Test
    void testGetClaimsByBulkClaimJobId() {
        // Arrange
        String jobId = "test-job-id";
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        
        Claim claim1 = new Claim();
        claim1.setId(1L);
        claim1.setBulkClaimJobId(jobId);
        
        Claim claim2 = new Claim();
        claim2.setId(2L);
        claim2.setBulkClaimJobId(jobId);
        
        List<Claim> claims = Arrays.asList(claim1, claim2);
        
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);
        when(claimService.findByBulkClaimJobId(jobId)).thenReturn(claims);
        
        // Act
        ResponseEntity<List<Claim>> response = controller.getClaimsByBulkClaimJobId(jobId);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals(1L, response.getBody().get(0).getId());
        assertEquals(2L, response.getBody().get(1).getId());
        
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
        verify(claimService, times(1)).findByBulkClaimJobId(jobId);
    }

    @Test
    void testGetClaimsByBulkClaimJobId_JobNotFound() {
        // Arrange
        String jobId = "non-existent-job-id";
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(null);
        
        // Act
        ResponseEntity<List<Claim>> response = controller.getClaimsByBulkClaimJobId(jobId);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
        verify(claimService, never()).findByBulkClaimJobId(anyString());
    }

    @Test
    void testDownloadX12File() {
        // Arrange
        String jobId = "test-job-id";
        String x12Content = "X12 file content";
        
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setX12FileContent(x12Content);
        
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);
        
        // Act
        ResponseEntity<String> response = controller.downloadX12File(jobId);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(x12Content, response.getBody());
        assertEquals(MediaType.TEXT_PLAIN, response.getHeaders().getContentType());
        assertTrue(response.getHeaders().getContentDisposition().toString().contains("attachment"));
        assertTrue(response.getHeaders().getContentDisposition().toString().contains("bulk_claim_" + jobId + ".x12"));
        
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }

    @Test
    void testDownloadX12File_JobNotFound() {
        // Arrange
        String jobId = "non-existent-job-id";
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(null);
        
        // Act
        ResponseEntity<String> response = controller.downloadX12File(jobId);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }

    @Test
    void testDownloadX12File_NoX12Content() {
        // Arrange
        String jobId = "test-job-id";
        
        BulkClaimJob job = new BulkClaimJob();
        job.setJobId(jobId);
        job.setX12FileContent(null);
        
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);
        
        // Act
        ResponseEntity<String> response = controller.downloadX12File(jobId);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        
        verify(bulkClaimJobService, times(1)).findByJobId(jobId);
    }
}
