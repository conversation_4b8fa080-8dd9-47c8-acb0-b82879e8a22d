package com.nymbl.tenant.controller;

import com.nymbl.tenant.dto.ClaimCreationRequest;
import com.nymbl.tenant.dto.ClaimFilesRequest;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.Fake;
import jakarta.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.sql.Date;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Unit tests for the ClaimController class.
 *
 * These tests focus on the CSRF-protected endpoints:
 * - /api/claim/add (POST)
 * - /api/claim/send-files (POST)
 */
class ClaimControllerTest {

    @Mock
    private ClaimService mockClaimService;

    @Mock
    private HttpServletRequest mockRequest;

    @InjectMocks
    private ClaimController claimControllerUnderTest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }



    /**
     * Test for the new addClaimPost endpoint.
     * This tests the implementation that uses a POST request with a request body.
     */
    @Test
    void testAddClaimPost() throws Exception {
        // Setup
        ClaimCreationRequest request = new ClaimCreationRequest();
        request.setPrescriptionId(1L);
        request.setUserId(2L);
        request.setBillingBranchId(3L);
        request.setDateOfService(Date.valueOf(LocalDate.now()));
        request.setResend(false);

        Claim expectedClaim = Fake.getClaim();
        List<Claim> claimList = Arrays.asList(expectedClaim);

        Mockito.when(this.mockClaimService.addClaim(
            ArgumentMatchers.eq(request.getPrescriptionId()),
            ArgumentMatchers.eq(request.getUserId()),
            ArgumentMatchers.eq(request.getBillingBranchId()),
            ArgumentMatchers.eq(request.getDateOfService()),
            ArgumentMatchers.eq(request.isResend())
        )).thenReturn(expectedClaim);
        Mockito.when(this.mockClaimService.findByPrescriptionId(request.getPrescriptionId())).thenReturn(claimList);

        // Run the test
        ResponseEntity<?> result = this.claimControllerUnderTest.addClaimPost(request, this.mockRequest);

        // Verify the results
        assertNotNull(result);
        assertEquals(200, result.getStatusCode().value());
        assertEquals(expectedClaim, result.getBody());

        // Verify service method was called with correct parameters
        Mockito.verify(this.mockClaimService).addClaim(
            request.getPrescriptionId(),
            request.getUserId(),
            request.getBillingBranchId(),
            request.getDateOfService(),
            request.isResend()
        );
        Mockito.verify(this.mockClaimService).findByPrescriptionId(request.getPrescriptionId());
        Mockito.verify(this.mockClaimService).auditNewlyAddedClaim(claimList);
    }
    /**
     * Test for the new addClaimPost endpoint with validation errors.
     */
    @Test
    void testAddClaimPost_ValidationError() throws Exception {
        // Setup
        ClaimCreationRequest request = new ClaimCreationRequest();
        // Missing required prescriptionId

        // Run the test
        ResponseEntity<?> result = this.claimControllerUnderTest.addClaimPost(request, this.mockRequest);

        // Verify the results
        assertNotNull(result);
        assertEquals(400, result.getStatusCode().value());
        Object body = result.getBody();
        assertNotNull(body);
        assertTrue(body.toString().contains("Prescription ID is required"));

        // Verify service method was not called
        Mockito.verify(this.mockClaimService, Mockito.never()).addClaim(
            ArgumentMatchers.any(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any(),
            ArgumentMatchers.anyBoolean()
        );
    }

    /**
     * Test for the new sendClaimFilesPost endpoint.
     */
    @Test
    void testSendClaimFilesPost_SingleSubmission() throws Exception {
        // Setup
        ClaimFilesRequest request = new ClaimFilesRequest();
        request.setClaimIds(Arrays.asList(1L, 2L, 3L));
        request.setBillingBranchId(4L);
        request.setSubmissionType("single");

        Mockito.when(this.mockClaimService.sendClaimFiles(
            ArgumentMatchers.eq(request.getClaimIds()),
            ArgumentMatchers.eq(request.getBillingBranchId())
        )).thenReturn(true);

        // Run the test
        ResponseEntity<?> result = this.claimControllerUnderTest.sendClaimFilesPost(request, this.mockRequest);

        // Verify the results
        assertNotNull(result);
        assertEquals(200, result.getStatusCode().value());

        // Verify service method was called with correct parameters
        Mockito.verify(this.mockClaimService).sendClaimFiles(request.getClaimIds(), request.getBillingBranchId());
    }

    /**
     * Test for the new sendClaimFilesPost endpoint when claims don't pass edit rules.
     */
    @Test
    void testSendClaimFilesPost_FailedEditRules() throws Exception {
        // Setup
        ClaimFilesRequest request = new ClaimFilesRequest();
        request.setClaimIds(Arrays.asList(1L, 2L, 3L));
        request.setBillingBranchId(4L);
        request.setSubmissionType("single");

        Mockito.when(this.mockClaimService.sendClaimFiles(
            ArgumentMatchers.eq(request.getClaimIds()),
            ArgumentMatchers.eq(request.getBillingBranchId())
        )).thenReturn(false);

        // Run the test
        ResponseEntity<?> result = this.claimControllerUnderTest.sendClaimFilesPost(request, this.mockRequest);

        // Verify the results
        assertNotNull(result);
        assertEquals(400, result.getStatusCode().value());

        // Verify service method was called with correct parameters
        Mockito.verify(this.mockClaimService).sendClaimFiles(request.getClaimIds(), request.getBillingBranchId());
    }
}