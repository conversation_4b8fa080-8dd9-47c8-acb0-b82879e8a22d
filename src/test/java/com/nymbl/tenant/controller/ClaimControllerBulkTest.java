package com.nymbl.tenant.controller;

import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.security.PrivilegeChecker;
import com.nymbl.tenant.dto.BulkClaimJobDTO;
import com.nymbl.tenant.dto.BulkClaimRequest;
import com.nymbl.tenant.dto.ClaimFilesRequest;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.SystemSettingService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class ClaimControllerBulkTest {

    @Mock
    private ClaimService claimService;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private SystemSettingService systemSettingService;

    @Mock
    private PrivilegeChecker privilegeChecker;

    @InjectMocks
    private ClaimController claimController;

    @BeforeEach
    void setUp() {
        openMocks(this);

        // Mock system settings to enable bulk claims
        SystemSetting bulkClaimSetting = new SystemSetting();
        bulkClaimSetting.setValue("Y");
        when(systemSettingService.findBySectionAndField("billing", "enable_bulk_claims"))
            .thenReturn(bulkClaimSetting);

        // Mock privilege checker to allow bulk claim submission
        when(privilegeChecker.hasPrivilege("bulk_claim_submit")).thenReturn(true);
    }

    @Test
    void testValidateBulkSubmission() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        BulkClaimRequest request = BulkClaimRequest.builder()
            .claimIds(claimIds)
            .build();

        List<String> errors = Collections.emptyList();
        when(claimService.validateBulkSubmission(claimIds)).thenReturn(errors);

        // Execute
        ResponseEntity<?> response = claimController.validateBulkSubmission(request);

        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        Object body = response.getBody();
        assertNotNull(body);

        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) body;
        assertTrue((Boolean) responseBody.get("valid"));
        assertEquals(errors, responseBody.get("errors"));

        verify(claimService).validateBulkSubmission(claimIds);
    }

    @Test
    void testValidateBulkSubmission_WithErrors() {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        BulkClaimRequest request = BulkClaimRequest.builder()
            .claimIds(claimIds)
            .build();

        List<String> errors = Collections.singletonList("Validation error");
        when(claimService.validateBulkSubmission(claimIds)).thenReturn(errors);

        // Execute
        ResponseEntity<?> response = claimController.validateBulkSubmission(request);

        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        Object body = response.getBody();
        assertNotNull(body);

        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) body;
        assertFalse((Boolean) responseBody.get("valid"));
        assertEquals(errors, responseBody.get("errors"));

        verify(claimService).validateBulkSubmission(claimIds);
    }

    @Test
    void testGetBulkJobStatus() {
        // Setup
        String jobId = "test-job-id";

        BulkClaimJob job = BulkClaimJob.builder()
            .jobId(jobId)
            .status("PROCESSING")
            .totalClaims(10)
            .processedClaims(5)
            .successfulClaims(4)
            .failedClaims(1)
            .currentPayer("Test Payer")
            .completed(false)
            .build();

        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);

        // Execute
        ResponseEntity<?> response = claimController.getBulkJobStatus(jobId);

        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        Object body = response.getBody();
        assertNotNull(body);

        // The response body is now a BulkClaimJobDTO
        BulkClaimJobDTO dto = (BulkClaimJobDTO) body;
        assertEquals(jobId, dto.getJobId());
        assertEquals("PROCESSING", dto.getStatus());
        assertEquals(Integer.valueOf(10), dto.getTotalClaims());
        assertEquals(Integer.valueOf(5), dto.getProcessedClaims());
        assertEquals(Integer.valueOf(4), dto.getSuccessfulClaims());
        assertEquals(Integer.valueOf(1), dto.getFailedClaims());
        assertEquals("Test Payer", dto.getCurrentPayer());
        assertFalse(dto.getCompleted());

        verify(bulkClaimJobService).findByJobId(jobId);
    }

    @Test
    void testGetBulkJobStatus_Completed() {
        // Setup
        String jobId = "test-job-id";

        BulkClaimJob job = BulkClaimJob.builder()
            .jobId(jobId)
            .status("COMPLETED")
            .completed(true)
            .build();

        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);

        // Execute
        ResponseEntity<?> response = claimController.getBulkJobStatus(jobId);

        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        Object body = response.getBody();
        assertNotNull(body);

        // The response body is now a BulkClaimJobDTO
        BulkClaimJobDTO dto = (BulkClaimJobDTO) body;
        assertEquals("COMPLETED", dto.getStatus());
        assertEquals(true, dto.getCompleted());

        verify(bulkClaimJobService).findByJobId(jobId);
    }

    @Test
    void testGetBulkJobStatus_Failed() {
        // Setup
        String jobId = "test-job-id";

        BulkClaimJob job = BulkClaimJob.builder()
            .jobId(jobId)
            .status("FAILED")
            .completed(true)
            .build();

        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(job);

        // Execute
        ResponseEntity<?> response = claimController.getBulkJobStatus(jobId);

        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        Object body = response.getBody();
        assertNotNull(body);

        // The response body is now a BulkClaimJobDTO
        BulkClaimJobDTO dto = (BulkClaimJobDTO) body;
        assertEquals("FAILED", dto.getStatus());
        assertEquals(true, dto.getCompleted());

        verify(bulkClaimJobService).findByJobId(jobId);
    }

    @Test
    void testGetBulkJobStatus_NotFound() {
        // Setup
        String jobId = "test-job-id";

        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(null);

        // Execute
        ResponseEntity<?> response = claimController.getBulkJobStatus(jobId);

        // Verify
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());

        verify(bulkClaimJobService).findByJobId(jobId);
    }

    @Test
    void testSendClaimFilesPost_BulkSubmission() throws Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        ClaimFilesRequest request = ClaimFilesRequest.builder()
            .claimIds(claimIds)
            .billingBranchId(1L)
            .submissionType("bulk")
            .build();

        Map<String, Object> jobResult = new HashMap<>();
        jobResult.put("success", true);
        jobResult.put("jobId", "test-job-id");
        jobResult.put("status", "PENDING");

        when(claimService.sendBulkClaimFiles(eq(claimIds), eq(1L))).thenReturn(jobResult);

        // Execute
        ResponseEntity<?> response = claimController.sendClaimFilesPost(request, httpServletRequest);

        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(jobResult, response.getBody());

        verify(claimService).sendBulkClaimFiles(eq(claimIds), eq(1L));
    }

    @Test
    void testSendClaimFilesPost_SingleSubmission_Success() throws Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        ClaimFilesRequest request = ClaimFilesRequest.builder()
            .claimIds(claimIds)
            .billingBranchId(1L)
            .submissionType("single")
            .build();

        when(claimService.sendClaimFiles(eq(claimIds), eq(1L))).thenReturn(true);

        // Execute
        ResponseEntity<?> response = claimController.sendClaimFilesPost(request, httpServletRequest);

        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
        assertNotNull(responseBody);
        assertEquals(true, responseBody.get("success"));
        assertEquals("Claims sent successfully.", responseBody.get("data"));

        verify(claimService).sendClaimFiles(eq(claimIds), eq(1L));
    }

    @Test
    void testSendClaimFilesPost_SingleSubmission_Failure() throws Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        ClaimFilesRequest request = ClaimFilesRequest.builder()
            .claimIds(claimIds)
            .billingBranchId(1L)
            .submissionType("single")
            .build();

        when(claimService.sendClaimFiles(eq(claimIds), eq(1L))).thenReturn(false);

        // Execute
        ResponseEntity<?> response = claimController.sendClaimFilesPost(request, httpServletRequest);

        // Verify
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
        assertNotNull(responseBody);
        assertEquals(false, responseBody.get("success"));
        assertEquals("Claims did not pass edit rules.", responseBody.get("error"));

        verify(claimService).sendClaimFiles(eq(claimIds), eq(1L));
    }

    @Test
    void testSendClaimFilesPost_X12Exception() throws Exception {
        // Setup
        List<Long> claimIds = Arrays.asList(1L, 2L);
        ClaimFilesRequest request = ClaimFilesRequest.builder()
            .claimIds(claimIds)
            .billingBranchId(1L)
            .submissionType("bulk")
            .build();

        X12Exception exception = new X12Exception(Collections.singletonList("X12 error"));
        when(claimService.sendBulkClaimFiles(eq(claimIds), eq(1L))).thenThrow(exception);

        // Execute
        ResponseEntity<?> response = claimController.sendClaimFilesPost(request, httpServletRequest);

        // Verify
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
        assertNotNull(responseBody);
        assertEquals(false, responseBody.get("success"));
        assertEquals(exception.getMessage(), responseBody.get("error"));

        verify(claimService).sendBulkClaimFiles(eq(claimIds), eq(1L));
    }

    @Test
    void testSendClaimFilesPost_NoClaimIds() throws Exception {
        // Setup
        ClaimFilesRequest request = ClaimFilesRequest.builder()
            .claimIds(null)
            .build();

        // Execute
        ResponseEntity<?> response = claimController.sendClaimFilesPost(request, httpServletRequest);

        // Verify
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
        assertNotNull(responseBody);
        assertEquals(false, responseBody.get("success"));
        assertEquals("Claim IDs are required", responseBody.get("error"));

        verifyNoInteractions(claimService);
    }
}
