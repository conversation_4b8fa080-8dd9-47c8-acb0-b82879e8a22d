package com.nymbl.tenant.performance;

import com.nymbl.config.exception.X12Exception;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Performance tests for bulk claim submission.
 * These tests measure the performance of bulk claim submission with different batch sizes.
 *
 * Note: These tests use mocks to simulate the behavior of the system without requiring
 * a full Spring context or database connection.
 */
class BulkClaimSubmissionPerformanceTest {

    @Mock
    private ClaimService claimService;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    private Long billingBranchId = 1L;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Create a batch of test claim IDs.
     *
     * @param count the number of claims to create
     * @return the list of claim IDs
     */
    private List<Long> createTestClaimIds(int count) {
        List<Long> claimIds = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            claimIds.add((long) (i + 1));
        }
        return claimIds;
    }

    /**
     * Test bulk claim submission with a small batch (10 claims).
     */
    @Test
    void testBulkClaimSubmission_SmallBatch() throws X12Exception {
        // Create 10 test claims
        List<Long> claimIds = createTestClaimIds(10);

        // Setup mocks
        String jobId = "test-job-id";
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("jobId", jobId);
        result.put("status", "PENDING");

        BulkClaimJob completedJob = new BulkClaimJob();
        completedJob.setJobId(jobId);
        completedJob.setStatus("COMPLETED");
        completedJob.setTotalClaims(claimIds.size());
        completedJob.setProcessedClaims(claimIds.size());
        completedJob.setSuccessfulClaims(claimIds.size());
        completedJob.setFailedClaims(0);
        completedJob.setCompleted(true);

        when(claimService.sendBulkClaimFiles(eq(claimIds), eq(billingBranchId))).thenReturn(result);
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(completedJob);

        // Measure performance
        Instant start = Instant.now();

        // Submit claims
        Map<String, Object> actualResult = claimService.sendBulkClaimFiles(claimIds, billingBranchId);

        // Get job ID
        String actualJobId = (String) actualResult.get("jobId");
        assertNotNull(actualJobId);

        // Simulate job completion
        BulkClaimJob job = bulkClaimJobService.findByJobId(actualJobId);

        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);

        // Log performance metrics
        System.out.println("Small batch (10 claims) processing time: " + duration.toMillis() + " ms");

        // Verify job status
        assertEquals("COMPLETED", job.getStatus());
        assertEquals(claimIds.size(), job.getTotalClaims());
        assertEquals(claimIds.size(), job.getProcessedClaims());

        // Verify interactions
        verify(claimService).sendBulkClaimFiles(eq(claimIds), eq(billingBranchId));
        verify(bulkClaimJobService).findByJobId(jobId);
    }

    /**
     * Test bulk claim submission with a medium batch (50 claims).
     */
    @Test
    void testBulkClaimSubmission_MediumBatch() throws X12Exception {
        // Create 50 test claims
        List<Long> claimIds = createTestClaimIds(50);

        // Setup mocks
        String jobId = "test-job-id";
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("jobId", jobId);
        result.put("status", "PENDING");

        BulkClaimJob completedJob = new BulkClaimJob();
        completedJob.setJobId(jobId);
        completedJob.setStatus("COMPLETED");
        completedJob.setTotalClaims(claimIds.size());
        completedJob.setProcessedClaims(claimIds.size());
        completedJob.setSuccessfulClaims(claimIds.size());
        completedJob.setFailedClaims(0);
        completedJob.setCompleted(true);

        when(claimService.sendBulkClaimFiles(eq(claimIds), eq(billingBranchId))).thenReturn(result);
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(completedJob);

        // Measure performance
        Instant start = Instant.now();

        // Submit claims
        Map<String, Object> actualResult = claimService.sendBulkClaimFiles(claimIds, billingBranchId);

        // Get job ID
        String actualJobId = (String) actualResult.get("jobId");
        assertNotNull(actualJobId);

        // Simulate job completion
        BulkClaimJob job = bulkClaimJobService.findByJobId(actualJobId);

        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);

        // Log performance metrics
        System.out.println("Medium batch (50 claims) processing time: " + duration.toMillis() + " ms");

        // Verify job status
        assertEquals("COMPLETED", job.getStatus());
        assertEquals(claimIds.size(), job.getTotalClaims());
        assertEquals(claimIds.size(), job.getProcessedClaims());

        // Verify interactions
        verify(claimService).sendBulkClaimFiles(eq(claimIds), eq(billingBranchId));
        verify(bulkClaimJobService).findByJobId(jobId);
    }

    /**
     * Test bulk claim submission with a large batch (100 claims).
     */
    @Test
    void testBulkClaimSubmission_LargeBatch() throws X12Exception {
        // Create 100 test claims
        List<Long> claimIds = createTestClaimIds(100);

        // Setup mocks
        String jobId = "test-job-id";
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("jobId", jobId);
        result.put("status", "PENDING");

        BulkClaimJob completedJob = new BulkClaimJob();
        completedJob.setJobId(jobId);
        completedJob.setStatus("COMPLETED");
        completedJob.setTotalClaims(claimIds.size());
        completedJob.setProcessedClaims(claimIds.size());
        completedJob.setSuccessfulClaims(claimIds.size());
        completedJob.setFailedClaims(0);
        completedJob.setCompleted(true);

        when(claimService.sendBulkClaimFiles(eq(claimIds), eq(billingBranchId))).thenReturn(result);
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(completedJob);

        // Measure performance
        Instant start = Instant.now();

        // Submit claims
        Map<String, Object> actualResult = claimService.sendBulkClaimFiles(claimIds, billingBranchId);

        // Get job ID
        String actualJobId = (String) actualResult.get("jobId");
        assertNotNull(actualJobId);

        // Simulate job completion
        BulkClaimJob job = bulkClaimJobService.findByJobId(actualJobId);

        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);

        // Log performance metrics
        System.out.println("Large batch (100 claims) processing time: " + duration.toMillis() + " ms");

        // Verify job status
        assertEquals("COMPLETED", job.getStatus());
        assertEquals(claimIds.size(), job.getTotalClaims());
        assertEquals(claimIds.size(), job.getProcessedClaims());

        // Verify interactions
        verify(claimService).sendBulkClaimFiles(eq(claimIds), eq(billingBranchId));
        verify(bulkClaimJobService).findByJobId(jobId);
    }
}
