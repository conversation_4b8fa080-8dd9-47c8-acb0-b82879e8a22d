package com.nymbl.tenant.model;

import org.junit.jupiter.api.Test;


import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

class BulkClaimJobTest {

    @Test
    void testBulkClaimJob() {
        // Setup
        Long id = 1L;
        String jobId = "test-job-id";
        ZonedDateTime startTime = ZonedDateTime.now().minusHours(1);
        ZonedDateTime endTime = ZonedDateTime.now();
        String status = "COMPLETED";
        int totalClaims = 10;
        int processedClaims = 10;
        int successfulClaims = 8;
        int failedClaims = 2;
        String currentPayer = "Test Payer";
        String errorMessage = "Test error message";
        Boolean completed = true;

        // Execute
        BulkClaimJob job = new BulkClaimJob();
        job.setId(id);
        job.setJobId(jobId);
        job.setStartTime(startTime);
        job.setEndTime(endTime);
        job.setStatus(status);
        job.setTotalClaims(totalClaims);
        job.setProcessedClaims(processedClaims);
        job.setSuccessfulClaims(successfulClaims);
        job.setFailedClaims(failedClaims);
        job.setCurrentPayer(currentPayer);
        job.setErrorMessage(errorMessage);
        job.setCompleted(completed);

        // Verify
        assertEquals(id, job.getId());
        assertEquals(jobId, job.getJobId());
        assertEquals(startTime, job.getStartTime());
        assertEquals(endTime, job.getEndTime());
        assertEquals(status, job.getStatus());
        assertEquals(totalClaims, job.getTotalClaims());
        assertEquals(processedClaims, job.getProcessedClaims());
        assertEquals(successfulClaims, job.getSuccessfulClaims());
        assertEquals(failedClaims, job.getFailedClaims());
        assertEquals(currentPayer, job.getCurrentPayer());
        assertEquals(errorMessage, job.getErrorMessage());
        assertEquals(completed, job.getCompleted());
    }

    @Test
    void testBulkClaimJob_DefaultValues() {
        // Execute
        BulkClaimJob job = new BulkClaimJob();

        // Verify
        assertNull(job.getId());
        assertNull(job.getJobId());
        assertNull(job.getStartTime());
        assertNull(job.getEndTime());
        assertNull(job.getStatus());
        assertNull(job.getTotalClaims());
        assertNull(job.getProcessedClaims());
        assertNull(job.getSuccessfulClaims());
        assertNull(job.getFailedClaims());
        assertNull(job.getCurrentPayer());
        assertNull(job.getErrorMessage());
        assertFalse(job.getCompleted());
    }

    @Test
    void testBulkClaimJob_EqualsAndHashCode() {
        // Setup
        String jobId1 = "test-job-id-1";
        String jobId2 = "test-job-id-2";

        // Execute
        BulkClaimJob job1 = new BulkClaimJob();
        job1.setJobId(jobId1);

        BulkClaimJob job2 = new BulkClaimJob();
        job2.setJobId(jobId1);

        BulkClaimJob job3 = new BulkClaimJob();
        job3.setJobId(jobId2);

        // Verify
        assertEquals(job1, job2);
        assertEquals(job1.hashCode(), job2.hashCode());
        assertNotEquals(job1, job3);
        assertNotEquals(job1.hashCode(), job3.hashCode());
    }
}
