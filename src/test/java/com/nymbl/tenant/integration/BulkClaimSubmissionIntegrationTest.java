package com.nymbl.tenant.integration;

import com.nymbl.config.exception.X12Exception;
import com.nymbl.config.security.PrivilegeChecker;
import com.nymbl.tenant.controller.ClaimController;
import com.nymbl.tenant.dto.BulkClaimJobDTO;
import com.nymbl.tenant.dto.BulkClaimRequest;
import com.nymbl.tenant.dto.ClaimFilesRequest;
import com.nymbl.tenant.model.BulkClaimJob;
import com.nymbl.tenant.model.Claim;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.repository.ClaimRepository;
import com.nymbl.tenant.service.BulkClaimJobService;
import com.nymbl.tenant.service.ClaimService;
import com.nymbl.tenant.service.SystemSettingService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mock;

/**
 * Mock-based integration tests for the bulk claim submission workflow.
 * These tests verify the interaction between components without requiring a full Spring context.
 */
class BulkClaimSubmissionIntegrationTest {

    @Mock
    private ClaimService claimService;

    @Mock
    private BulkClaimJobService bulkClaimJobService;

    @Mock
    private ClaimRepository claimRepository;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private SystemSettingService systemSettingService;

    @Mock
    private PrivilegeChecker privilegeChecker;

    @InjectMocks
    private ClaimController claimController;

    private List<Long> claimIds;
    private Long billingBranchId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Initialize test data
        claimIds = Arrays.asList(1L, 2L);
        billingBranchId = 1L;

        // Mock system settings to enable bulk claims
        SystemSetting bulkClaimSetting = new SystemSetting();
        bulkClaimSetting.setValue("Y");
        when(systemSettingService.findBySectionAndField("billing", "enable_bulk_claims"))
            .thenReturn(bulkClaimSetting);

        // Mock privilege checker to allow bulk claim submission
        when(privilegeChecker.hasPrivilege("bulk_claim_submit")).thenReturn(true);
    }

    /**
     * Test the complete bulk claim submission workflow.
     */
    @Test
    void testBulkClaimSubmissionWorkflow() throws X12Exception {
        // Setup mocks
        // Step 1: Validate bulk submission
        List<String> emptyErrors = Collections.emptyList();
        when(claimService.validateBulkSubmission(claimIds)).thenReturn(emptyErrors);

        // Step 2: Send bulk claim files
        String jobId = "test-job-id";
        Map<String, Object> jobResult = new HashMap<>();
        jobResult.put("success", true);
        jobResult.put("jobId", jobId);
        jobResult.put("status", "PENDING");
        when(claimService.sendBulkClaimFiles(eq(claimIds), eq(billingBranchId))).thenReturn(jobResult);

        // Step 3: Job status
        BulkClaimJob completedJob = mock(BulkClaimJob.class);
        when(completedJob.getJobId()).thenReturn(jobId);
        when(completedJob.getStatus()).thenReturn("COMPLETED");
        when(completedJob.getTotalClaims()).thenReturn(claimIds.size());
        when(completedJob.getProcessedClaims()).thenReturn(claimIds.size());
        when(completedJob.getSuccessfulClaims()).thenReturn(claimIds.size());
        when(completedJob.getFailedClaims()).thenReturn(0);
        when(completedJob.getCompleted()).thenReturn(true);
        when(bulkClaimJobService.findByJobId(jobId)).thenReturn(completedJob);

        // Step 4: Claim updates
        Claim claim1 = mock(Claim.class);
        when(claim1.getId()).thenReturn(claimIds.get(0));
        when(claim1.getNymblStatusId()).thenReturn(21L);

        Claim claim2 = mock(Claim.class);
        when(claim2.getId()).thenReturn(claimIds.get(1));
        when(claim2.getNymblStatusId()).thenReturn(21L);

        when(claimRepository.findById(claimIds.get(0))).thenReturn(Optional.of(claim1));
        when(claimRepository.findById(claimIds.get(1))).thenReturn(Optional.of(claim2));

        // Execute
        // Step 1: Validate bulk submission
        BulkClaimRequest validateRequest = mock(BulkClaimRequest.class);
        when(validateRequest.getClaimIds()).thenReturn(claimIds);
        ResponseEntity<?> validateResponse = claimController.validateBulkSubmission(validateRequest);

        assertEquals(HttpStatus.OK, validateResponse.getStatusCode());
        @SuppressWarnings("unchecked")
        Map<String, Object> validateBody = (Map<String, Object>) validateResponse.getBody();
        assertNotNull(validateBody);
        assertTrue((Boolean) validateBody.get("valid"));

        // Step 2: Send bulk claim files
        ClaimFilesRequest submitRequest = mock(ClaimFilesRequest.class);
        when(submitRequest.getClaimIds()).thenReturn(claimIds);
        when(submitRequest.getBillingBranchId()).thenReturn(billingBranchId);
        when(submitRequest.getSubmissionType()).thenReturn("bulk");

        ResponseEntity<?> submitResponse = claimController.sendClaimFilesPost(submitRequest, httpServletRequest);

        assertEquals(HttpStatus.OK, submitResponse.getStatusCode());
        @SuppressWarnings("unchecked")
        Map<String, Object> submitBody = (Map<String, Object>) submitResponse.getBody();
        assertNotNull(submitBody);
        assertTrue((Boolean) submitBody.get("success"));
        assertEquals(jobId, submitBody.get("jobId"));

        // Step 3: Get job status
        ResponseEntity<?> statusResponse = claimController.getBulkJobStatus(jobId);
        assertEquals(HttpStatus.OK, statusResponse.getStatusCode());
        BulkClaimJobDTO jobDTO = (BulkClaimJobDTO) statusResponse.getBody();
        assertNotNull(jobDTO);
        assertEquals(jobId, jobDTO.getJobId());
        assertTrue(jobDTO.getCompleted());
        assertEquals("COMPLETED", jobDTO.getStatus());
        assertEquals(claimIds.size(), jobDTO.getTotalClaims());
        assertEquals(claimIds.size(), jobDTO.getProcessedClaims());

        // Step 4: Verify claims were updated
        for (Long claimId : claimIds) {
            Optional<Claim> optionalClaim = claimRepository.findById(claimId);
            assertTrue(optionalClaim.isPresent());
            Claim claim = optionalClaim.get();
            assertEquals(21L, claim.getNymblStatusId()); // Status should be updated to "Submitted"
        }

        // Verify interactions
        verify(claimService).validateBulkSubmission(claimIds);
        verify(claimService).sendBulkClaimFiles(eq(claimIds), eq(billingBranchId));
        verify(bulkClaimJobService).findByJobId(jobId);
        verify(claimRepository, times(2)).findById(anyLong());
    }

    /**
     * Test bulk claim submission with validation errors.
     */
    @Test
    void testBulkClaimSubmissionValidationErrors() {
        // Setup
        List<String> validationErrors = Collections.singletonList("Claims must have the same payer");
        when(claimService.validateBulkSubmission(claimIds)).thenReturn(validationErrors);

        // Execute
        BulkClaimRequest validateRequest = mock(BulkClaimRequest.class);
        when(validateRequest.getClaimIds()).thenReturn(claimIds);
        ResponseEntity<?> validateResponse = claimController.validateBulkSubmission(validateRequest);

        // Verify
        assertEquals(HttpStatus.OK, validateResponse.getStatusCode());
        @SuppressWarnings("unchecked")
        Map<String, Object> validateBody = (Map<String, Object>) validateResponse.getBody();
        assertNotNull(validateBody);
        assertFalse((Boolean) validateBody.get("valid"));
        assertEquals(validationErrors, validateBody.get("errors"));

        verify(claimService).validateBulkSubmission(claimIds);
    }
}
