package com.nymbl.tenant;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the TenantContext class.
 * These tests verify that tenant context is properly managed in different threads.
 */
class TenantContextTest {

    @AfterEach
    void tearDown() {
        // Clear tenant context after each test
        TenantContext.clear();
    }

    @Test
    void testSetAndGetCurrentTenant() {
        // Setup
        String expectedTenant = "test-tenant";

        // Execute
        TenantContext.setCurrentTenant(expectedTenant);
        String actualTenant = TenantContext.getCurrentTenant();

        // Verify
        assertEquals(expectedTenant, actualTenant);
    }

    @Test
    void testSetAndGetUserTimezoneId() {
        // Setup
        String expectedTimezone = "America/New_York";

        // Execute
        TenantContext.setUserTimezoneId(expectedTimezone);
        String actualTimezone = TenantContext.getUserTimezoneId();

        // Verify
        assertEquals(expectedTimezone, actualTimezone);
    }

    @Test
    void testClear() {
        // Setup
        TenantContext.setCurrentTenant("test-tenant");
        TenantContext.setUserTimezoneId("America/New_York");

        // Execute
        TenantContext.clear();

        // Verify
        assertEquals(TenantContext.DEFAULT_TENANT, TenantContext.getCurrentTenant());
        // Use the actual default value instead of the private constant
        assertEquals("America/New_York", TenantContext.getUserTimezoneId());
    }

    @Test
    void testDefaultValues() {
        // Verify
        assertEquals(TenantContext.DEFAULT_TENANT, TenantContext.getCurrentTenant());
        // Use the actual default value instead of the private constant
        assertEquals("America/New_York", TenantContext.getUserTimezoneId());
    }

    @Test
    void testThreadIsolation() throws InterruptedException {
        // Setup
        String mainThreadTenant = "main-thread-tenant";
        String mainThreadTimezone = "America/New_York";
        TenantContext.setCurrentTenant(mainThreadTenant);
        TenantContext.setUserTimezoneId(mainThreadTimezone);

        // Variables to capture values in the background thread
        final String[] backgroundThreadTenant = new String[1];
        final String[] backgroundThreadTimezone = new String[1];

        // Create a latch to wait for the background thread to complete
        CountDownLatch latch = new CountDownLatch(1);

        // Execute in a background thread
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            // Capture the initial values in the background thread
            backgroundThreadTenant[0] = TenantContext.getCurrentTenant();
            backgroundThreadTimezone[0] = TenantContext.getUserTimezoneId();

            // Set different values in the background thread
            TenantContext.setCurrentTenant("background-thread-tenant");
            TenantContext.setUserTimezoneId("Europe/London");

            latch.countDown();
        });

        // Wait for the background thread to complete
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Background thread did not complete in time");

        // Verify that the background thread initially had the default values, not the main thread values
        assertEquals(TenantContext.DEFAULT_TENANT, backgroundThreadTenant[0]);
        // Use the actual default value instead of the private constant
        assertEquals("America/New_York", backgroundThreadTimezone[0]);

        // Verify that the main thread values are still intact
        assertEquals(mainThreadTenant, TenantContext.getCurrentTenant());
        assertEquals(mainThreadTimezone, TenantContext.getUserTimezoneId());

        // Clean up
        executor.shutdown();
    }

    @Test
    void testMultipleThreads() throws InterruptedException {
        // Setup
        int threadCount = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // Execute in multiple threads
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.execute(() -> {
                try {
                    // Wait for all threads to be ready
                    startLatch.await();

                    // Set tenant context for this thread
                    String tenant = "tenant-" + threadIndex;
                    String timezone = "timezone-" + threadIndex;
                    TenantContext.setCurrentTenant(tenant);
                    TenantContext.setUserTimezoneId(timezone);

                    // Verify that the values are set correctly for this thread
                    assertEquals(tenant, TenantContext.getCurrentTenant());
                    assertEquals(timezone, TenantContext.getUserTimezoneId());

                    // Signal completion
                    completionLatch.countDown();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }

        // Start all threads
        startLatch.countDown();

        // Wait for all threads to complete
        assertTrue(completionLatch.await(10, TimeUnit.SECONDS), "Not all threads completed in time");

        // Clean up
        executor.shutdown();
    }
}
