-- START NYM-3275: Password History Tracking
-- Add index for password history tracking in user_audit if it doesn't exist
SELECT IF(
    NOT EXISTS(
        SELECT 1 FROM information_schema.STATISTICS
        WHERE TABLE_SCHEMA = 'nymbl_master'
        AND TABLE_NAME = 'user_audit'
        AND INDEX_NAME = 'idx_user_audit_password_history'
    ),
    'CREATE INDEX idx_user_audit_password_history ON nymbl_master.user_audit (id, password, revision_id)',
    'SELECT 1'
) INTO @sql;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add last password change date column to user table if it doesn't exist
SELECT IF(
    NOT EXISTS(
        SELECT 1 FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = 'nymbl_master'
        AND TABLE_NAME = 'user'
        AND COLUMN_NAME = 'last_password_change_date'
    ),
    'ALTER TABLE nymbl_master.user ADD COLUMN last_password_change_date TIMESTAMP NULL',
    'SELECT 1'
) INTO @sql;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add last password change date column to user_audit table if it doesn't exist
SELECT IF(
    NOT EXISTS(
        SELECT 1 FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = 'nymbl_master'
        AND TABLE_NAME = 'user_audit'
        AND COLUMN_NAME = 'last_password_change_date'
    ),
    'ALTER TABLE nymbl_master.user_audit ADD COLUMN last_password_change_date TIMESTAMP NULL',
    'SELECT 1'
) INTO @sql;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Initialize last_password_change_date for existing users to their last known password change or current timestamp
UPDATE nymbl_master.user SET last_password_change_date = CURRENT_TIMESTAMP WHERE last_password_change_date IS NULL;

-- Initialize audit records for existing users to make password history work
-- First, insert a record into the audit_revision table
SET @new_revision = (SELECT COALESCE(MAX(revision_id), 0) + 1 FROM nymbl_master.audit_revision);

-- Only create a new revision if there are users who need password history entries
SET @needs_revision = (SELECT COUNT(*) FROM nymbl_master.user u
                      WHERE NOT EXISTS (
                          SELECT 1 FROM nymbl_master.user_audit ua
                          WHERE ua.id = u.id AND ua.password IS NOT NULL
                      ));

-- Create new revision
INSERT INTO nymbl_master.audit_revision (revision_id, rev_timestamp, user)
SELECT @new_revision, UNIX_TIMESTAMP() * 1000, 'system_migration'
WHERE @needs_revision > 0;

-- Create a temporary table to identify users who don't have password history
CREATE TEMPORARY TABLE IF NOT EXISTS nymbl_master.users_needing_history AS
SELECT
    u.id,
    u.username,
    u.password,
    u.email,
    u.first_name,
    u.last_name,
    u.created_at,
    u.active
FROM
    nymbl_master.user u
WHERE
    NOT EXISTS (
        SELECT 1 FROM nymbl_master.user_audit ua
        WHERE ua.id = u.id AND ua.password IS NOT NULL
    )
    AND u.password IS NOT NULL;  -- Only include users who have passwords

-- Insert audit records for users who need them
INSERT INTO nymbl_master.user_audit (id, revision_id, revision_type, username, password, email, first_name, last_name, created_at, active)
SELECT
    id,
    @new_revision,
    0, -- 0 is for ADD operation
    username,
    password,
    email,
    first_name,
    last_name,
    created_at,
    active
FROM
    nymbl_master.users_needing_history;

-- Drop the temporary table
DROP TEMPORARY TABLE IF EXISTS nymbl_master.users_needing_history;
-- END NYM-3275