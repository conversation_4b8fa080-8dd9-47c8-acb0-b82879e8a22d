-- =============================================
-- Bulk Claim Feature - DOWN Migration
-- =============================================

-- Drop the audit table first (due to foreign key constraint)
DROP TABLE IF EXISTS bulk_claim_job_audit;

-- Drop the main table
DROP TABLE IF EXISTS bulk_claim_job;

DELIMITER //

CREATE PROCEDURE drop_column_if_exists(
    IN p_table VARCHAR(100),
    IN p_column VARCHAR(100)
)
BEGIN
    SET @table = p_table;
    SET @column = p_column;

    SET @check_sql = CONCAT(
        'SELECT COUNT(*) INTO @exists FROM information_schema.columns ',
        'WHERE table_schema = DATABASE() ',
        'AND table_name = "', @table, '" ',
        'AND column_name = "', @column, '"'
    );

    PREPARE check_stmt FROM @check_sql;
    EXECUTE check_stmt;
    DEALLOCATE PREPARE check_stmt;

    IF @exists > 0 THEN
        SET @drop_column = CONCAT(
            'ALTER TABLE ', @table,
            ' DROP COLUMN ', @column
        );
        PREPARE drop_stmt FROM @drop_column;
        EXECUTE drop_stmt;
        DEALLOCATE PREPARE drop_stmt;
    END IF;
END //

CREATE PROCEDURE remove_model_stub_columns(IN p_table VARCHAR(100))
BEGIN
    CALL drop_column_if_exists(p_table, 'version');
    CALL drop_column_if_exists(p_table, 'updated_at');
    CALL drop_column_if_exists(p_table, 'created_at');
END //

DELIMITER ;

-- Process main tables
CALL remove_model_stub_columns('bulk_claim_job');
CALL remove_model_stub_columns('claim_file');
CALL remove_model_stub_columns('claim_submission');
CALL remove_model_stub_columns('claim');

-- Remove bulk claim columns from claim table
CALL drop_column_if_exists('claim', 'bulk_claim_job_id');
CALL drop_column_if_exists('claim', 'bulk_submission');

-- Process audit tables
CALL remove_model_stub_columns('bulk_claim_job_audit');
CALL remove_model_stub_columns('claim_file_audit');
CALL remove_model_stub_columns('claim_submission_audit');
CALL remove_model_stub_columns('claim_audit');

-- Remove bulk claim columns from claim_audit table
CALL drop_column_if_exists('claim_audit', 'bulk_claim_job_id');
CALL drop_column_if_exists('claim_audit', 'bulk_submission');

-- Remove x12 file columns from bulk_claim_job_audit table
CALL drop_column_if_exists('bulk_claim_job_audit', 'x12_file_path');
CALL drop_column_if_exists('bulk_claim_job_audit', 'x12_file_content');

-- Remove the "enable_bulk_claims" system setting
DELETE FROM system_setting WHERE field = 'enable_bulk_claims' AND section = 'billing';

-- Remove the bulk_claim_submit privilege
DELETE FROM `nymbl_master`.`privilege` WHERE name = 'bulk_claim_submit';

-- Cleanup procedures
DROP PROCEDURE IF EXISTS remove_model_stub_columns;
DROP PROCEDURE IF EXISTS drop_column_if_exists;

